---
type: "always_apply"
---

## 0. Environment
- Always keep in mind that we're running on a testing / staging server, never run "npm run dev" or other services; all services should be running already. if in doubt, ask.
- never do "php artisan migrate:fresh"
- never do "db wipe"
- our staging server is available at https://crm.concept-42.com/ - when attempting to access it, base your routes on this.
- when looking for a solution, always check the documentation first, and attempt to apply it
- when working with a package based on another package, don't assume the parent package requires/behaves in a certain way and work based on an assumption. always try to check what the parent package expects/behaves like


## 1. Stack & Versions
- **Laravel**: 12.x (PHP ≥ 8.2, supports up to 8.4)  
- **Filament**: 4.x (Schema-based architecture, major shift from v3)  
- **Livewire**: 3.x (default namespace `App\Livewire`)  
- **Blade**: use anonymous components, layouts in `resources/views/components/layouts`  
- **DB**: MariaDB or PostgreSQL (Eloquent ORM + migrations)  

---

## 2. <PERSON>vel 12 Rules
- **Typed Routes**: Always declare parameter types.
  ```php
  #[Route('/users/{id}')]
  public function show(int $id) { ... }
  ```
- **Dependency Injection**: Use PHP 8 attributes (e.g. `#[Give]`) for contextual binding.  
- **Queries**: Use `reorderDesc()`, cursor pagination for large datasets.  
- **Collections**: Use `withHeartbeat()` for long-running processes.  
- **String Helpers**: Prefer `Str::encrypt()` / `Str::decrypt()` over manual Crypt facade.  

---

## 3. Filament 4 Rules
- **Unified Schema Core**: Forms, tables, widgets, and info lists use the same Schema.  
- **Resource Structure**:  
  ```
  app/Filament/Resources/Users/
  ├── UserResource.php
  ├── Schemas/UserForm.php
  ├── Schemas/UserTable.php
  └── Pages/{CreateUser, EditUser}.php
  ```
- **Actions**: Use unified `Action::make()` across forms, tables, pages.  
- **Page Layouts**: Define with PHP `schema()` instead of Blade overrides.  
- **DB-backed Tables**: Always back Filament `Table` with Eloquent models; avoid static/API-only tables.  
- **Performance**: Trust partial rendering (modals don’t re-render tables). Avoid N+1 queries — eager load in resources.  

---

## 4. Livewire 3 Rules
- **Namespace & Layout**:  
  - Classes under `App\Livewire`  
  - Default layout: `components.layouts.app`  
- **Validation**: Use attributes:
  ```php
  #[Validate('required|min:3')]
  public string $name;
  ```
- **Real-time Updates**: Use `validateOnly()` in property update hooks.  
- **Frontend**: Use Alpine.js for simple interactivity; mark elements with `wire:ignore` when Alpine manages DOM.  

---

## 5. Blade Rules
- Use **anonymous components** for layouts & UI widgets.  
- Organize layouts in `resources/views/components/layouts/`.  
- Use modern directives: `@datetime`, `@json`, `@verbatim`.  

---

## 6. Code Generation Commands
- **Laravel**:
  ```bash
  php artisan make:job ProcessData --batched
  php artisan queue:work --verbose
  ```
- **Filament**:
  ```bash
  php artisan make:filament-resource User --generate
  php artisan make:filament-page Dashboard
  php artisan make:filament-widget StatsOverview
  ```

---

## 7. Performance Rules
- Use **cursor pagination** for large datasets.  
- Always **eager load** relations in Filament tables to prevent N+1.  
- Cache heavy queries when possible.  
- Use `php artisan config:cache && php artisan route:cache && php artisan view:cache` in production.  

---

## 8. Security Rules
- Enforce strict typing in routes/controllers.  
- Use Laravel validation for all inputs.  
- In Filament, enable built-in **2FA** and **email change verification**.  
- Sanitize rich text input if exposing TipTap editor to users.  
- Keep packages updated (Laravel 12 security until Feb 2027).  

---

✅ **Key principle**: Generate code using **Laravel 12 syntax**, **Filament 4 Schema structure**, and **Livewire 3 validation attributes**. Avoid deprecated Filament v3 structures, static tables, and untyped routes.  
