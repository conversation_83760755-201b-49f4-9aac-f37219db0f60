<?php

use Illuminate\Support\Facades\Route;
use Livewire\Volt\Volt;

Route::get('/', function () {
    return redirect('/app');
})->name('home');

// Public test routes (no auth required)
Route::get('test-reverb', function () {
    return view('test-reverb');
})->name('test.reverb');

Route::get('simple-test', function () {
    return response()->json(['message' => 'Simple route works!', 'time' => now()]);
});

Route::get('test-echo-config', function () {
    return view('test-echo');
});

Route::get('reverb-status', function () {
    try {
        // Test multiple endpoints
        $tests = [];

        // Test basic connection
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'http://**********:8081');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 5);
        curl_setopt($ch, CURLOPT_NOBODY, true);
        $result = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        $tests['basic'] = ['http_code' => $httpCode, 'success' => $httpCode > 0];

        // Test apps endpoint (what Laravel tries to use)
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'http://**********:8081/apps/355121/events');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 5);
        curl_setopt($ch, CURLOPT_NOBODY, true);
        $result = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        $tests['apps_endpoint'] = ['http_code' => $httpCode, 'success' => $httpCode > 0];

        return response()->json([
            'tests' => $tests,
            'broadcast_driver' => config('broadcasting.default'),
            'reverb_config' => [
                'host' => config('broadcasting.connections.reverb.options.host'),
                'port' => config('broadcasting.connections.reverb.options.port'),
                'scheme' => config('broadcasting.connections.reverb.options.scheme'),
            ]
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
            'reverb_running' => false
        ]);
    }
});

Route::get('test-notification', function () {
    try {
        // Use Laravel's broadcasting system instead of manual Pusher client
        $event = new \App\Events\TestNotification('Laravel Broadcasting Test!', 'success');
        broadcast($event);

        return response()->json([
            'success' => true,
            'message' => 'Laravel broadcast sent successfully!'
        ]);
    } catch (\Exception $e) {
        \Log::error('Broadcasting error: ' . $e->getMessage(), [
            'trace' => $e->getTraceAsString()
        ]);

        return response()->json([
            'success' => false,
            'message' => 'Error: ' . $e->getMessage(),
            'file' => $e->getFile() . ':' . $e->getLine()
        ], 500);
    }
})->name('test.notification');

Route::view('dashboard', 'dashboard')
    ->middleware(['auth', 'verified'])
    ->name('dashboard');

Route::middleware(['auth'])->group(function () {
    Route::redirect('settings', 'settings/profile');

    Volt::route('settings/profile', 'settings.profile')->name('settings.profile');
    Volt::route('settings/password', 'settings.password')->name('settings.password');
    Volt::route('settings/appearance', 'settings.appearance')->name('settings.appearance');

    // File management routes
    Route::get('files/{uuid}/download', [App\Http\Controllers\FileController::class, 'download'])->name('files.download');
    Route::get('files/{uuid}/stream', [App\Http\Controllers\FileController::class, 'stream'])->name('files.stream');
    Route::delete('files/{uuid}', [App\Http\Controllers\FileController::class, 'delete'])->name('files.delete');

    // Document management routes
    Route::get('documents/{uuid}/download', function ($uuid) {
        $document = \App\Models\GeneratedDocument::where('uuid', $uuid)->firstOrFail();
        
        // Check if user has access to this document
        if (!auth()->user()->can('view', $document)) {
            abort(403, 'Unauthorized access to document');
        }
        
        if (!$document->isPdfGenerated()) {
            abort(404, 'PDF not generated for this document');
        }
        
        if (!\Storage::disk('local')->exists($document->pdf_path)) {
            abort(404, 'PDF file not found on disk');
        }
        
        // Sanitize filename by removing invalid characters
        $sanitizedFilename = preg_replace('/[\/\\\\:*?"<>|]/', '_', $document->title) . '.pdf';
        
        return \Storage::disk('local')->download($document->pdf_path, $sanitizedFilename);
    })->name('documents.download');
    
    Route::get('documents/{uuid}/preview', function ($uuid) {
        $document = \App\Models\GeneratedDocument::where('uuid', $uuid)->firstOrFail();
        
        // Check if user has access to this document
        if (!auth()->user()->can('view', $document)) {
            abort(403, 'Unauthorized access to document');
        }
        
        if (!$document->isPdfGenerated()) {
            abort(404, 'PDF not generated for this document');
        }
        
        if (!\Storage::disk('local')->exists($document->pdf_path)) {
            abort(404, 'PDF file not found on disk');
        }
        
        // Sanitize filename by removing invalid characters
        $sanitizedFilename = preg_replace('/[\/\\\\:*?"<>|]/', '_', $document->title) . '.pdf';
        
        return response()->file(\Storage::disk('local')->path($document->pdf_path), [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'inline; filename="' . $sanitizedFilename . '"',
        ]);
    })->name('documents.preview');

    // Notification testing routes
    Route::get('test-notifications', function () {
        $user = auth()->user();
        $debug = [];

        try {
            $debug['user'] = [
                'id' => $user->id,
                'name' => $user->name,
                'has_notifiable_trait' => in_array('Illuminate\Notifications\Notifiable', class_uses_recursive($user)),
            ];

            // Test 1: Direct Filament notification
            $debug['step1'] = 'Creating notification...';
            $notification = \Filament\Notifications\Notification::make()
                ->title('Direct Test')
                ->body('Testing at ' . now()->format('H:i:s'))
                ->success();

            $debug['step2'] = 'Notification object created';

            // Send to database
            $debug['step3'] = 'Sending to database...';
            $notification->sendToDatabase($user);

            $debug['step4'] = 'Sent to database';

            // Check count
            $count = $user->notifications()->count();
            $debug['notification_count'] = $count;

            // Get latest notification
            $latest = $user->notifications()->latest()->first();
            $debug['latest_notification'] = $latest ? [
                'id' => $latest->id,
                'type' => $latest->type,
                'data' => $latest->data,
                'read_at' => $latest->read_at,
            ] : null;

            return response()->json([
                'success' => true,
                'debug' => $debug,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'file' => $e->getFile() . ':' . $e->getLine(),
                'trace' => $e->getTraceAsString(),
                'debug' => $debug,
            ], 500);
        }
    })->name('test.notifications');

    Route::get('process-queue', function () {
        try {
            // Check pending jobs before
            $jobsBefore = \DB::table('jobs')->count();

            // Process all pending jobs (not just one)
            \Illuminate\Support\Facades\Artisan::call('queue:work', [
                '--stop-when-empty' => true,
                '--max-jobs' => 100,
            ]);

            $output = \Illuminate\Support\Facades\Artisan::output();

            // Check jobs after
            $jobsAfter = \DB::table('jobs')->count();

            $user = auth()->user();
            $count = $user->notifications()->count();

            return response()->json([
                'success' => true,
                'message' => 'Queue processed',
                'jobs_before' => $jobsBefore,
                'jobs_after' => $jobsAfter,
                'jobs_processed' => $jobsBefore - $jobsAfter,
                'artisan_output' => $output,
                'notification_count' => $count,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
            ], 500);
        }
    })->name('process.queue');

    Route::get('test-notification-complete', function () {
        $user = auth()->user();

        try {
            // Send notification
            \App\Services\NotificationService::send(
                recipient: $user,
                title: 'Complete Test',
                body: 'This notification should appear as toast AND in database at ' . now()->format('H:i:s'),
                icon: 'heroicon-o-sparkles',
                iconColor: 'success'
            );

            // Process the queue immediately
            \Illuminate\Support\Facades\Artisan::call('queue:work', [
                '--stop-when-empty' => true,
                '--max-jobs' => 10,
            ]);

            $count = $user->notifications()->count();

            return response()->json([
                'success' => true,
                'message' => 'Notification sent and queue processed!',
                'notification_count' => $count,
                'instructions' => 'Check the Filament UI - you should see a toast notification AND the bell icon should update',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 500);
        }
    })->name('test.notification-complete');

    Route::get('test-sync-notification', function () {
        $user = auth()->user();

        try {
            \Log::info('Before sending notification');

            // Send both database AND broadcast
            $notification = \Filament\Notifications\Notification::make()
                ->title('Full Test - ' . now()->format('H:i:s'))
                ->body('This should appear in database AND as toast!')
                ->icon('heroicon-o-bolt')
                ->iconColor('success');

            \Log::info('Notification created');

            // Send to database
            $notification->sendToDatabase($user, isEventDispatched: true);
            \Log::info('Sent to database');

            // Send broadcast
            $notification->broadcast($user);
            \Log::info('Broadcast called');

            // Check jobs table
            $pendingJobs = \DB::table('jobs')->count();
            \Log::info('Pending jobs: ' . $pendingJobs);

            return response()->json([
                'success' => true,
                'message' => 'Notification sent!',
                'pending_jobs' => $pendingJobs,
                'user_channel' => 'App.Models.User.' . $user->id,
                'instructions' => 'Check logs and console',
            ]);
        } catch (\Exception $e) {
            \Log::error('Error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 500);
        }
    })->name('test.sync-notification');

    Route::get('test-direct-broadcast', function () {
        $user = auth()->user();

        try {
            // Send ONLY broadcast (not database)
            $notification = \Filament\Notifications\Notification::make()
                ->title('Direct Broadcast Test')
                ->body('Testing broadcast only at ' . now()->format('H:i:s'))
                ->success();

            // Only broadcast, no database
            $notification->broadcast($user);

            return response()->json([
                'success' => true,
                'message' => 'Broadcast sent (no database)',
                'channel' => 'App.Models.User.' . $user->id,
                'instructions' => [
                    '1. Open browser console (F12)',
                    '2. Look for Echo messages',
                    '3. Should see notification on channel: App.Models.User.' . $user->id,
                    '4. Toast should appear if Echo is connected',
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
            ], 500);
        }
    })->name('test.direct-broadcast');

    Route::get('debug-echo-config', function () {
        return response()->json([
            'filament_config' => config('filament.broadcasting.echo'),
            'env_vars' => [
                'VITE_REVERB_HOST' => env('VITE_REVERB_HOST'),
                'VITE_REVERB_PORT' => env('VITE_REVERB_PORT'),
                'VITE_REVERB_PATH' => env('VITE_REVERB_PATH'),
                'VITE_REVERB_SCHEME' => env('VITE_REVERB_SCHEME'),
            ],
        ]);
    })->name('debug.echo-config');

    Route::get('test-toast-only', function () {
        // Send a toast notification using session (not broadcast)
        \Filament\Notifications\Notification::make()
            ->title('Toast Test')
            ->body('This should appear as a toast!')
            ->success()
            ->send();

        return redirect('/app');
    })->name('test.toast-only');

    Route::get('test-notification-types', function () {
        $user = auth()->user();

        // Success notification
        \Filament\Notifications\Notification::make()
            ->title('Success!')
            ->body('This is a success notification')
            ->success()
            ->sendToDatabase($user, isEventDispatched: true)
            ->broadcast($user);

        // Warning notification
        \Filament\Notifications\Notification::make()
            ->title('Warning!')
            ->body('This is a warning notification')
            ->warning()
            ->sendToDatabase($user, isEventDispatched: true)
            ->broadcast($user);

        // Danger notification
        \Filament\Notifications\Notification::make()
            ->title('Error!')
            ->body('This is a danger notification')
            ->danger()
            ->sendToDatabase($user, isEventDispatched: true)
            ->broadcast($user);

        // Info notification
        \Filament\Notifications\Notification::make()
            ->title('Info')
            ->body('This is an info notification')
            ->info()
            ->sendToDatabase($user, isEventDispatched: true)
            ->broadcast($user);

        return response()->json([
            'success' => true,
            'message' => '4 different notification types sent!',
        ]);
    })->name('test.notification-types');
    Route::post('files/upload', [App\Http\Controllers\FileController::class, 'upload'])->name('files.upload');


});

require __DIR__.'/auth.php';


// Notification test routes
Route::middleware(['web', 'auth'])->group(function () {

    // Echo connection status page
    Route::get('test/notification/echo-status', function () {
        return view('test-echo-status');
    })->name('test.notification.echo-status');

    // Database-only notification (no broadcast)
    Route::get('test/notification/database-only', function () {
        $userId = request('user_id', auth()->id());
        $user = \App\Models\User::find($userId);

        if (!$user) {
            return response()->json(['success' => false, 'message' => 'User not found'], 404);
        }

        Filament\Notifications\Notification::make()
            ->title('Database Only Test')
            ->body('This should appear in the bell icon after 30 seconds (polling)')
            ->success()
            ->sendToDatabase($user);

        return response()->json([
            'success' => true,
            'message' => "Database notification sent to {$user->name}",
        ]);
    })->name('test.notification.database-only');

    // Broadcast-only notification (no database)
    Route::get('test/notification/broadcast-only', function () {
        $userId = request('user_id', auth()->id());
        $user = \App\Models\User::find($userId);

        if (!$user) {
            return response()->json(['success' => false, 'message' => 'User not found'], 404);
        }

        Filament\Notifications\Notification::make()
            ->title('Broadcast Only Test')
            ->body('This should appear as a toast immediately')
            ->info()
            ->broadcast($user);

        return response()->json([
            'success' => true,
            'message' => "Broadcast notification sent to {$user->name}",
        ]);
    })->name('test.notification.broadcast-only');

    // BOTH database AND broadcast (CORRECT WAY)
    Route::get('test/notification/both', function () {
        $userId = request('user_id', auth()->id());
        $user = \App\Models\User::find($userId);

        if (!$user) {
            return response()->json(['success' => false, 'message' => 'User not found'], 404);
        }

        $notification = Filament\Notifications\Notification::make()
            ->title('Complete Test - ' . now()->format('H:i:s'))
            ->body('This should appear as toast AND in bell icon')
            ->success();

        $notification->sendToDatabase($user, isEventDispatched: true);
        $notification->broadcast($user);

        return response()->json([
            'success' => true,
            'message' => "Both database and broadcast sent to {$user->name}",
        ]);
    })->name('test.notification.both');

    // Multiple notification types
    Route::get('test/notification/types', function () {
        $userId = request('user_id', auth()->id());
        $user = \App\Models\User::find($userId);

        if (!$user) {
            return response()->json(['success' => false, 'message' => 'User not found'], 404);
        }

        // Success
        $notif = Filament\Notifications\Notification::make()
            ->title('Success Notification')
            ->body('This is a success message')
            ->success();
        $notif->sendToDatabase($user, isEventDispatched: true);
        $notif->broadcast($user);

        // Warning
        $notif = Filament\Notifications\Notification::make()
            ->title('Warning Notification')
            ->body('This is a warning message')
            ->warning();
        $notif->sendToDatabase($user, isEventDispatched: true);
        $notif->broadcast($user);

        // Danger
        $notif = Filament\Notifications\Notification::make()
            ->title('Danger Notification')
            ->body('This is a danger message')
            ->danger();
        $notif->sendToDatabase($user, isEventDispatched: true);
        $notif->broadcast($user);

        // Info
        $notif = Filament\Notifications\Notification::make()
            ->title('Info Notification')
            ->body('This is an info message')
            ->info();
        $notif->sendToDatabase($user, isEventDispatched: true);
        $notif->broadcast($user);

        return response()->json([
            'success' => true,
            'message' => "4 notifications sent to {$user->name} (success, warning, danger, info)",
        ]);
    })->name('test.notification.types');

    // Persistent notification (won't auto-dismiss)
    Route::get('test/notification/persistent', function () {
        $userId = request('user_id', auth()->id());
        $user = \App\Models\User::find($userId);

        if (!$user) {
            return response()->json(['success' => false, 'message' => 'User not found'], 404);
        }

        $notif = Filament\Notifications\Notification::make()
            ->title('Persistent Notification')
            ->body('This notification will not auto-dismiss')
            ->warning()
            ->persistent();

        $notif->sendToDatabase($user, isEventDispatched: true);
        $notif->broadcast($user);

        return response()->json([
            'success' => true,
            'message' => "Persistent notification sent to {$user->name}",
        ]);
    })->name('test.notification.persistent');
});
