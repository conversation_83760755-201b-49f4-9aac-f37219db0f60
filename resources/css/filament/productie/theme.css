@import '../../../../vendor/filament/filament/resources/css/theme.css';

@source '../../../../app/Filament/Productie/**/*';
@source '../../../../resources/views/filament/productie/**/*';

/* Global Tighter Design - Smaller Fonts & Reduced Padding */

/* Base font size reduction (-2pts from default) */
html {
    font-size: 14px !important; /* Reduced from default 16px */
}

/* Button styling - smaller padding and font */
.fi-btn {
    @apply !px-2 !py-1.5 !text-xs !font-medium; /* Reduced from px-3 py-2 text-sm */
}

/* Form field labels - smaller font */
.fi-fo-field-wrp-label {
    @apply !text-xs !font-medium; /* Reduced from text-sm */
}

/* Form inputs - smaller padding and font */
.fi-input {
    @apply !px-2 !py-1.5 !text-xs; /* Reduced padding and font size */
}

/* Select inputs */
.fi-select-input {
    @apply !px-2 !py-1.5 !text-xs;
}

/* Textarea inputs */
.fi-textarea {
    @apply !px-2 !py-1.5 !text-xs;
}

/* Table headers - smaller font and padding */
.fi-ta-header-cell {
    @apply !px-2 !py-1 !text-xs !font-medium; /* Further reduced vertical padding from py-1.5 to py-1 */
}

/* Table cells - smaller padding and font */
.fi-ta-cell {
    @apply !px-2 !py-1 !text-xs; /* Further reduced vertical padding from py-1.5 to py-1 */
}

/* Table text items */
.fi-ta-text-item-label {
    @apply !text-xs; /* Reduced from text-sm */
}

/* Navigation items - smaller font and padding */
.fi-sidebar-nav-item {
    @apply !px-2 !py-1.5 !text-xs; /* Reduced padding and font */
}

/* Navigation labels */
.fi-sidebar-nav-item-label {
    @apply !text-xs !font-medium;
}

/* Page headings - proportionally smaller */
.fi-header-heading {
    @apply !text-lg !font-semibold; /* Reduced from text-xl */
}

/* Section headings */
.fi-section-header-heading {
    @apply !text-sm !font-semibold; /* Reduced from text-base */
}

/* Modal headers */
.fi-modal-header-heading {
    @apply !text-base !font-semibold; /* Reduced from text-lg */
}

/* Form sections - tighter spacing */
.fi-fo-section {
    @apply !space-y-3; /* Reduced from space-y-4 */
}

.fi-fo-section-content {
    @apply !p-3; /* Reduced from p-4 */
}

/* Form field wrapper - tighter spacing */
.fi-fo-field-wrp {
    @apply !space-y-1; /* Reduced from space-y-2 */
}

/* Additional vertical spacing reductions - from factor x8 to x6 */
.fi-section-header {
    @apply !py-1.5; /* Reduced section header vertical padding */
}

.fi-section-content {
    @apply !gap-1.5; /* Reduced content row gap from default (likely gap-2) */
}

/* Table row spacing - further tightening */
.fi-ta-row {
    @apply !py-0.5; /* Minimal vertical padding for table rows */
}

/* Table content wrapper - tighter spacing */
.fi-ta-content {
    @apply !space-y-1; /* Reduced spacing between table elements */
}

/* Help text - smaller font */
.fi-fo-field-wrp-hint {
    @apply !text-xs; /* Reduced from text-sm */
}

/* Error messages - smaller font */
.fi-fo-field-wrp-error-message {
    @apply !text-xs; /* Reduced from text-sm */
}

/* Badges - smaller padding and font */
.fi-badge {
    @apply !px-1.5 !py-0.5 !text-xs; /* Reduced from px-2 py-1 text-sm */
}

/* Action buttons in tables */
.fi-ta-actions {
    @apply !space-x-1; /* Reduced from space-x-2 */
}

/* Dropdown items - smaller padding */
.fi-dropdown-list-item {
    @apply !px-2 !py-1 !text-xs; /* Reduced from px-3 py-2 text-sm */
}

/* Notifications - smaller padding and font */
.fi-no-notification {
    @apply !p-3 !text-xs; /* Reduced from p-4 text-sm */
}

/* Widget cards - tighter padding */
.fi-wi-stats-card {
    @apply !p-3; /* Reduced from p-4 */
}

.fi-wi-stats-card-value {
    @apply !text-lg !font-semibold; /* Reduced from text-xl */
}

.fi-wi-stats-card-label {
    @apply !text-xs; /* Reduced from text-sm */
}

/* Pagination - smaller elements */
.fi-pagination {
    @apply !space-x-1; /* Reduced spacing */
}

.fi-pagination-item {
    @apply !px-2 !py-1 !text-xs; /* Reduced from px-3 py-2 text-sm */
}

/* Tabs - smaller padding */
.fi-tabs-tab {
    @apply !px-2 !py-1.5 !text-xs !font-medium; /* Reduced from px-3 py-2 text-sm */
}

/* Breadcrumbs - smaller font */
.fi-breadcrumbs-item {
    @apply !text-xs; /* Reduced from text-sm */
}

/* Override Filament CSS custom properties for max width */
:root {
    --fi-content-max-width: none !important;
    --fi-page-max-width: none !important;
    --fi-section-max-width: none !important;
}

/* Workspace Width Maximization and Sidebar Customization */
/* DON'T override sidebar width - let Filament handle it */

/* Remove the fixed margin-left - this was the problem! */
.fi-main {
    /* margin-left: auto !important; */  /* Let Filament handle this */
}

.fi-main-content {
    max-width: none !important; /* Remove max-width constraint */
    width: 100% !important; /* Use full available width */
}

.fi-page {
    max-width: none !important; /* Remove page max-width constraint */
    width: 100% !important;
}

.fi-section-content-ctn {
    max-width: none !important; /* Remove section content max-width */
    width: 100% !important;
}

/* Additional content width overrides */
.fi-main-ctn,
.fi-page-content,
.fi-section,
.fi-section-content {
    max-width: none !important;
    width: 100% !important;
}

/* Table and form maximization */
.fi-ta {
    max-width: none !important;
    width: 100% !important;
}

.fi-fo {
    max-width: none !important;
}

/* Table container full width */
.fi-ta-ctn {
    max-width: none !important;
    width: 100% !important;
}

/* Table header and body full width */
.fi-ta-header,
.fi-ta-content {
    width: 100% !important;
}

/* Override any max-width constraints on tables */
.fi-ta table,
.fi-ta-table {
    width: 100% !important;
    max-width: none !important;
}

/* Ensure table wrapper uses full width */
.fi-ta-wrapper {
    width: 100% !important;
    max-width: none !important;
}

/* Widget full width */
.fi-wi {
    max-width: none !important;
    width: 100% !important;
}

/* Widget wrapper */
.fi-wi-wrapper {
    max-width: none !important;
    width: 100% !important;
}

/* Page wrapper and container overrides */
.fi-page-wrapper,
.fi-page-container {
    max-width: none !important;
    width: 100% !important;
}

/* Livewire component wrappers */
[wire\\:id] {
    max-width: none !important;
    width: 100% !important;
}

/* Generic container overrides */
.container,
.mx-auto {
    max-width: none !important;
}

/* Remove any horizontal padding that might constrain content */
.fi-page,
.fi-page-content {
    padding-left: 0 !important;
    padding-right: 0 !important;
}

/* Make sure grid uses full width */
.grid {
    width: 100% !important;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
    /* Let Filament handle responsive sidebar */
}
