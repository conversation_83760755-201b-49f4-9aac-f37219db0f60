@import '../../../../vendor/filament/filament/resources/css/theme.css';

@source '../../../../app/Filament/App/**/*';
@source '../../../../resources/views/filament/app/**/*';

/* Guava Calendar CSS */
@source '../../../../vendor/guava/calendar/resources/**/*';
@import '../../../../vendor/guava/calendar/resources/css/theme.css';



/* Override Filament CSS custom properties for max width */
:root {
    --fi-content-max-width: none !important;
    --fi-page-max-width: none !important;
    --fi-section-max-width: none !important;
}

/* Global Tighter Design - Smaller Fonts & Reduced Padding */

/* Base font size reduction (-2pts from default) */
html {
    font-size: 14px !important; /* Reduced from default 16px */
}

/* Button styling - smaller padding and font */
.fi-btn {
    @apply !px-2 !py-1.5 !text-xs !font-medium; /* Reduced from px-3 py-2 text-sm */
}

/* Form field labels - smaller font */
.fi-fo-field-wrp-label {
    @apply !text-xs !font-medium; /* Reduced from text-sm */
}

/* Form inputs - smaller padding and font */
.fi-input {
    @apply !px-2 !py-1.5 !text-xs; /* Reduced padding and font size */
}

/* Select inputs */
.fi-select-input {
    @apply !px-2 !py-1.5 !text-xs;
}

/* Textarea inputs */
.fi-textarea {
    @apply !px-2 !py-1.5 !text-xs;
}

/* Table headers - smaller font and padding */
.fi-ta-header-cell {
    @apply !px-2 !py-1 !text-xs !font-medium; /* Further reduced vertical padding from py-1.5 to py-1 */
}

/* Table cells - smaller padding and font */
.fi-ta-cell {
    @apply !px-2 !py-1 !text-xs; /* Further reduced vertical padding from py-1.5 to py-1 */
}

/* Table text items */
.fi-ta-text-item-label {
    @apply !text-xs; /* Reduced from text-sm */
}

/* Navigation items - smaller font and padding */
.fi-sidebar-nav-item {
    @apply !px-2 !py-1.5 !text-xs; /* Reduced padding and font */
}

/* Navigation labels */
.fi-sidebar-nav-item-label {
    @apply !text-xs !font-medium;
}

/* Page headings - proportionally smaller */
.fi-header-heading {
    @apply !text-lg !font-semibold; /* Reduced from text-xl */
}

/* Section headings */
.fi-section-header-heading {
    @apply !text-sm !font-semibold; /* Reduced from text-base */
}

/* Modal headers */
.fi-modal-header-heading {
    @apply !text-base !font-semibold; /* Reduced from text-lg */
}

/* Form sections - tighter spacing */
.fi-fo-section {
    @apply !space-y-3; /* Reduced from space-y-4 */
}

.fi-fo-section-content {
    @apply !p-3; /* Reduced from p-4 */
}

/* Form field wrapper - tighter spacing */
.fi-fo-field-wrp {
    @apply !space-y-1; /* Reduced from space-y-2 */
}

/* Additional vertical spacing reductions - from factor x8 to x6 */
.fi-section-header {
    @apply !py-1.5; /* Reduced section header vertical padding */
}

.fi-section-content {
    @apply !gap-1.5; /* Reduced content row gap from default (likely gap-2) */
}

/* Table row spacing - further tightening */
.fi-ta-row {
    @apply !py-0.5; /* Minimal vertical padding for table rows */
}

/* Table content wrapper - tighter spacing */
.fi-ta-content {
    @apply !space-y-1; /* Reduced spacing between table elements */
}

/* Help text - smaller font */
.fi-fo-field-wrp-hint {
    @apply !text-xs; /* Reduced from text-sm */
}

/* Error messages - smaller font */
.fi-fo-field-wrp-error-message {
    @apply !text-xs; /* Reduced from text-sm */
}

/* Badges - smaller padding and font */
.fi-badge {
    @apply !px-1.5 !py-0.5 !text-xs; /* Reduced from px-2 py-1 text-sm */
}

/* Action buttons in tables */
.fi-ta-actions {
    @apply !space-x-1; /* Reduced from space-x-2 */
}

/* Dropdown items - smaller padding */
.fi-dropdown-list-item {
    @apply !px-2 !py-1 !text-xs; /* Reduced from px-3 py-2 text-sm */
}

/* Notifications - smaller padding and font */
.fi-no-notification {
    @apply !p-3 !text-xs; /* Reduced from p-4 text-sm */
}

/* Widget cards - tighter padding */
.fi-wi-stats-card {
    @apply !p-3; /* Reduced from p-4 */
}

.fi-wi-stats-card-value {
    @apply !text-lg !font-semibold; /* Reduced from text-xl */
}

.fi-wi-stats-card-label {
    @apply !text-xs; /* Reduced from text-sm */
}

/* Pagination - smaller elements */
.fi-pagination {
    @apply !space-x-1; /* Reduced spacing */
}

.fi-pagination-item {
    @apply !px-2 !py-1 !text-xs; /* Reduced from px-3 py-2 text-sm */
}

/* Tabs - smaller padding */
.fi-tabs-tab {
    @apply !px-2 !py-1.5 !text-xs !font-medium; /* Reduced from px-3 py-2 text-sm */
}

/* Breadcrumbs - smaller font */
.fi-breadcrumbs-item {
    @apply !text-xs; /* Reduced from text-sm */
}

.fi-main-content {
    max-width: none !important; /* Remove max-width constraint */
    width: 100% !important; /* Use full available width */
}

.fi-page {
    max-width: none !important; /* Remove page max-width constraint */
    width: 100% !important;
}

.fi-section-content-ctn {
    max-width: none !important; /* Remove section content max-width */
    width: 100% !important;
}

/* Additional content width overrides */
.fi-main-ctn,
.fi-page-content,
.fi-section,
.fi-section-content {
    max-width: none !important;
    width: 100% !important;
}

/* Table and form maximization */
.fi-ta {
    max-width: none !important;
    width: 100% !important;
}

.fi-fo {
    max-width: none !important;
}

/* Table container full width */
.fi-ta-ctn {
    max-width: none !important;
    width: 100% !important;
}

/* Table header and body full width */
.fi-ta-header,
.fi-ta-content {
    width: 100% !important;
}

/* Override any max-width constraints on tables */
.fi-ta table,
.fi-ta-table {
    width: 100% !important;
    max-width: none !important;
}

/* Ensure table wrapper uses full width */
.fi-ta-wrapper {
    width: 100% !important;
    max-width: none !important;
}

/* Calendar widget full width */
.fi-wi {
    max-width: none !important;
}

/* Calendar page specific - filter buttons row padding */
[data-calendar] ~ .fi-section-header,
.fi-wi [data-calendar] .fi-section-header {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
}

/* More specific selector for calendar widget section header */
.fi-section:has([data-calendar]) .fi-section-header {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
}

/* Calendar styling improvements */
/* Thicker cell walls */
.ec-day {
    border-width: 2px !important;
}

/* All caps for month name */
.ec-title {
    text-transform: uppercase !important;
}

/* All caps for weekday names header row with background */
.ec-header .ec-days {
    background-color: #e5e7eb !important; /* Light gray background */
    max-height: 2rem !important;
}

.ec-header .ec-day {
    text-transform: uppercase !important;
    font-weight: 600 !important;
    padding: 0.125rem 0.25rem !important; /* Minimal padding to keep it small */
  
}

/* Dark mode support for weekday header */
.dark .ec-header.ec-days,
.dark .ec-header.ec-days .ec-day {
    background-color: #374151 !important; /* Dark gray for dark mode */
}

/* Calendar vertical stretch on large screens */
@media (min-width: 1024px) and (orientation: landscape) {
    /* Make the calendar page content fill available height */
    .fi-page-content {
        display: flex;
        flex-direction: column;
        min-height: calc(100vh - 4rem); /* Subtract header height */
    }

    /* Calendar widget should grow to fill space */
    .fi-wi-calendar {
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    /* Calendar section should fill available space */
    .fi-wi-calendar .fi-section {
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    /* Calendar content should fill section */
    .fi-wi-calendar .fi-section-content {
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    /* Event Calendar container should fill available space */
    .ec {
        height: 100% !important;
        min-height: 600px;
        display: flex !important;
        flex-direction: column !important;
    }

    /* Event Calendar body should expand */
    .ec-body {
        flex: 1 !important;
        display: flex !important;
        flex-direction: column !important;
    }

    /* Event Calendar content should expand */
    .ec-content {
        flex: 1 !important;
        display: flex !important;
        flex-direction: column !important;
    }

    /* Calendar days rows should expand equally */
    .ec-days {
        flex: 1 !important;
        display: flex !important;
    }

    /* Each day cell should expand */
    .ec-day {
        flex: 1 !important;
        min-height: 100px !important;
    }
}

/* Stage Progress Styles */
.stage-active {
    @apply bg-amber-500 shadow-sm;
}

.stage-inactive {
    @apply bg-gray-300;
}

.line-active {
    @apply bg-amber-500;
}

.line-inactive {
    @apply bg-gray-300;
}




