@import '../../../../vendor/filament/filament/resources/css/theme.css';

@source '../../../../app/Filament/Definitions/**/*';
@source '../../../../resources/views/filament/definitions/**/*';

/* Global Tighter Design - Smaller Fonts & Reduced Padding */

/* Base font size reduction (-2pts from default) */
html {
    font-size: 14px !important; /* Reduced from default 16px */
}

/* Button styling - smaller padding and font */
.fi-btn {
    @apply !px-2 !py-1.5 !text-xs !font-medium; /* Reduced from px-3 py-2 text-sm */
}

/* Form field labels - smaller font */
.fi-fo-field-wrp-label {
    @apply !text-xs !font-medium; /* Reduced from text-sm */
}

/* Form inputs - smaller padding and font */
.fi-input {
    @apply !px-2 !py-1.5 !text-xs; /* Reduced padding and font size */
}

/* Select inputs */
.fi-select-input {
    @apply !px-2 !py-1.5 !text-xs;
}

/* Textarea inputs */
.fi-textarea {
    @apply !px-2 !py-1.5 !text-xs;
}

/* Table headers - smaller font and padding */
.fi-ta-header-cell {
    @apply !px-2 !py-1 !text-xs !font-medium; /* Further reduced vertical padding from py-1.5 to py-1 */
}

/* Table cells - smaller padding and font */
.fi-ta-cell {
    @apply !px-2 !py-1 !text-xs; /* Further reduced vertical padding from py-1.5 to py-1 */
}

/* Table text items */
.fi-ta-text-item-label {
    @apply !text-xs; /* Reduced from text-sm */
}

/* Navigation items - smaller font and padding */
.fi-sidebar-nav-item {
    @apply !px-2 !py-1.5 !text-xs; /* Reduced padding and font */
}

/* Navigation labels */
.fi-sidebar-nav-item-label {
    @apply !text-xs !font-medium;
}

/* Page headings - proportionally smaller */
.fi-header-heading {
    @apply !text-lg !font-semibold; /* Reduced from text-xl */
}

/* Section headings */
.fi-section-header-heading {
    @apply !text-sm !font-semibold; /* Reduced from text-base */
}

/* Modal headers */
.fi-modal-header-heading {
    @apply !text-base !font-semibold; /* Reduced from text-lg */
}

/* Form sections - tighter spacing */
.fi-fo-section {
    @apply !space-y-3; /* Reduced from space-y-4 */
}

.fi-fo-section-content {
    @apply !p-3; /* Reduced from p-4 */
}

/* Form field wrapper - tighter spacing */
.fi-fo-field-wrp {
    @apply !space-y-1; /* Reduced from space-y-2 */
}

/* Additional vertical spacing reductions - from factor x8 to x6 */
.fi-section-header {
    @apply !py-1.5; /* Reduced section header vertical padding */
}

.fi-section-content {
    @apply !gap-1.5; /* Reduced content row gap from default (likely gap-2) */
}

/* Table row spacing - further tightening */
.fi-ta-row {
    @apply !py-0.5; /* Minimal vertical padding for table rows */
}

/* Table content wrapper - tighter spacing */
.fi-ta-content {
    @apply !space-y-1; /* Reduced spacing between table elements */
}

/* Help text - smaller font */
.fi-fo-field-wrp-hint {
    @apply !text-xs; /* Reduced from text-sm */
}

/* Error messages - smaller font */
.fi-fo-field-wrp-error-message {
    @apply !text-xs; /* Reduced from text-sm */
}

/* Badges - smaller padding and font */
.fi-badge {
    @apply !px-1.5 !py-0.5 !text-xs; /* Reduced from px-2 py-1 text-sm */
}

/* Action buttons in tables */
.fi-ta-actions {
    @apply !space-x-1; /* Reduced from space-x-2 */
}

/* Dropdown items - smaller padding */
.fi-dropdown-list-item {
    @apply !px-2 !py-1 !text-xs; /* Reduced from px-3 py-2 text-sm */
}

/* Notifications - smaller padding and font */
.fi-no-notification {
    @apply !p-3 !text-xs; /* Reduced from p-4 text-sm */
}

/* Widget cards - tighter padding */
.fi-wi-stats-card {
    @apply !p-3; /* Reduced from p-4 */
}

.fi-wi-stats-card-value {
    @apply !text-lg !font-semibold; /* Reduced from text-xl */
}

.fi-wi-stats-card-label {
    @apply !text-xs; /* Reduced from text-sm */
}

/* Pagination - smaller elements */
.fi-pagination {
    @apply !space-x-1; /* Reduced spacing */
}

.fi-pagination-item {
    @apply !px-2 !py-1 !text-xs; /* Reduced from px-3 py-2 text-sm */
}

/* Tabs - smaller padding */
.fi-tabs-tab {
    @apply !px-2 !py-1.5 !text-xs !font-medium; /* Reduced from px-3 py-2 text-sm */
}

/* Breadcrumbs - smaller font */
.fi-breadcrumbs-item {
    @apply !text-xs; /* Reduced from text-sm */
}

/* Workspace Width Maximization and Sidebar Customization */
.fi-sidebar {
    width: 14rem !important; /* Reduce sidebar width from default 16rem to 14rem */
}

.fi-main {
    margin-left: 14rem !important; /* Adjust main content margin to match sidebar */
}

.fi-main-content {
    max-width: none !important; /* Remove max-width constraint */
    width: 100% !important; /* Use full available width */
}

.fi-page {
    max-width: none !important; /* Remove page max-width constraint */
}

.fi-section-content-ctn {
    max-width: none !important; /* Remove section content max-width */
}

/* Table and form maximization */
.fi-ta {
    max-width: none !important;
}

.fi-fo {
    max-width: none !important;
}

/* Widget full width */
.fi-wi {
    max-width: none !important;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
    .fi-sidebar {
        width: 12rem !important;
    }

    .fi-main {
        margin-left: 12rem !important;
    }
}
