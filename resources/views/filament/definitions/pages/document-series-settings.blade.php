<x-filament-panels::page>
    <div class="space-y-6">
        {{-- Header Section --}}
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center space-x-3 mb-4">
                <div class="flex-shrink-0">
                    <svg class="h-8 w-8 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                </div>
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Document Series Settings</h1>
                    <p class="text-sm text-gray-600">Configure series prefixes for document numbering</p>
                </div>
            </div>
        </div>

        {{-- Form Section --}}
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <form wire:submit="save">
                <div class="p-6">
                    {{ $this->form }}
                </div>
                
                {{-- Form Actions --}}
                <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 rounded-b-lg">
                    <div class="flex justify-end space-x-3">
                        <x-filament::button
                            type="submit"
                            wire:click="save"
                        >
                            Save Settings
                        </x-filament::button>
                    </div>
                </div>
            </form>
        </div>

        {{-- Help Section --}}
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-blue-800">How document series work</h3>
                    <div class="mt-2 text-sm text-blue-700">
                        <p class="mb-2">Document series are used to create unique document titles with sequential numbering:</p>
                        <ul class="list-disc list-inside space-y-1">
                            <li><strong>Format:</strong> "PROFORMA Seria CRMPRF 00001 / 21.10.2025"</li>
                            <li><strong>Series:</strong> The prefix you configure (e.g., CRMPRF)</li>
                            <li><strong>Number:</strong> Automatically incremented (00001, 00002, etc.)</li>
                            <li><strong>Date:</strong> Current date when document is created</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-filament-panels::page>
