<x-filament-widgets::widget>
    @php
        $activities = $this->getActivities();
    @endphp
    
    <div class="space-y-4">
        {{-- Header --}}
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-xl font-bold text-gray-900 dark:text-white">
                    Activitățile mele
                </h2>
                <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                    Ai {{ $activities->total() }} {{ $activities->total() == 1 ? 'activitate asignată' : 'activități asignate' }}
                </p>
            </div>
        </div>
        
        @if($activities->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                @foreach($activities as $activity)
                    <x-filament::card>
                        {{-- Header with Comanda Info --}}
                        <div class="space-y-3">
                            <div class="flex items-center justify-between">
                                <x-filament::badge color="info">
                                    {{ $activity->comanda->internal_number }}
                                </x-filament::badge>
                                
                                <div class="flex gap-1">
                                    @if($activity->comanda->priority)
                                        <x-filament::badge color="danger" size="xs">
                                            Prioritate
                                        </x-filament::badge>
                                    @endif
                                    
                                    @if($activity->comanda->fast_track)
                                        <x-filament::badge color="warning" size="xs">
                                            Rapidă
                                        </x-filament::badge>
                                    @endif
                                </div>
                            </div>
                            
                            {{-- Comanda Name --}}
                            <h2 class="text-xl font-semibold text-gray-500 dark:text-gray-400">
                                {{ $activity->comanda->name }}
                            </h2>
                            
                            {{-- Activity Type --}}
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                                {{ $activity->activityType?->name ?? $activity->type }}
                            </h3>
                            
                            {{-- Description --}}
                            @if($activity->description)
                                <div class="text-sm text-gray-600 dark:text-gray-300 line-clamp-3">
                                    {{ $activity->description }}
                                </div>
                            @endif
                            
                            {{-- Deadline --}}
                            @php
                                $deadline = $activity->deadline ?? $activity->comanda->deadline;
                            @endphp
                            @if($deadline)
                                <div class="flex items-center gap-2 text-sm">
                                    <x-filament::icon
                                        icon="heroicon-m-calendar"
                                        class="h-4 w-4 text-gray-400"
                                    />
                                    <span class="text-gray-600 dark:text-gray-400">
                                        {{ $deadline->format('d.m.Y H:i') }}
                                    </span>
                                    
                                    @if($deadline->isPast())
                                        <x-filament::badge color="danger" size="xs">
                                            Întârziat
                                        </x-filament::badge>
                                    @elseif($deadline->diffInDays() <= 1)
                                        <x-filament::badge color="warning" size="xs">
                                            Urgent
                                        </x-filament::badge>
                                    @endif
                                </div>
                            @endif
                            
                            {{-- Files --}}
                            @php
                                $activityFiles = $activity->files->count() > 0 ? $activity->files : collect();
                                $attachmentFiles = $activity->attachments ?? [];
                                
                                // If no File records but attachments exist, create a collection from attachments
                                if ($activityFiles->isEmpty() && !empty($attachmentFiles)) {
                                    $activityFiles = collect($attachmentFiles)->map(function($filePath) {
                                        return (object) [
                                            'id' => null,
                                            'original_name' => basename($filePath),
                                            'path' => $filePath,
                                        ];
                                    });
                                }
                            @endphp
                            
                            @if($activityFiles->count() > 0)
                                <div class="space-y-2">
                                    <div class="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                                        <x-filament::icon
                                            icon="heroicon-m-paper-clip"
                                            class="h-4 w-4"
                                        />
                                        <span class="font-medium">{{ $activityFiles->count() }} {{ $activityFiles->count() == 1 ? 'fișier' : 'fișiere' }}</span>
                                    </div>
                                    
                                    {{-- File List --}}
                                    <div class="space-y-1 max-h-32 overflow-y-auto">
                                        @foreach($activityFiles as $file)
                                            <div class="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-800 rounded-md">
                                                <div class="flex items-center gap-2 min-w-0 flex-1">
                                                    {{-- File Icon --}}
                                                    @php
                                                        $extension = strtolower(pathinfo($file->original_name, PATHINFO_EXTENSION));
                                                        $icon = match($extension) {
                                                            'pdf' => 'heroicon-o-document-text',
                                                            'doc', 'docx' => 'heroicon-o-document',
                                                            'xls', 'xlsx' => 'heroicon-o-table-cells',
                                                            'jpg', 'jpeg', 'png', 'gif', 'svg' => 'heroicon-o-photo',
                                                            'txt', 'csv' => 'heroicon-o-document-text',
                                                            default => 'heroicon-o-paper-clip'
                                                        };
                                                    @endphp
                                                    
                                                    <x-filament::icon
                                                        :icon="$icon"
                                                        class="h-4 w-4 text-gray-500 flex-shrink-0"
                                                    />
                                                    
                                                    {{-- File Name --}}
                                                    <span class="text-xs text-gray-700 dark:text-gray-300 truncate" title="{{ $file->original_name }}">
                                                        {{ $file->original_name }}
                                                    </span>
                                                </div>
                                                
                                                {{-- Download Button --}}
                                                @if($file->id)
                                                    <a href="{{ route('filament.productie.files.download', $file->id) }}" 
                                                       class="flex-shrink-0 p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
                                                       title="Download {{ $file->original_name }}">
                                                        <x-filament::icon
                                                            icon="heroicon-o-arrow-down-tray"
                                                            class="h-4 w-4"
                                                        />
                                                    </a>
                                                @else
                                                    {{-- Direct download from storage for files without File records --}}
                                                    <a href="{{ route('filament.productie.attachments.download', $activity->id) }}?file={{ urlencode($file->path) }}" 
                                                       class="flex-shrink-0 p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
                                                       title="Download {{ $file->original_name }}">
                                                        <x-filament::icon
                                                            icon="heroicon-o-arrow-down-tray"
                                                            class="h-4 w-4"
                                                        />
                                                    </a>
                                                @endif
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @endif
                            
                            {{-- Stage Info --}}
                            <div class="flex items-center justify-between pt-2 border-t border-gray-200 dark:border-gray-700">
                                <span class="text-xs text-gray-500 dark:text-gray-400">
                                    {{ $activity->activityType?->stage_name ?? 'N/A' }}
                                </span>
                            </div>
                            
                            {{-- Action Button --}}
                            <div class="pt-3">
                                {{ ($this->markAsDoneAction)(['activity' => $activity->id]) }}
                            </div>
                        </div>
                    </x-filament::card>
                @endforeach
            </div>
            
            {{-- Pagination --}}
            @if($activities->hasPages())
                <div class="mt-4">
                    {{ $activities->links() }}
                </div>
            @endif
        @else
            {{-- Empty State --}}
            <div class="text-center py-12">
                <x-filament::icon
                    icon="heroicon-o-clipboard-document-check"
                    class="mx-auto h-12 w-12 text-gray-400"
                />
                
                <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                    Nicio activitate asignată
                </h3>
                
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    Nu ai activități în așteptare asignate momentan.
                </p>
            </div>
        @endif
    </div>
    
    <x-filament-actions::modals />
</x-filament-widgets::widget>

