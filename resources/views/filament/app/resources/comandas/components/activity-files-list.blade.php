@php
    // Access the repeater item state from Livewire
    $key = $getStatePath();
    
    // Try to get the activity ID from the form data
    $formData = $getRecord();
    
    // For repeaters, we need to find the activity in the form state
    $activityId = null;
    if ($formData && method_exists($formData, 'getKey')) {
        $activityId = $formData->getKey();
    }
    
    // Load the activity with files
    $activity = $activityId ? \App\Models\ComandaActivity::with('files')->find($activityId) : null;
@endphp

<div>
    <label class="block text-sm font-medium leading-6 text-gray-900 dark:text-white mb-2">
        Attached Files
    </label>
    
    @if(!$activity || $activity->files->isEmpty())
        <p class="text-sm text-gray-500">No files attached</p>
    @else
        <div class="space-y-2">
            @foreach($activity->files as $file)
                @php
                    $isNew = $file->created_at->isToday();
                @endphp
                <div class="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-800 rounded text-sm">
                    <span class="text-gray-700 dark:text-gray-300 flex items-center">
                        <x-filament::icon icon="heroicon-o-paper-clip" class="h-4 w-4 mr-2" />
                        {{ $file->original_name }}
                        @if($isNew)
                            <span class="ml-1 inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">NOU</span>
                        @endif
                    </span>
                    <a href="{{ route('files.download', $file->uuid) }}" target="_blank" class="text-blue-600 hover:text-blue-800">
                        <x-filament::icon icon="heroicon-o-arrow-down-tray" class="h-4 w-4" />
                    </a>
                </div>
            @endforeach
        </div>
    @endif
</div>

