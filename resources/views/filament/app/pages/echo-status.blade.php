<x-filament-panels::page>
    <div class="space-y-6">
        {{-- User Selection --}}
        <x-filament::section>
            <x-slot name="heading">
                User Selection
            </x-slot>

            <form wire:submit.prevent="save">
                {{ $this->form }}
            </form>

            @if($selectedUser = $this->getSelectedUser())
                <div class="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <p class="text-sm text-blue-800 dark:text-blue-200">
                        <strong>Testing as:</strong> {{ $selectedUser->name }} ({{ $selectedUser->email }})
                        <br>
                        <strong>Role:</strong> {{ ucfirst($selectedUser->role) }}
                        <br>
                        <strong>User ID:</strong> {{ $selectedUser->id }}
                        <br>
                        <strong>Channel:</strong> App.Models.User.{{ $selectedUser->id }}
                    </p>
                </div>
            @endif
        </x-filament::section>

        {{-- Connection Status Card --}}
        <x-filament::section>
            <x-slot name="heading">
                Connection Status
            </x-slot>

            <div class="space-y-4">
                <div id="connection-status" class="p-4 rounded-lg bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200">
                    Checking...
                </div>
                <div id="connection-details" class="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                    <!-- Details will be populated here -->
                </div>
            </div>
        </x-filament::section>

        {{-- Test Notifications Card --}}
        <x-filament::section>
            <x-slot name="heading">
                Test Notifications
            </x-slot>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                <x-filament::button 
                    color="info"
                    onclick="testBroadcastOnly()"
                    class="w-full"
                >
                    Broadcast Only (Toast)
                </x-filament::button>
                
                <x-filament::button 
                    color="success"
                    onclick="testDatabaseOnly()"
                    class="w-full"
                >
                    Database Only (Bell)
                </x-filament::button>
                
                <x-filament::button 
                    color="primary"
                    onclick="testBoth()"
                    class="w-full"
                >
                    Both (Toast + Bell) ✓
                </x-filament::button>
                
                <x-filament::button 
                    color="warning"
                    onclick="testTypes()"
                    class="w-full"
                >
                    All Types (4 notifications)
                </x-filament::button>
                
                <x-filament::button 
                    color="danger"
                    onclick="testPersistent()"
                    class="w-full"
                >
                    Persistent (Won't Close)
                </x-filament::button>
            </div>
        </x-filament::section>

        {{-- Event Log Card --}}
        <x-filament::section>
            <x-slot name="heading">
                Event Log
            </x-slot>
            
            <div id="event-log" class="space-y-2 max-h-96 overflow-y-auto font-mono text-sm">
                <!-- Events will appear here -->
            </div>
        </x-filament::section>
    </div>

    @push('scripts')
    <script>
        const statusEl = document.getElementById('connection-status');
        const detailsEl = document.getElementById('connection-details');
        const logEl = document.getElementById('event-log');

        let currentUserId = {{ $this->selectedUserId ?? auth()->id() }};
        let echoChannel = null;

        // Listen for user changes from Livewire
        window.addEventListener('user-changed', event => {
            currentUserId = event.detail.userId;
            addLog(`Switched to user ID: ${currentUserId}`, 'info');

            // Unsubscribe from old channel
            if (echoChannel) {
                window.Echo.leave(`App.Models.User.${echoChannel}`);
                addLog(`Left channel: App.Models.User.${echoChannel}`, 'info');
            }

            // Subscribe to new channel
            subscribeToUserChannel(currentUserId);
        });

        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                info: 'text-blue-600 dark:text-blue-400',
                success: 'text-green-600 dark:text-green-400',
                error: 'text-red-600 dark:text-red-400',
                warning: 'text-yellow-600 dark:text-yellow-400'
            };
            const entry = document.createElement('div');
            entry.className = colors[type] || colors.info;
            entry.textContent = `[${timestamp}] ${message}`;
            logEl.insertBefore(entry, logEl.firstChild);
        }
        
        function updateStatus(status, message, color) {
            statusEl.className = `p-4 rounded-lg ${color}`;
            statusEl.textContent = message;
            addLog(message, status);
        }

        function updateEchoConfig() {
            const config = window.Echo.connector?.options || {};
            detailsEl.innerHTML = `
                <p><strong>Echo Configuration:</strong></p>
                <p>• Broadcaster: ${config.broadcaster || 'unknown'}</p>
                <p>• Host: ${config.wsHost || 'unknown'}</p>
                <p>• Port: ${config.wsPort || config.wssPort || 'unknown'}</p>
                <p>• Force TLS: ${config.forceTLS ? 'Yes' : 'No'}</p>
                <p>• Auth Endpoint: ${config.authEndpoint || 'unknown'}</p>
                <p>• Logged in as: {{ auth()->user()->name }} (ID: {{ auth()->id() }})</p>
                <p>• Testing channel for User ID: ${currentUserId}</p>
                <p>• Channel: App.Models.User.${currentUserId}</p>
            `;
        }

        function subscribeToUserChannel(userId) {
            const channelName = `App.Models.User.${userId}`;

            addLog(`Subscribing to channel: ${channelName}`, 'info');

            echoChannel = userId;

            window.Echo.private(channelName)
                .notification((notification) => {
                    addLog(`Received notification: ${notification.title}`, 'success');
                    console.log('Notification received:', notification);
                })
                .error((error) => {
                    addLog(`Channel error: ${JSON.stringify(error)}`, 'error');
                });

            addLog('Listening for notifications on private channel', 'success');
            updateEchoConfig();
        }

        // Check if Echo is available
        setTimeout(() => {
            if (typeof window.Echo === 'undefined') {
                updateStatus('error', 'Echo is not loaded!', 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200');
                detailsEl.innerHTML = '<p class="text-red-600 dark:text-red-400">❌ Echo is not available. Check that Filament is loading Echo.</p>';
                addLog('Echo is undefined - Filament may not be initializing Echo', 'error');
                return;
            }
            
            addLog('Echo object found', 'success');
            
            // Display Echo configuration
            updateEchoConfig();
            
            // Check connection state
            if (window.Echo.connector?.pusher?.connection) {
                const connection = window.Echo.connector.pusher.connection;
                
                connection.bind('state_change', (states) => {
                    addLog(`Connection state: ${states.previous} → ${states.current}`, 'info');
                    
                    if (states.current === 'connected') {
                        updateStatus('success', 'Connected to Reverb!', 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200');
                    } else if (states.current === 'connecting') {
                        updateStatus('info', 'Connecting to Reverb...', 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200');
                    } else if (states.current === 'disconnected') {
                        updateStatus('warning', 'Disconnected from Reverb', 'bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200');
                    } else if (states.current === 'failed') {
                        updateStatus('error', 'Connection failed!', 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200');
                    }
                });
                
                connection.bind('error', (error) => {
                    addLog(`Connection error: ${JSON.stringify(error)}`, 'error');
                });
                
                // Check current state
                const currentState = connection.state;
                addLog(`Current connection state: ${currentState}`, 'info');
                
                if (currentState === 'connected') {
                    updateStatus('success', 'Connected to Reverb!', 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200');
                } else {
                    updateStatus('warning', `Connection state: ${currentState}`, 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200');
                }
            }
            
            // Subscribe to initial user's channel
            subscribeToUserChannel(currentUserId);

        }, 1000); // Wait 1 second for Echo to initialize
        
        // Test functions
        async function testBroadcastOnly() {
            addLog(`Sending broadcast-only notification to user ${currentUserId}...`, 'info');
            try {
                const response = await fetch(`/test/notification/broadcast-only?user_id=${currentUserId}`);
                const data = await response.json();
                addLog(data.message, 'success');
            } catch (error) {
                addLog(`Error: ${error.message}`, 'error');
            }
        }

        async function testDatabaseOnly() {
            addLog(`Sending database-only notification to user ${currentUserId}...`, 'info');
            try {
                const response = await fetch(`/test/notification/database-only?user_id=${currentUserId}`);
                const data = await response.json();
                addLog(data.message, 'success');
            } catch (error) {
                addLog(`Error: ${error.message}`, 'error');
            }
        }

        async function testBoth() {
            addLog(`Sending both database and broadcast notification to user ${currentUserId}...`, 'info');
            try {
                const response = await fetch(`/test/notification/both?user_id=${currentUserId}`);
                const data = await response.json();
                addLog(data.message, 'success');
            } catch (error) {
                addLog(`Error: ${error.message}`, 'error');
            }
        }

        async function testTypes() {
            addLog(`Sending 4 different notification types to user ${currentUserId}...`, 'info');
            try {
                const response = await fetch(`/test/notification/types?user_id=${currentUserId}`);
                const data = await response.json();
                addLog(data.message, 'success');
            } catch (error) {
                addLog(`Error: ${error.message}`, 'error');
            }
        }

        async function testPersistent() {
            addLog(`Sending persistent notification to user ${currentUserId}...`, 'info');
            try {
                const response = await fetch(`/test/notification/persistent?user_id=${currentUserId}`);
                const data = await response.json();
                addLog(data.message, 'success');
            } catch (error) {
                addLog(`Error: ${error.message}`, 'error');
            }
        }
    </script>
    @endpush
</x-filament-panels::page>

