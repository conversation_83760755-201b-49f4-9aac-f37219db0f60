@extends('documents.layouts.base')

@section('content')
<div class="document-header">
    <div class="company-info">
        <div class="company-details">
            <h1>{{ \App\Models\SystemSetting::get('company_name', config('company.name', 'Caramel CRM')) }}</h1>
            <p>{{ \App\Models\SystemSetting::get('company_address', config('company.address', 'Company Address')) }}</p>
            @php
                $cui = \App\Models\SystemSetting::get('company_cui', config('company.cui'));
                $regCom = \App\Models\SystemSetting::get('company_reg_com', config('company.reg_com'));
            @endphp
            @if($cui)
                <p>CUI: {{ $cui }}</p>
            @endif
            @if($regCom)
                <p>Reg. Com: {{ $regCom }}</p>
            @endif
        </div>
        <div class="document-meta">
            <h2>{{ strtoupper($document->getTypeLabel()) }}</h2>
            <p><strong>Nr:</strong> {{ $document->document_number }}</p>
            <p><strong>Data:</strong> {{ $document->created_at->format('d.m.Y') }}</p>
        </div>
    </div>
</div>

<div class="client-info">
    <h3>Client:</h3>
    <p><strong>{{ $client->company_name ?? $client->name }}</strong></p>
    <p>{{ $client->address }}</p>
    @if($client->cui)
        <p>CUI: {{ $client->cui }}</p>
    @endif
    @if($client->reg_com)
        <p>Reg. Com: {{ $client->reg_com }}</p>
    @endif
    @if($client->email)
        <p>Email: {{ $client->email }}</p>
    @endif
    @if($client->phone)
        <p>Telefon: {{ $client->phone }}</p>
    @endif
</div>

<div class="comanda-info">
    <h3>Comandă: {{ $comanda->name }}</h3>
    @if($comanda->description)
        <p>{{ $comanda->description }}</p>
    @endif
    <p><strong>Număr intern:</strong> {{ $comanda->internal_number }}</p>
    <p><strong>Stadiu:</strong> Stadiul {{ $comanda->stage }}</p>
</div>

@if($comanda->items && $comanda->items->count() > 0)
<table class="items-table">
    <thead>
        <tr>
            <th>Nr. Crt.</th>
            <th>Descriere</th>
            <th>U.M.</th>
            <th>Cantitate</th>
            <th>Preț Unitar</th>
            <th>Valoare</th>
        </tr>
    </thead>
    <tbody>
        @foreach($comanda->items as $index => $item)
        <tr>
            <td class="number">{{ $index + 1 }}</td>
            <td>{{ $item->description }}</td>
            <td>{{ $item->unit ?? 'buc' }}</td>
            <td class="number">{{ $item->quantity }}</td>
            <td class="price">{{ number_format($item->unit_price, 2) }} {{ \App\Models\SystemSetting::get('default_currency', config('company.default_currency', 'RON')) }}</td>
            <td class="price">{{ number_format($item->total_price, 2) }} {{ \App\Models\SystemSetting::get('default_currency', config('company.default_currency', 'RON')) }}</td>
        </tr>
        @endforeach
    </tbody>
</table>

<div class="totals-section">
    @php
        $subtotal = $comanda->items->sum('total_price');
        $vatRate = \App\Models\SystemSetting::get('vat_rate', config('company.vat_rate', 19));
        $vatAmount = $subtotal * ($vatRate / 100);
        $total = $subtotal + $vatAmount;
    @endphp
    
    <p><strong>Subtotal: {{ number_format($subtotal, 2) }} {{ \App\Models\SystemSetting::get('default_currency', config('company.default_currency', 'RON')) }}</strong></p>
    <p>TVA ({{ $vatRate }}%): {{ number_format($vatAmount, 2) }} {{ \App\Models\SystemSetting::get('default_currency', config('company.default_currency', 'RON')) }}</p>
    <h3>Total: {{ number_format($total, 2) }} {{ \App\Models\SystemSetting::get('default_currency', config('company.default_currency', 'RON')) }}</h3>
</div>
@endif

{{-- Custom fields from template --}}
@if(isset($custom_fields) && count($custom_fields) > 0)
<div class="custom-fields">
    <h3>Informații suplimentare:</h3>
    @foreach($custom_fields as $field => $value)
        @if($value)
            <p><strong>{{ ucfirst(str_replace('_', ' ', $field)) }}:</strong> {{ $value }}</p>
        @endif
    @endforeach
</div>
@endif

<div class="document-footer">
    @if($document->type === 'proforma')
        <p>Proforma valabilă 30 de zile de la data emiterii.</p>
    @endif
    <p>Document generat automat de sistemul CRM Caramel la {{ now()->format('d.m.Y H:i') }}.</p>
    @if($document->generated_by)
        <p>Generat de: {{ $document->generatedBy->name ?? 'Sistem' }}</p>
    @endif
</div>
@endsection
