<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reverb WebSocket Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-2xl mx-auto">
        <h1 class="text-3xl font-bold mb-6">Reverb WebSocket Test</h1>
        
        <div class="bg-white p-6 rounded-lg shadow-md mb-6">
            <h2 class="text-xl font-semibold mb-4">Connection Status</h2>
            <div id="connection-status" class="p-3 rounded bg-yellow-100 text-yellow-800">
                Connecting...
            </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow-md mb-6">
            <h2 class="text-xl font-semibold mb-4">Test Notification</h2>
            <button id="send-test" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Send Test Notification
            </button>
        </div>

        <div class="bg-white p-6 rounded-lg shadow-md">
            <h2 class="text-xl font-semibold mb-4">Received Messages</h2>
            <div id="messages" class="space-y-2 max-h-64 overflow-y-auto">
                <!-- Messages will appear here -->
            </div>
        </div>
    </div>

    @vite(['resources/js/app.js'])
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const statusEl = document.getElementById('connection-status');
            const messagesEl = document.getElementById('messages');
            const sendTestBtn = document.getElementById('send-test');

            function addMessage(message, type = 'info') {
                const messageEl = document.createElement('div');
                const bgColor = type === 'success' ? 'bg-green-100 text-green-800' : 
                               type === 'error' ? 'bg-red-100 text-red-800' : 
                               'bg-blue-100 text-blue-800';
                
                messageEl.className = `p-3 rounded ${bgColor}`;
                messageEl.innerHTML = `
                    <div class="font-semibold">${type.toUpperCase()}</div>
                    <div>${message}</div>
                    <div class="text-xs opacity-75">${new Date().toLocaleTimeString()}</div>
                `;
                messagesEl.appendChild(messageEl);
                messagesEl.scrollTop = messagesEl.scrollHeight;
            }

            // Wait for Filament's Echo to be available
            window.addEventListener('EchoLoaded', () => {
                addMessage('Filament Echo loaded!', 'success');
                initializeEcho();
            });

            // Also check if Echo is already available
            const checkEcho = setInterval(() => {
                if (window.Echo) {
                    clearInterval(checkEcho);
                    addMessage('Echo found via polling', 'info');
                    initializeEcho();
                }
            }, 100);

            function initializeEcho() {
                addMessage('Echo is available, attempting to connect...', 'info');

                // Listen for connection events
                window.Echo.connector.pusher.connection.bind('connected', () => {
                    statusEl.className = 'p-3 rounded bg-green-100 text-green-800';
                    statusEl.textContent = 'Connected to Reverb!';
                    addMessage('Successfully connected to Reverb WebSocket server!', 'success');
                });

                window.Echo.connector.pusher.connection.bind('disconnected', () => {
                    statusEl.className = 'p-3 rounded bg-red-100 text-red-800';
                    statusEl.textContent = 'Disconnected from Reverb';
                    addMessage('Disconnected from Reverb WebSocket server', 'error');
                });

                window.Echo.connector.pusher.connection.bind('error', (error) => {
                    statusEl.className = 'p-3 rounded bg-red-100 text-red-800';
                    statusEl.textContent = 'Connection Error';
                    addMessage(`Connection error: ${JSON.stringify(error)}`, 'error');
                });

                // Listen for test notifications
                window.Echo.channel('test-notifications')
                    .listen('.test.notification', (e) => {
                        addMessage(`Received: ${e.message}`, e.type || 'info');
                    });

                addMessage('Echo listeners set up successfully', 'info');
            }

            // Send test notification
            sendTestBtn.addEventListener('click', async () => {
                try {
                    const response = await fetch('/test-notification');
                    const data = await response.json();
                    addMessage(`Sent test notification: ${data.message}`, 'info');
                } catch (error) {
                    addMessage(`Error sending notification: ${error.message}`, 'error');
                }
            });
        });
    </script>
</body>
</html>
