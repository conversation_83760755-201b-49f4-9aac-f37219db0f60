@php
    $userData = $getState();
    $userName = $userData['name'] ?? 'Unknown';
    $userId = $userData['id'];
    
    // Generate initials
    $initials = '';
    $nameParts = explode(' ', $userName);
    foreach ($nameParts as $part) {
        if (strlen($initials) < 2) {
            $initials .= strtoupper(substr($part, 0, 1));
        } else {
            break;
        }
    }
    
    // Generate color based on user ID
    $colors = [
        '#ef4444', // red-500
        '#10b981', // green-500
        '#3b82f6', // blue-500
        '#f59e0b', // yellow-500
        '#8b5cf6', // purple-500
        '#dc2626', // red-600
        '#059669', // green-600
        '#2563eb', // blue-600
        '#d97706', // yellow-600
        '#7c3aed', // purple-600
    ];
    
    $colorIndex = $userId ? $userId % count($colors) : 0;
    $backgroundColor = $colors[$colorIndex];
@endphp

<div 
    title="{{ $userName }}"
    style="
        width: 32px; 
        height: 32px; 
        border-radius: 50%; 
        background-color: {{ $backgroundColor }};
        display: flex; 
        align-items: center; 
        justify-content: center; 
        color: white; 
        font-weight: bold; 
        font-size: 12px;
        margin: 0 auto;
    "
>
    {{ $initials }}
</div>
