@php
    $comanda = $getRecord();
    $currentStage = $comanda->stage;
    $blockedStages = $comanda->blocked_stages ?? [];
    $isFinished = $comanda->is_finished;

    $stages = [
        1 => 'Ofertare',
        2 => 'Contractare',
        3 => 'Pregatire',
        4 => 'Aprovizionare',
        5 => 'Executie',
        6 => 'Livrare',
        7 => 'Facturare'
    ];
    
    // Color scheme
    $completedColor = $isFinished ? '#bef264' : '#f59e0b'; // Pastel lime green if finished, orange otherwise
    $incompleteColor = '#d1d5db'; // Gray
@endphp

<div style="display: flex; align-items: center; justify-content: center; padding: 8px 0; min-width: 300px;">
    @foreach($stages as $stageNumber => $stageName)
        @php
            $isCompleted = $currentStage >= $stageNumber;
            $isBlocked = in_array($stageNumber, $blockedStages);
            
            // Build tooltip
            $tooltip = $stageName;
            if ($currentStage == $stageNumber) {
                $tooltip .= ' (Current)';
            } elseif ($currentStage > $stageNumber) {
                $tooltip .= ' (Complete)';
            }
            if ($isBlocked) {
                $tooltip .= ' - Has unfinished activities!';
            }
        @endphp
        
        <div style="display: flex; align-items: center;">
            <!-- Stage Circle -->
            <div style="position: relative;">
                <div
                    title="{{ $tooltip }}"
                    style="
                        width: 32px;
                        height: 32px;
                        border-radius: 50%;
                        background-color: {{ $isCompleted ? $completedColor : $incompleteColor }};
                        box-shadow: {{ $isCompleted ? '0 1px 3px rgba(0,0,0,0.1)' : 'none' }};
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    ">
                    <!-- Blocker Warning Icon -->
                    @if($isBlocked)
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" 
                             style="width: 20px; height: 20px; color: #dc2626;">
                            <path fill-rule="evenodd" d="M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z" clip-rule="evenodd" />
                        </svg>
                    @endif
                </div>
            </div>

            <!-- Connecting Line (except for last stage) -->
            @if($stageNumber < 7)
                <div style="
                    width: 40px;
                    height: 4px;
                    background-color: {{ $currentStage > $stageNumber ? $completedColor : $incompleteColor }};
                "></div>
            @endif
        </div>
    @endforeach
</div>
