<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Echo Connection Status</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-6">Laravel Echo / Reverb Connection Test</h1>
        
        <div class="bg-white p-6 rounded-lg shadow-md mb-6">
            <h2 class="text-xl font-semibold mb-4">Connection Status</h2>
            <div id="connection-status" class="p-4 rounded bg-yellow-100 text-yellow-800 mb-4">
                Checking...
            </div>
            <div id="connection-details" class="text-sm text-gray-600 space-y-1">
                <!-- Details will be populated here -->
            </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow-md mb-6">
            <h2 class="text-xl font-semibold mb-4">Test Notifications</h2>
            <div class="space-y-2">
                <button onclick="testBroadcastOnly()" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded w-full">
                    Test Broadcast Only (Toast Only)
                </button>
                <button onclick="testDatabaseOnly()" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded w-full">
                    Test Database Only (Bell Icon Only)
                </button>
                <button onclick="testBoth()" class="bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded w-full">
                    Test Both (Toast + Bell Icon) ✓
                </button>
                <button onclick="testTypes()" class="bg-orange-500 hover:bg-orange-700 text-white font-bold py-2 px-4 rounded w-full">
                    Test All Types (4 notifications)
                </button>
                <button onclick="testPersistent()" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded w-full">
                    Test Persistent (Won't Auto-Close)
                </button>
            </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow-md">
            <h2 class="text-xl font-semibold mb-4">Event Log</h2>
            <div id="event-log" class="space-y-2 max-h-96 overflow-y-auto font-mono text-sm">
                <!-- Events will appear here -->
            </div>
        </div>
    </div>

    @vite(['resources/js/app.js'])
    
    <script>
        const statusEl = document.getElementById('connection-status');
        const detailsEl = document.getElementById('connection-details');
        const logEl = document.getElementById('event-log');
        
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                info: 'text-blue-600',
                success: 'text-green-600',
                error: 'text-red-600',
                warning: 'text-yellow-600'
            };
            const entry = document.createElement('div');
            entry.className = colors[type] || colors.info;
            entry.textContent = `[${timestamp}] ${message}`;
            logEl.insertBefore(entry, logEl.firstChild);
        }
        
        function updateStatus(status, message, color) {
            statusEl.className = `p-4 rounded ${color}`;
            statusEl.textContent = message;
            addLog(message, status);
        }
        
        // Check if Echo is available
        setTimeout(() => {
            if (typeof window.Echo === 'undefined') {
                updateStatus('error', 'Echo is not loaded!', 'bg-red-100 text-red-800');
                detailsEl.innerHTML = '<p class="text-red-600">❌ Echo is not available. Check that Filament is loading Echo.</p>';
                addLog('Echo is undefined - Filament may not be initializing Echo', 'error');
                return;
            }
            
            addLog('Echo object found', 'success');
            
            // Display Echo configuration
            const config = window.Echo.connector?.options || {};
            detailsEl.innerHTML = `
                <p><strong>Echo Configuration:</strong></p>
                <p>• Broadcaster: ${config.broadcaster || 'unknown'}</p>
                <p>• Host: ${config.wsHost || 'unknown'}</p>
                <p>• Port: ${config.wsPort || config.wssPort || 'unknown'}</p>
                <p>• Force TLS: ${config.forceTLS ? 'Yes' : 'No'}</p>
                <p>• Auth Endpoint: ${config.authEndpoint || 'unknown'}</p>
                <p>• User ID: {{ auth()->id() }}</p>
                <p>• Channel: App.Models.User.{{ auth()->id() }}</p>
            `;
            
            // Check connection state
            if (window.Echo.connector?.pusher?.connection) {
                const connection = window.Echo.connector.pusher.connection;
                
                connection.bind('state_change', (states) => {
                    addLog(`Connection state: ${states.previous} → ${states.current}`, 'info');
                    
                    if (states.current === 'connected') {
                        updateStatus('success', 'Connected to Reverb!', 'bg-green-100 text-green-800');
                    } else if (states.current === 'connecting') {
                        updateStatus('info', 'Connecting to Reverb...', 'bg-yellow-100 text-yellow-800');
                    } else if (states.current === 'disconnected') {
                        updateStatus('warning', 'Disconnected from Reverb', 'bg-orange-100 text-orange-800');
                    } else if (states.current === 'failed') {
                        updateStatus('error', 'Connection failed!', 'bg-red-100 text-red-800');
                    }
                });
                
                connection.bind('error', (error) => {
                    addLog(`Connection error: ${JSON.stringify(error)}`, 'error');
                });
                
                // Check current state
                const currentState = connection.state;
                addLog(`Current connection state: ${currentState}`, 'info');
                
                if (currentState === 'connected') {
                    updateStatus('success', 'Connected to Reverb!', 'bg-green-100 text-green-800');
                } else {
                    updateStatus('warning', `Connection state: ${currentState}`, 'bg-yellow-100 text-yellow-800');
                }
            }
            
            // Listen to user's private channel for notifications
            const userId = {{ auth()->id() }};
            const channelName = `App.Models.User.${userId}`;
            
            addLog(`Subscribing to channel: ${channelName}`, 'info');
            
            window.Echo.private(channelName)
                .notification((notification) => {
                    addLog(`Received notification: ${notification.title}`, 'success');
                    console.log('Notification received:', notification);
                })
                .error((error) => {
                    addLog(`Channel error: ${JSON.stringify(error)}`, 'error');
                });
                
            addLog('Listening for notifications on private channel', 'success');
            
        }, 1000); // Wait 1 second for Echo to initialize
        
        // Test functions
        async function testBroadcastOnly() {
            addLog('Sending broadcast-only notification...', 'info');
            const response = await fetch('/test/notification/broadcast-only');
            const data = await response.json();
            addLog(data.message, 'success');
        }
        
        async function testDatabaseOnly() {
            addLog('Sending database-only notification...', 'info');
            const response = await fetch('/test/notification/database-only');
            const data = await response.json();
            addLog(data.message, 'success');
        }
        
        async function testBoth() {
            addLog('Sending both database and broadcast notification...', 'info');
            const response = await fetch('/test/notification/both');
            const data = await response.json();
            addLog(data.message, 'success');
        }
        
        async function testTypes() {
            addLog('Sending 4 different notification types...', 'info');
            const response = await fetch('/test/notification/types');
            const data = await response.json();
            addLog(data.message, 'success');
        }
        
        async function testPersistent() {
            addLog('Sending persistent notification...', 'info');
            const response = await fetch('/test/notification/persistent');
            const data = await response.json();
            addLog(data.message, 'success');
        }
    </script>
</body>
</html>

