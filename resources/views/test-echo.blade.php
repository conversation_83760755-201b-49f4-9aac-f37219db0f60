<!DOCTYPE html>
<html>
<head>
    <title>Echo Config Test</title>
</head>
<body>
    <h1>Echo Configuration Test</h1>
    
    <h2>Server Config:</h2>
    <pre>{{ json_encode(config('filament.broadcasting.echo'), J<PERSON><PERSON>_PRETTY_PRINT) }}</pre>
    
    <h2>JavaScript Config:</h2>
    <pre id="js-config"></pre>
    
    <script>
        const config = @js(config('filament.broadcasting.echo'));
        document.getElementById('js-config').textContent = JSON.stringify(config, null, 2);
        console.log('Echo config:', config);
    </script>
</body>
</html>

