<?php

namespace App\Policies;

use App\Models\File;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class FilePolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->hasAnyRole(['superadmin', 'manager', 'pm', 'specialist']);
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, File $file): bool
    {
        // Superadmin and managers can view all files
        if ($user->hasAnyRole(['superadmin', 'manager'])) {
            return true;
        }

        // Check if user has access to the related comanda/activity
        if ($file->fileable_type === 'App\\Models\\Comanda') {
            $comanda = $file->fileable;
            return $user->id === $comanda->owner_id ||
                   $comanda->activities()->where('assigned_to', $user->id)->exists();
        }

        if ($file->fileable_type === 'App\\Models\\ComandaActivity') {
            $activity = $file->fileable;
            return $user->id === $activity->assigned_to ||
                   $user->id === $activity->comanda->owner_id;
        }

        return false;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->hasAnyRole(['superadmin', 'manager', 'pm', 'specialist']);
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, File $file): bool
    {
        // Superadmin and managers can update all files
        if ($user->hasAnyRole(['superadmin', 'manager'])) {
            return true;
        }

        // File uploader can update their own files
        if ($user->id === $file->uploaded_by) {
            return true;
        }

        // Check if user has access to the related comanda/activity
        return $this->view($user, $file);
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, File $file): bool
    {
        // Superadmin and managers can delete all files
        if ($user->hasAnyRole(['superadmin', 'manager'])) {
            return true;
        }

        // File uploader can delete their own files
        if ($user->id === $file->uploaded_by) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, File $file): bool
    {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, File $file): bool
    {
        return false;
    }
}
