<?php

namespace App\Policies;

use App\Models\GeneratedDocument;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class GeneratedDocumentPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return true; // Allow authenticated users to view documents
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, GeneratedDocument $generatedDocument): bool
    {
        return true; // Allow authenticated users to view any document
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return true; // Allow authenticated users to create documents
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, GeneratedDocument $generatedDocument): bool
    {
        return true; // Allow authenticated users to update documents
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, GeneratedDocument $generatedDocument): bool
    {
        return true; // Allow authenticated users to delete documents
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, GeneratedDocument $generatedDocument): bool
    {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, GeneratedDocument $generatedDocument): bool
    {
        return false;
    }
}
