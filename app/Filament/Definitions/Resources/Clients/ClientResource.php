<?php

namespace App\Filament\Definitions\Resources\Clients;

use App\Filament\Definitions\Resources\Clients\Pages\CreateClient;
use App\Filament\Definitions\Resources\Clients\Pages\EditClient;
use App\Filament\Definitions\Resources\Clients\Pages\ListClients;
use App\Filament\Definitions\Resources\Clients\Schemas\ClientForm;
use App\Filament\Definitions\Resources\Clients\Tables\ClientsTable;
use App\Models\Client;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;

class ClientResource extends Resource
{
    protected static ?string $model = Client::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedRectangleStack;

    protected static ?string $recordTitleAttribute = 'display_name';

    public static function form(Schema $schema): Schema
    {
        return ClientForm::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return ClientsTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListClients::route('/'),
            'create' => CreateClient::route('/create'),
            'edit' => EditClient::route('/{record}/edit'),
        ];
    }

    // RBAC Implementation
    public static function canViewAny(): bool
    {
        $user = auth()->user();

        if (!$user || !$user->is_active) {
            return false;
        }

        // Only superadmin, manager, and PM can access client definitions
        return $user->hasAnyRole(['superadmin', 'manager', 'pm']);
    }

    public static function canView(Model $record): bool
    {
        return static::canViewAny();
    }

    public static function canCreate(): bool
    {
        $user = auth()->user();

        if (!$user || !$user->is_active) {
            return false;
        }

        // Only superadmin and manager can create clients
        return $user->hasAnyRole(['superadmin', 'manager']);
    }

    public static function canEdit(Model $record): bool
    {
        $user = auth()->user();

        if (!$user || !$user->is_active) {
            return false;
        }

        // Only superadmin and manager can edit clients
        return $user->hasAnyRole(['superadmin', 'manager']);
    }

    public static function canDelete(Model $record): bool
    {
        $user = auth()->user();

        if (!$user || !$user->is_active) {
            return false;
        }

        // Only superadmin can delete clients
        return $user->isRole('superadmin');
    }
}
