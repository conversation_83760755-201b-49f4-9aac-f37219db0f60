<?php

namespace App\Filament\Definitions\Resources\Clients\Schemas;

use App\Services\InfocuiService;
use Filament\Actions\Action;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;
use Illuminate\Support\Facades\Log;

class ClientForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make('Client Type')
                    ->schema([
                        Radio::make('client_type')
                            ->options([
                                'company' => 'Company',
                                'individual' => 'Individual',
                            ])
                            ->default('company')
                            ->reactive()
                            ->afterStateUpdated(function ($state, callable $set) {
                                if ($state === 'company') {
                                    $set('name', null);
                                } else {
                                    $set('company_name', null);
                                    $set('cui', null);
                                    $set('reg_com', null);
                                }
                            }),
                    ])
                    ->columns(2),

                Section::make('Basic Information')
                    ->schema([
                        TextInput::make('company_name')
                            ->label('Company Name')
                            ->visible(fn ($get) => $get('client_type') === 'company')
                            ->required(fn ($get) => $get('client_type') === 'company')
                            ->maxLength(255),

                        TextInput::make('name')
                            ->label('Individual Name')
                            ->visible(fn ($get) => $get('client_type') === 'individual')
                            ->required(fn ($get) => $get('client_type') === 'individual')
                            ->maxLength(255),

                        TextInput::make('cui')
                            ->label('CUI (Tax ID)')
                            ->visible(fn ($get) => $get('client_type') === 'company')
                            ->placeholder('12345678')
                            ->helperText('Enter CUI without RO prefix')
                            ->prefix(fn ($get) => !empty($get('tva')) ? 'RO' : null)
                            ->maxLength(20)
                            ->reactive()
                            ->suffixAction(
                                Action::make('lookup')
                                    ->icon('heroicon-m-magnifying-glass')
                                    ->tooltip('Lookup company data from Infocui')
                                    ->action(function ($get, $set) {
                                        $cui = $get('cui');
                                        
                                        if (empty($cui)) {
                                            \Filament\Notifications\Notification::make()
                                                ->title('Error')
                                                ->body('Please enter a CUI first')
                                                ->danger()
                                                ->send();
                                            return;
                                        }
                                        
                                        try {
                                            $service = new InfocuiService();
                                            $data = $service->getCompanyInfo($cui);
                                            
                                            if ($data === null) {
                                                \Filament\Notifications\Notification::make()
                                                    ->title('Not Found')
                                                    ->body('No company found with this CUI')
                                                    ->warning()
                                                    ->send();
                                                return;
                                            }
                                            
                                            // Populate fields only if they are empty
                                            if (empty($get('company_name'))) {
                                                $set('company_name', $data['company_name'] ?? null);
                                            }
                                            if (empty($get('reg_com'))) {
                                                $set('reg_com', $data['reg_com'] ?? null);
                                            }
                                            if (empty($get('address'))) {
                                                $set('address', $data['address'] ?? null);
                                            }
                                            if (empty($get('phone'))) {
                                                $set('phone', $data['phone'] ?? null);
                                            }
                                            if (empty($get('tva'))) {
                                                $set('tva', $data['tva'] ?? null);
                                            }
                                            if (empty($get('notes'))) {
                                                $set('notes', $data['notes'] ?? null);
                                            }
                                            
                                            // Log TVA for debugging
                                            Log::info('Infocui lookup completed', [
                                                'cui' => $cui,
                                                'tva_from_api' => $data['tva'] ?? 'null',
                                                'tva_in_form' => $get('tva'),
                                            ]);
                                            
                                            \Filament\Notifications\Notification::make()
                                                ->title('Success')
                                                ->body('Company data loaded successfully' . ($data['tva'] ? ' (TVA registered)' : ''))
                                                ->success()
                                                ->send();
                                        } catch (\Exception $e) {
                                            Log::error('Infocui lookup failed', ['error' => $e->getMessage()]);
                                            \Filament\Notifications\Notification::make()
                                                ->title('Error')
                                                ->body('Failed to lookup company data: ' . $e->getMessage())
                                                ->danger()
                                                ->send();
                                        }
                                    })
                            ),

                        TextInput::make('reg_com')
                            ->label('Reg. Com.')
                            ->visible(fn ($get) => $get('client_type') === 'company')
                            ->placeholder('J40/1234/2024')
                            ->maxLength(50),

                        Textarea::make('address')
                            ->required()
                            ->rows(3)
                            ->columnSpanFull(),
                    ])
                    ->columns(2),

                Section::make('Contact Information')
                    ->schema([
                        TextInput::make('email')
                            ->label('Email Address')
                            ->email()
                            ->maxLength(255),

                        TextInput::make('phone')
                            ->tel()
                            ->maxLength(20),

                        TextInput::make('contact_person')
                            ->label('Contact Person')
                            ->maxLength(255),

                        TextInput::make('contact_email')
                            ->label('Contact Email')
                            ->email()
                            ->maxLength(255),

                        TextInput::make('contact_phone')
                            ->label('Contact Phone')
                            ->tel()
                            ->maxLength(20),
                    ])
                    ->columns(2),

                Section::make('Banking Details')
                    ->schema([
                        TextInput::make('bank_name')
                            ->label('Bank Name')
                            ->maxLength(255),

                        TextInput::make('bank_account')
                            ->label('Bank Account')
                            ->maxLength(50),
                    ])
                    ->columns(2)
                    ->collapsible(),

                Section::make('Additional Information')
                    ->schema([
                        Textarea::make('notes')
                            ->rows(3)
                            ->columnSpanFull(),

                        Toggle::make('is_active')
                            ->label('Active')
                            ->default(true),
                    ])
                    ->collapsible(),
            ]);
    }
}
