<?php

namespace App\Filament\Definitions\Resources\SystemSettings;

use App\Filament\Definitions\Resources\SystemSettings\Pages\CreateSystemSetting;
use App\Filament\Definitions\Resources\SystemSettings\Pages\EditSystemSetting;
use App\Filament\Definitions\Resources\SystemSettings\Pages\ListSystemSettings;
use App\Models\SystemSetting;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Section;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\IconColumn;

class SystemSettingResource extends Resource
{
    protected static ?string $model = SystemSetting::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedCog6Tooth;
    
    protected static ?string $navigationLabel = 'Document Series';
    
    protected static ?string $title = 'Document Series Configuration';

    public static function form(Schema $schema): Schema
    {
        return $schema
            ->schema([
                Section::make('Document Series Configuration')
                    ->description('Configure the series prefixes for different document types')
                    ->schema([
                        TextInput::make('proforma_series')
                            ->label('Proforma Series')
                            ->default(fn() => SystemSetting::getDocumentSeries('proforma'))
                            ->placeholder('e.g., CRMPRF')
                            ->maxLength(10)
                            ->required()
                            ->helperText('Series prefix for proforma invoices (e.g., CRMPRF for "PROFORMA Seria CRMPRF 00001")'),
                            
                        TextInput::make('invoice_series')
                            ->label('Invoice Series')
                            ->default(fn() => SystemSetting::getDocumentSeries('invoice'))
                            ->placeholder('e.g., CRMINV')
                            ->maxLength(10)
                            ->required()
                            ->helperText('Series prefix for invoices'),
                            
                        TextInput::make('contract_series')
                            ->label('Contract Series')
                            ->default(fn() => SystemSetting::getDocumentSeries('contract'))
                            ->placeholder('e.g., CRMCTR')
                            ->maxLength(10)
                            ->required()
                            ->helperText('Series prefix for contracts'),
                    ])
                    ->columns(1),
            ])
            ->statePath('data');
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('key')
                    ->label('Setting Key')
                    ->searchable()
                    ->sortable(),
                    
                TextColumn::make('value')
                    ->label('Value')
                    ->searchable(),
                    
                TextColumn::make('description')
                    ->label('Description')
                    ->limit(50),
                    
                TextColumn::make('updated_at')
                    ->label('Last Updated')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                //
            ])
            ->bulkActions([
                //
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListSystemSettings::route('/'),
            'create' => CreateSystemSetting::route('/create'),
            'edit' => EditSystemSetting::route('/{record}/edit'),
        ];
    }
}
