<?php

namespace App\Filament\Definitions\Resources\ActivityTypes\Tables;

use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TernaryFilter;
use Filament\Tables\Table;

class ActivityTypesTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label('Name')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),

                TextColumn::make('key')
                    ->label('Key')
                    ->searchable()
                    ->sortable()
                    ->fontFamily('mono')
                    ->color('gray'),

                TextColumn::make('stage_name')
                    ->label('Stage')
                    ->sortable(query: function ($query, string $direction) {
                        return $query->orderBy('stage', $direction);
                    })
                    ->badge()
                    ->color(fn (string $state): string => match(true) {
                        str_contains($state, 'Ofertare') => 'info',
                        str_contains($state, 'Contractare') => 'success',
                        str_contains($state, 'Pregatire') => 'warning',
                        str_contains($state, 'Aprovizionare') => 'primary',
                        str_contains($state, 'Executie') => 'danger',
                        str_contains($state, 'Livrare') => 'purple',
                        str_contains($state, 'Facturare') => 'gray',
                        default => 'gray'
                    }),

                TextColumn::make('sort_order')
                    ->label('Order')
                    ->sortable()
                    ->alignCenter(),

                IconColumn::make('is_active')
                    ->label('Active')
                    ->boolean()
                    ->sortable()
                    ->alignCenter(),

                TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime('M j, Y')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('stage')
                    ->label('Stage')
                    ->options([
                        1 => 'Ofertare',
                        2 => 'Contractare',
                        3 => 'Pregatire',
                        4 => 'Aprovizionare',
                        5 => 'Executie',
                        6 => 'Livrare',
                        7 => 'Facturare',
                    ]),
                
                TernaryFilter::make('is_active')
                    ->label('Active')
                    ->placeholder('All')
                    ->trueLabel('Active only')
                    ->falseLabel('Inactive only'),
            ])
            ->recordActions([
                EditAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('stage')
            ->poll('30s');
    }
}
