<?php

namespace App\Filament\Definitions\Pages;

use App\Models\SystemSetting;
use Filament\Actions\Action;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Filament\Support\Exceptions\Halt;
use BackedEnum;

class DocumentSeriesSettings extends Page
{
    protected static string | BackedEnum | null $navigationIcon = 'heroicon-o-cog-6-tooth';
    protected string $view = 'filament.definitions.pages.document-series-settings';
    protected static ?string $title = 'Document Series Settings';
    protected static ?string $navigationLabel = 'Document Series';

    public ?array $data = [];

    public function mount(): void
    {
        $this->form->fill([
            'proforma_series' => SystemSetting::getDocumentSeries('proforma'),
            'invoice_series' => SystemSetting::getDocumentSeries('invoice'),
            'contract_series' => SystemSetting::getDocumentSeries('contract'),
            'guarantee_series' => SystemSetting::getDocumentSeries('guarantee'),
            'proces_verbal_series' => SystemSetting::getDocumentSeries('proces_verbal'),
            'bill_series' => SystemSetting::getDocumentSeries('bill'),
            'note_series' => SystemSetting::getDocumentSeries('note'),
        ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Document Series Configuration')
                    ->description('Configure the series prefixes for different document types. These will be used in document titles like "PROFORMA Seria CRMPRF 00001 / 21.10.2025"')
                    ->schema([
                        TextInput::make('proforma_series')
                            ->label('Proforma Series')
                            ->placeholder('e.g., CRMPRF')
                            ->maxLength(10)
                            ->required()
                            ->helperText('Series prefix for proforma invoices'),
                            
                        TextInput::make('invoice_series')
                            ->label('Invoice Series')
                            ->placeholder('e.g., CRMINV')
                            ->maxLength(10)
                            ->required()
                            ->helperText('Series prefix for invoices'),
                            
                        TextInput::make('contract_series')
                            ->label('Contract Series')
                            ->placeholder('e.g., CRMCTR')
                            ->maxLength(10)
                            ->required()
                            ->helperText('Series prefix for contracts'),
                            
                        TextInput::make('guarantee_series')
                            ->label('Guarantee Series')
                            ->placeholder('e.g., CRMGAR')
                            ->maxLength(10)
                            ->required()
                            ->helperText('Series prefix for guarantee certificates'),
                            
                        TextInput::make('proces_verbal_series')
                            ->label('Proces Verbal Series')
                            ->placeholder('e.g., CRMPV')
                            ->maxLength(10)
                            ->required()
                            ->helperText('Series prefix for proces verbal documents'),
                            
                        TextInput::make('bill_series')
                            ->label('Bill Series')
                            ->placeholder('e.g., CRMBILL')
                            ->maxLength(10)
                            ->required()
                            ->helperText('Series prefix for bills'),
                            
                        TextInput::make('note_series')
                            ->label('Note Series')
                            ->placeholder('e.g., CRMNOTE')
                            ->maxLength(10)
                            ->required()
                            ->helperText('Series prefix for notes'),
                    ])
                    ->columns(2),
            ])
            ->statePath('data');
    }

    protected function getFormActions(): array
    {
        return [
            Action::make('save')
                ->label('Save Settings')
                ->submit('save')
                ->keyBindings(['mod+s']),
        ];
    }

    public function save(): void
    {
        try {
            $data = $this->form->getState();
            
            // Save each series setting
            SystemSetting::setDocumentSeries('proforma', $data['proforma_series']);
            SystemSetting::setDocumentSeries('invoice', $data['invoice_series']);
            SystemSetting::setDocumentSeries('contract', $data['contract_series']);
            SystemSetting::setDocumentSeries('guarantee', $data['guarantee_series']);
            SystemSetting::setDocumentSeries('proces_verbal', $data['proces_verbal_series']);
            SystemSetting::setDocumentSeries('bill', $data['bill_series']);
            SystemSetting::setDocumentSeries('note', $data['note_series']);

            Notification::make()
                ->title('Settings Saved Successfully')
                ->body('Document series settings have been updated.')
                ->success()
                ->send();

        } catch (Halt $exception) {
            // Handle form validation errors
            return;
        } catch (\Exception $exception) {
            Notification::make()
                ->title('Error Saving Settings')
                ->body($exception->getMessage())
                ->danger()
                ->send();
        }
    }
}
