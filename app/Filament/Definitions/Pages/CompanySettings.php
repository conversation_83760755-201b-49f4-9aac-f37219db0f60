<?php

namespace App\Filament\Definitions\Pages;

use App\Models\SystemSetting;
use Filament\Schemas\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Select;
use Filament\Schemas\Components\Grid;
use Filament\Forms\Form;
use Filament\Pages\Page;
use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Config;
use BackedEnum;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Schemas\Schema;

class CompanySettings extends Page implements HasForms
{
    use InteractsWithForms;
    protected static BackedEnum | string | null $navigationIcon = 'heroicon-o-building-office';
    protected string $view = 'filament.definitions.pages.company-settings';
    protected static ?string $title = 'Company Settings';
    protected static ?string $navigationLabel = 'Company Settings';
    protected static \UnitEnum | string | null $navigationGroup = 'System Configuration';
    protected static ?int $navigationSort = 2;

    public ?array $data = [];

    public function mount(): void
    {
        $this->form->fill([
            'company_name' => SystemSetting::get('company_name', config('company.name')),
            'company_address' => SystemSetting::get('company_address', config('company.address')),
            'company_cui' => SystemSetting::get('company_cui', config('company.cui')),
            'company_reg_com' => SystemSetting::get('company_reg_com', config('company.reg_com')),
            'company_phone' => SystemSetting::get('company_phone', config('company.phone')),
            'company_email' => SystemSetting::get('company_email', config('company.email')),
            'company_website' => SystemSetting::get('company_website', config('company.website')),
            'company_bank_account' => SystemSetting::get('company_bank_account', config('company.bank_account')),
            'company_bank_name' => SystemSetting::get('company_bank_name', config('company.bank_name')),
            'default_currency' => SystemSetting::get('default_currency', config('company.default_currency')),
            'vat_rate' => SystemSetting::get('vat_rate', config('company.vat_rate')),
            'invoice_prefix' => SystemSetting::get('invoice_prefix', config('company.invoice_prefix')),
            'proforma_prefix' => SystemSetting::get('proforma_prefix', config('company.proforma_prefix')),
            'quote_prefix' => SystemSetting::get('quote_prefix', config('company.quote_prefix')),
            'company_capital' => SystemSetting::get('company_capital', config('company.legal.capital')),
            'company_director' => SystemSetting::get('company_director', config('company.legal.director')),
            'company_fiscal_code' => SystemSetting::get('company_fiscal_code', config('company.legal.fiscal_code')),
            'company_trade_register' => SystemSetting::get('company_trade_register', config('company.legal.trade_register')),
        ]);
    }

    public function form(Schema $schema): Schema
    {
        return $schema
            ->schema([
                Section::make('Company Information')
                    ->description('Basic company information that appears on documents')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextInput::make('company_name')
                                    ->label('Company Name')
                                    ->required()
                                    ->maxLength(255),
                                TextInput::make('company_cui')
                                    ->label('CUI (Tax ID)')
                                    ->maxLength(20),
                            ]),
                        Textarea::make('company_address')
                            ->label('Company Address')
                            ->rows(3)
                            ->maxLength(500),
                        Grid::make(2)
                            ->schema([
                                TextInput::make('company_reg_com')
                                    ->label('Registration Number (Reg. Com)')
                                    ->maxLength(50),
                                TextInput::make('company_phone')
                                    ->label('Phone Number')
                                    ->tel()
                                    ->maxLength(20),
                            ]),
                        Grid::make(2)
                            ->schema([
                                TextInput::make('company_email')
                                    ->label('Email Address')
                                    ->email()
                                    ->maxLength(255),
                                TextInput::make('company_website')
                                    ->label('Website')
                                    ->url()
                                    ->maxLength(255),
                            ]),
                    ])
                    ->columns(1),

                Section::make('Banking Information')
                    ->description('Bank account details for invoices')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextInput::make('company_bank_name')
                                    ->label('Bank Name')
                                    ->maxLength(255),
                                TextInput::make('company_bank_account')
                                    ->label('Bank Account Number')
                                    ->maxLength(50),
                            ]),
                    ])
                    ->columns(1),

                Section::make('Document Settings')
                    ->description('Default settings for document generation')
                    ->schema([
                        Grid::make(3)
                            ->schema([
                                Select::make('default_currency')
                                    ->label('Default Currency')
                                    ->options([
                                        'RON' => 'RON (Romanian Leu)',
                                        'EUR' => 'EUR (Euro)',
                                        'USD' => 'USD (US Dollar)',
                                        'GBP' => 'GBP (British Pound)',
                                    ])
                                    ->default('RON'),
                                TextInput::make('vat_rate')
                                    ->label('VAT Rate (%)')
                                    ->numeric()
                                    ->minValue(0)
                                    ->maxValue(100)
                                    ->default(19),
                                TextInput::make('invoice_prefix')
                                    ->label('Invoice Prefix')
                                    ->maxLength(10)
                                    ->default('INV'),
                            ]),
                        Grid::make(2)
                            ->schema([
                                TextInput::make('proforma_prefix')
                                    ->label('Proforma Prefix')
                                    ->maxLength(10)
                                    ->default('PRO'),
                                TextInput::make('quote_prefix')
                                    ->label('Quote Prefix')
                                    ->maxLength(10)
                                    ->default('QUO'),
                            ]),
                    ])
                    ->columns(1),

                Section::make('Legal Information')
                    ->description('Additional legal details')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextInput::make('company_capital')
                                    ->label('Share Capital')
                                    ->maxLength(100),
                                TextInput::make('company_director')
                                    ->label('Director Name')
                                    ->maxLength(255),
                            ]),
                        Grid::make(2)
                            ->schema([
                                TextInput::make('company_fiscal_code')
                                    ->label('Fiscal Code')
                                    ->maxLength(50),
                                TextInput::make('company_trade_register')
                                    ->label('Trade Register')
                                    ->maxLength(100),
                            ]),
                    ])
                    ->columns(1),
            ])
            ->statePath('data');
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('save')
                ->label('Save Settings')
                ->action('save'),
        ];
    }

    public function save(): void
    {
        $data = $this->form->getState();

        // Save company information
        SystemSetting::set('company_name', $data['company_name'], 'string', 'Company name');
        SystemSetting::set('company_address', $data['company_address'], 'string', 'Company address');
        SystemSetting::set('company_cui', $data['company_cui'], 'string', 'Company CUI');
        SystemSetting::set('company_reg_com', $data['company_reg_com'], 'string', 'Company registration number');
        SystemSetting::set('company_phone', $data['company_phone'], 'string', 'Company phone');
        SystemSetting::set('company_email', $data['company_email'], 'string', 'Company email');
        SystemSetting::set('company_website', $data['company_website'], 'string', 'Company website');
        SystemSetting::set('company_bank_account', $data['company_bank_account'], 'string', 'Company bank account');
        SystemSetting::set('company_bank_name', $data['company_bank_name'], 'string', 'Company bank name');

        // Save document settings
        SystemSetting::set('default_currency', $data['default_currency'], 'string', 'Default currency');
        SystemSetting::set('vat_rate', $data['vat_rate'], 'number', 'VAT rate percentage');
        SystemSetting::set('invoice_prefix', $data['invoice_prefix'], 'string', 'Invoice prefix');
        SystemSetting::set('proforma_prefix', $data['proforma_prefix'], 'string', 'Proforma prefix');
        SystemSetting::set('quote_prefix', $data['quote_prefix'], 'string', 'Quote prefix');

        // Save legal information
        SystemSetting::set('company_capital', $data['company_capital'], 'string', 'Company share capital');
        SystemSetting::set('company_director', $data['company_director'], 'string', 'Company director');
        SystemSetting::set('company_fiscal_code', $data['company_fiscal_code'], 'string', 'Company fiscal code');
        SystemSetting::set('company_trade_register', $data['company_trade_register'], 'string', 'Company trade register');

        Notification::make()
            ->title('Settings Saved')
            ->body('Company settings have been updated successfully.')
            ->success()
            ->send();
    }
}
