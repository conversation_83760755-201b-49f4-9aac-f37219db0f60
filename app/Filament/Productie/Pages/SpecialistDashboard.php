<?php

namespace App\Filament\Productie\Pages;

use App\Models\ComandaActivity;
use Filament\Pages\Page;
use BackedEnum;
use Filament\Support\Icons\Heroicon;
use Filament\Support\Enums\Width;

class SpecialistDashboard extends Page
{
    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedClipboardDocumentList;
    
    protected string $view = 'filament.productie.pages.specialist-dashboard';
    
    protected static ?string $title = 'Activitățile mele';
    
    protected static ?string $navigationLabel = 'Panou principal';
    
    public function getMaxContentWidth(): Width | string | null
    {
        return Width::Full;
    }
}
