<?php

namespace App\Filament\App\Pages;

use Filament\Pages\Page;
use App\Filament\App\Widgets\CalendarWidget;
use BackedEnum;

class Calendar extends Page
{
    protected static string | BackedEnum | null $navigationIcon = 'heroicon-o-calendar';

    protected string $view = 'filament.app.pages.calendar';

    protected static ?string $navigationLabel = 'Calendar';
    
    protected static \UnitEnum | string | null $navigationGroup = 'Orders & Calendar';

    protected static ?int $navigationSort = 2;

    protected function getHeaderWidgets(): array
    {
        return [
            CalendarWidget::class,
        ];
    }

    public function getTitle(): string
    {
        return 'Calendar';
    }
}

