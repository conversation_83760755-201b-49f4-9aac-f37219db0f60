<?php

namespace App\Filament\App\Pages;

use Filament\Pages\Page;
use Filament\Forms\Components\Select;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Schemas\Schema;
use App\Models\User;
use Livewire\Attributes\Reactive;

class EchoStatus extends Page implements HasForms
{
    use InteractsWithForms;

    protected static \BackedEnum | string | null $navigationIcon = 'heroicon-o-signal';

    protected string $view = 'filament.app.pages.echo-status';

    protected static ?string $navigationLabel = 'Echo Status';

    protected static ?string $title = 'Echo / Reverb Connection Status';

    protected static ?int $navigationSort = 999;

    protected static \UnitEnum | string | null $navigationGroup = 'System';

    public ?int $selectedUserId = null;

    public function mount(): void
    {
        // Default to current user
        $this->selectedUserId = auth()->id();
        $this->form->fill([
            'user_id' => $this->selectedUserId,
        ]);
    }

    public function form(Schema $schema): Schema
    {
        return $schema
            ->schema([
                Select::make('user_id')
                    ->label('Test as User')
                    ->options(User::where('is_active', true)->pluck('name', 'id'))
                    ->searchable()
                    ->live()
                    ->afterStateUpdated(function ($state) {
                        $this->selectedUserId = $state;
                        $this->dispatch('user-changed', userId: $state);
                    })
                    ->helperText('Select a user to test their Echo connection and send them test notifications'),
            ])
            ->statePath('data');
    }

    /**
     * Only superadmins can access this page
     */
    public static function canAccess(): bool
    {
        $user = auth()->user();

        if (!$user || !$user->is_active) {
            return false;
        }

        return $user->isRole('superadmin');
    }

    public function getTitle(): string
    {
        return 'Echo / Reverb Connection Status';
    }

    public function getSelectedUser(): ?User
    {
        return $this->selectedUserId ? User::find($this->selectedUserId) : null;
    }
}

