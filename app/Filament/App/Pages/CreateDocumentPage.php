<?php

namespace App\Filament\App\Pages;

use App\Models\Client;
use App\Models\Comanda;
use App\Models\DocumentTemplate;
use App\Models\GeneratedDocument;
use App\Models\SystemSetting;
use App\Services\DocumentGenerationService;
use BackedEnum;
use Filament\Actions\Action;
use Filament\Schemas\Schema;
use Filament\Forms\Components\DatePicker;
use Filament\Schemas\Components\Grid;
use Filament\Forms\Components\KeyValue;
use Filament\Forms\Components\Repeater;
use Filament\Schemas\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Filament\Support\Exceptions\Halt;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class CreateDocumentPage extends Page implements HasForms
{
    use InteractsWithForms;

    protected static string|BackedEnum|null $navigationIcon = 'heroicon-o-document-plus';
    protected string $view = 'filament.app.pages.create-document';
    protected static ?string $title = 'Adaugă un document nou';
    protected static ?string $navigationLabel = '+ Document';
    protected static \UnitEnum | string | null $navigationGroup = 'Documents';
    protected static ?int $navigationSort = 1;
    
    // Make the button stand out with success color
    public static function getNavigationBadge(): ?string
    {
        return 'NEW';
    }
    
    public static function getNavigationBadgeColor(): ?string
    {
        return 'success';
    }

    public ?array $data = [];
    public ?Comanda $selectedComanda = null;
    public ?array $comandaItems = [];
    public ?GeneratedDocument $duplicateDocument = null;
    public bool $showDuplicateModal = false;
    public ?int $contextComandaId = null; // Store the comanda_id from URL context

    public function mount(): void
    {
        $comandaId = request()->get('comanda_id');
        
        // Store comanda_id as a component property so it persists across Livewire updates
        $this->contextComandaId = $comandaId ? (int)$comandaId : null;
        
        if ($comandaId) {
            $comanda = Comanda::with(['client', 'items', 'generatedDocuments'])->find($comandaId);
            if ($comanda) {
                $this->selectedComanda = $comanda;
                
                // Check if comanda already has a quotation
                $hasQuotation = $comanda->generatedDocuments()
                    ->where('type', 'quotation')
                    ->exists();
                
                // Default to quotation, but use proforma if quotation already exists
                $defaultType = $hasQuotation ? 'proforma' : 'quotation';
                $defaultTemplate = $this->getOrCreateDefaultTemplate($defaultType);
                
                $items = $comanda->items->map(function ($item) {
                    return [
                        'name' => $item->name,
                        'description' => $item->description,
                        'unit' => $item->unit ?? 'buc',
                        'quantity' => $item->quantity,
                        'unit_price' => $item->unit_price,
                        'total_price' => $item->total_price,
                    ];
                })->toArray();
                
                $this->form->fill([
                    'source_type' => 'comanda',
                    'type' => $defaultType,
                    'template_id' => $defaultTemplate->id,
                    'title' => $this->generateDefaultTitle($defaultType),
                    'comanda_id' => $comanda->id,
                    'client_id' => $comanda->client_id,
                    'client_name' => $comanda->client->company_name ?: $comanda->client->name,
                    'client_cui' => $comanda->client->cui,
                    'client_reg_com' => $comanda->client->reg_com,
                    'client_address' => $comanda->client->address,
                    'client_email' => $comanda->client->email,
                    'client_phone' => $comanda->client->phone,
                    'items' => $items,
                ]);
                
                // Store items in component property as well
                $this->comandaItems = $items;
                $this->selectedComanda = $comanda;
            }
        } else {
            // Standalone mode - always default to quotation
            $defaultTemplate = $this->getOrCreateDefaultTemplate('quotation');
            
            $this->form->fill([
                'source_type' => 'standalone',
                'type' => 'quotation',
                'template_id' => $defaultTemplate->id,
                'title' => $this->generateDefaultTitle('quotation'),
            ]);
        }
    }

    public function form(Schema $schema): Schema
    {
        return $schema
            ->schema([
                // Header Section - Document Type & Template
                Section::make('')
                    ->description('')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                Select::make('type')
                                    ->label('Tipul documentului')
                                    ->options([
                                        'quotation' => 'Ofertă',
                                        'proforma' => 'Factură Proformă',
                                        // 'invoice' => 'Factură',
                                        // 'contract' => 'Contract',
                                        // Other types disabled for now
                                    ])
                                    ->default(function () {
                                        // If we have a comanda_id, check if it already has a quotation
                                        $comandaId = request()->get('comanda_id');
                                        if ($comandaId) {
                                            $hasQuotation = \App\Models\GeneratedDocument::where('comanda_id', $comandaId)
                                                ->where('type', 'quotation')
                                                ->exists();
                                            return $hasQuotation ? 'proforma' : 'quotation';
                                        }
                                        // Default to quotation when creating standalone
                                        return 'quotation';
                                    })
                                    ->required()
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, callable $set) {
                                        $set('template_id', null);
                                        $set('title', $this->generateDefaultTitle($state));
                                    }),

                                Select::make('template_id')
                                    ->label('Template')
                                    ->options(function (callable $get) {
                                        $type = $get('type');
                                        if (!$type) {
                                            return [];
                                        }
                                        
                                        $templates = DocumentTemplate::active()
                                            ->ofType($type)
                                            ->pluck('name', 'id')
                                            ->toArray();
                                        
                                        // If no templates available, return a default option
                                        if (empty($templates)) {
                                            return ['default' => 'Default Template'];
                                        }
                                        
                                        return $templates;
                                    })
                                    ->default(function (callable $get) {
                                        $type = $get('type');
                                        if (!$type) {
                                            return null;
                                        }
                                        
                                        // Try to get default template for this type
                                        $defaultTemplate = DocumentTemplate::active()
                                            ->ofType($type)
                                            ->default()
                                            ->first();
                                            
                                        if ($defaultTemplate) {
                                            return $defaultTemplate->id;
                                        }
                                        
                                        // If no default, get first available template
                                        $firstTemplate = DocumentTemplate::active()
                                            ->ofType($type)
                                            ->first();
                                            
                                        return $firstTemplate ? $firstTemplate->id : 'default';
                                    })
                                    ->required()
                                    ->reactive()
                                    ->hidden()
                                    ->dehydrated(), // Hide the template field but include it in form submission
                            ]),

                        TextInput::make('title')
                            ->label('Titlul documentului')
                            ->required()
                            ->maxLength(255)
                            ->reactive()
                            ->default(fn() => $this->generateDefaultTitle('proforma')),
                    ])
                    ->columns(1),

                // Document Source Selection - Dynamic based on document type
                Section::make(function (callable $get) {
                        return $get('type') === 'proforma' 
                            ? 'Tipul de factură proformă' 
                            : 'Sursa documentului';
                    })
                    ->description(function (callable $get) {
                        return $get('type') === 'proforma'
                            ? 'Alege tipul de plată (integrală sau parțială în avans)'
                            : 'Alege cum să creezi acest document';
                    })
                    ->schema([
                        // For PROFORMA: Different source options
                        Select::make('source_type')
                            ->label(fn (callable $get) => $get('type') === 'proforma' ? 'Tipul plății' : 'Tipul sursei')
                            ->options(function (callable $get) {
                                $type = $get('type');
                                $comandaId = $this->contextComandaId ?? request()->get('comanda_id');
                                
                                if ($type === 'proforma') {
                                    $options = [];
                                    
                                    if ($comandaId) {
                                        // IN COMANDA CONTEXT: Show comanda-related options
                                        
                                        // Option 1: Full payment from existing quotation (only if quotation exists for this comanda)
                                        $hasQuotation = \App\Models\GeneratedDocument::where('comanda_id', $comandaId)
                                            ->where('type', 'quotation')
                                            ->exists();
                                        if ($hasQuotation) {
                                            $options['full_from_quotation'] = 'Plată integrală bazată pe ofertă existentă';
                                        }
                                        
                                        // Option 2: Full payment from comanda
                                        $options['full_from_comanda'] = 'Plată integrală bazată pe comandă existentă';
                                        
                                        // Option 3: Advance payment from quotation (only if quotations exist for this comanda)
                                        if ($hasQuotation) {
                                            $options['advance_from_quotation'] = 'Plată avans % din ofertă existentă';
                                        }
                                    } else {
                                        // STANDALONE MODE: Only show standalone and comanda selection options
                                        $options['full_from_comanda'] = 'Plată integrală bazată pe comandă existentă';
                                        $options['standalone'] = 'Document nou, neatașat la o comandă';
                                    }
                                    
                                    return $options;
                                } else {
                                    // For other document types (quotation, etc.)
                                    $options = [
                                        'comanda' => 'Din comandă existentă',
                                    ];
                                    
                                    // Only show standalone option if NOT in comanda context
                                    if (!$comandaId) {
                                        $options['standalone'] = 'Creează un document nou, fără comandă';
                                    }
                                    
                                    return $options;
                                }
                            })
                            ->default(function (callable $get) {
                                $type = $get('type');
                                $comandaId = request()->get('comanda_id');
                                
                                if ($type === 'proforma' && $comandaId) {
                                    // Check if quotation exists for this comanda
                                    $hasQuotation = \App\Models\GeneratedDocument::where('comanda_id', $comandaId)
                                        ->where('type', 'quotation')
                                        ->exists();
                                    return $hasQuotation ? 'full_from_quotation' : 'full_from_comanda';
                                }
                                
                                return $type === 'proforma' ? 'full_from_comanda' : 'comanda';
                            })
                            ->required()
                            ->reactive()
                            ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                // Clear all fields when switching source type
                                $set('comanda_id', null);
                                $set('quotation_id', null);
                                $set('advance_percentage', null);
                                $set('client_id', null);
                                $set('client_name', null);
                                $set('client_cui', null);
                                $set('client_reg_com', null);
                                $set('client_address', null);
                                $set('client_email', null);
                                $set('client_phone', null);
                                $set('items', []);
                                $this->selectedComanda = null;
                                $this->comandaItems = [];
                            }),
                    ]),

                // Quotation Selection Section (for advance_from_quotation or full_from_quotation)
                Section::make('Alege oferta')
                    ->description('Alege oferta pe baza căreia creezi proforma')
                    ->schema([
                        Select::make('quotation_id')
                            ->label('Ofertă')
                            ->options(function (callable $get) {
                                // Get comanda_id from multiple sources (component property is most reliable)
                                $comandaId = $this->contextComandaId ?? $get('comanda_id');
                                
                                \Log::info('Quotation options called', [
                                    'source_type' => $get('source_type'),
                                    'context_comanda_id' => $this->contextComandaId,
                                    'comanda_id_from_get' => $get('comanda_id'),
                                    'final_comanda_id' => $comandaId
                                ]);
                                
                                $query = \App\Models\GeneratedDocument::where('type', 'quotation');
                                
                                // If we have a comanda context, ALWAYS filter by it
                                if ($comandaId) {
                                    $query->where('comanda_id', $comandaId);
                                    \Log::info('Filtering quotations by comanda_id', ['comanda_id' => $comandaId]);
                                }
                                
                                $quotations = $query->get();
                                \Log::info('Found quotations', ['count' => $quotations->count(), 'comanda_ids' => $quotations->pluck('comanda_id')->unique()->toArray()]);
                                
                                return $quotations->mapWithKeys(function ($quotation) {
                                    $comandaInfo = $quotation->comanda ? " ({$quotation->comanda->internal_number})" : '';
                                    return [
                                        $quotation->id => "{$quotation->document_number} - {$quotation->title}{$comandaInfo}"
                                    ];
                                });
                            })
                            ->searchable()
                            ->required(fn (callable $get) => in_array($get('source_type'), ['advance_from_quotation', 'full_from_quotation']))
                            ->reactive()
                            ->live()
                            ->afterStateHydrated(function ($state, callable $set) {
                                // Clear quotation selection when section first appears
                                $set('quotation_id', null);
                            })
                            ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                if ($state) {
                                    $quotation = \App\Models\GeneratedDocument::with(['comanda.client', 'comanda.items'])->find($state);
                                    if ($quotation && $quotation->comanda) {
                                        // Security check: if in comanda context, ensure quotation belongs to that comanda
                                        $contextComandaId = $get('comanda_id') ?? request()->get('comanda_id');
                                        if ($contextComandaId && $quotation->comanda_id != $contextComandaId) {
                                            // Quotation doesn't belong to this comanda - reset and show error
                                            $set('quotation_id', null);
                                            \Filament\Notifications\Notification::make()
                                                ->danger()
                                                ->title('Eroare')
                                                ->body('Această ofertă nu aparține comenzii curente.')
                                                ->send();
                                            return;
                                        }
                                        
                                        $comanda = $quotation->comanda;
                                        $this->selectedComanda = $comanda;
                                        $set('comanda_id', $comanda->id);
                                        $set('client_id', $comanda->client_id);
                                        $set('client_name', $comanda->client->company_name ?? $comanda->client->name);
                                        $set('client_cui', $comanda->client->cui);
                                        $set('client_reg_com', $comanda->client->reg_com);
                                        $set('client_address', $comanda->client->address);
                                        $set('client_email', $comanda->client->email);
                                        $set('client_phone', $comanda->client->phone);
                                        
                                        // Load items from quotation's generated data
                                        if (!empty($quotation->generated_data['items'])) {
                                            $set('items', $quotation->generated_data['items']);
                                            $this->comandaItems = $quotation->generated_data['items'];
                                        }
                                    }
                                }
                            }),
                    ])
                    ->visible(fn (callable $get) => in_array($get('source_type'), ['advance_from_quotation', 'full_from_quotation'])),

                // Advance Payment Percentage Section
                Section::make('Procent avans')
                    ->description('Specifică procentul pentru plata în avans')
                    ->schema([
                        TextInput::make('advance_percentage')
                            ->label('Procent avans (%)')
                            ->numeric()
                            ->required()
                            ->minValue(1)
                            ->maxValue(100)
                            ->default(50)
                            ->suffix('%')
                            ->helperText('Introdu procentul din valoarea totală a ofertei care va fi facturat în avans.')
                            ->reactive(),
                    ])
                    ->visible(fn (callable $get) => $get('source_type') === 'advance_from_quotation'),

                // Comanda Selection Section (for full_from_comanda or when source_type is 'comanda')
                Section::make('Alege comanda')
                    ->description(function () {
                        $comandaId = request()->get('comanda_id');
                        return $comandaId 
                            ? 'Comanda curentă (nu poate fi modificată în acest context)'
                            : 'Alege comanda pe baza căreia creezi documentul';
                    })
                    ->schema([
                        Select::make('comanda_id')
                            ->label('Comanda')
                            ->options(function (callable $get) {
                                // Get comanda_id from form state first, fallback to request
                                $comandaId = $get('comanda_id') ?? request()->get('comanda_id');
                                
                                // If in comanda context, only show the current comanda
                                $query = Comanda::with('client');
                                if ($comandaId) {
                                    $query->where('id', $comandaId);
                                }
                                
                                return $query->get()->mapWithKeys(function ($comanda) {
                                        $clientName = $comanda->client->company_name ?: $comanda->client->name;
                                        return [
                                            $comanda->id => "{$comanda->internal_number} - {$comanda->name} ({$clientName})"
                                        ];
                                    });
                            })
                            ->searchable()
                            ->disabled(function (callable $get) {
                                // Disable if comanda_id is already set in form or request
                                return ($get('comanda_id') ?? request()->get('comanda_id')) !== null;
                            })
                            ->dehydrated() // Force inclusion in form submission even when disabled
                            ->required(fn (callable $get) => in_array($get('source_type'), ['comanda', 'full_from_comanda']))
                            ->reactive()
                            ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                if ($state) {
                                    // Security check: if in comanda context, prevent changing to a different comanda
                                    $contextComandaId = request()->get('comanda_id');
                                    if ($contextComandaId && $state != $contextComandaId) {
                                        // Attempting to select a different comanda - reset and show error
                                        $set('comanda_id', $contextComandaId);
                                        \Filament\Notifications\Notification::make()
                                            ->danger()
                                            ->title('Eroare')
                                            ->body('Nu poți schimba comanda în acest context.')
                                            ->send();
                                        return;
                                    }
                                    
                                    $comanda = Comanda::with(['client', 'items'])->find($state);
                                    if ($comanda) {
                                        $this->selectedComanda = $comanda;
                                        $set('client_id', $comanda->client_id);
                                        $set('client_name', $comanda->client->company_name ?? $comanda->client->name);
                                        $set('client_cui', $comanda->client->cui);
                                        $set('client_reg_com', $comanda->client->reg_com);
                                        $set('client_address', $comanda->client->address);
                                        $set('client_email', $comanda->client->email);
                                        $set('client_phone', $comanda->client->phone);
                                        
                                        // Load comanda items and populate the form
                                        $items = $comanda->items->map(function ($item) {
                                            return [
                                                'name' => $item->name,
                                                'description' => $item->description,
                                                'unit' => $item->unit ?? 'buc',
                                                'quantity' => $item->quantity,
                                                'unit_price' => $item->unit_price,
                                                'total_price' => $item->total_price,
                                            ];
                                        })->toArray();
                                        
                                        $set('items', $items);
                                        $this->comandaItems = $items;
                                    }
                                }
                            }),
                    ])
                    ->visible(fn (callable $get) => in_array($get('source_type'), ['comanda', 'full_from_comanda'])),

                // Client Selection Section (only when source_type is 'standalone')
                Section::make('Alege clientul')
                    ->description('Alege clientul pentru care creezi documentul')
                    ->schema([
                        Select::make('client_id')
                            ->label('Client')
                            ->options(function () {
                                return Client::active()
                                    ->get()
                                    ->mapWithKeys(function ($client) {
                                        $displayName = $client->company_name ?: $client->name;
                                        return [
                                            $client->id => $displayName
                                        ];
                                    });
                            })
                            ->searchable()
                            ->required(fn (callable $get) => $get('source_type') === 'standalone')
                            ->reactive()
                            ->afterStateUpdated(function ($state, callable $set) {
                                if ($state) {
                                    $client = Client::find($state);
                                    if ($client) {
                                        $set('client_name', $client->company_name ?? $client->name);
                                        $set('client_cui', $client->cui);
                                        $set('client_reg_com', $client->reg_com);
                                        $set('client_address', $client->address);
                                        $set('client_email', $client->email);
                                        $set('client_phone', $client->phone);
                                    }
                                }
                            }),
                    ])
                    ->visible(fn (callable $get) => $get('source_type') === 'standalone'),

                // Client Information Section (Read-only, pulled from comanda or client)
                Section::make('Informații client')
                    ->description('Informații client (automat completate din comanda)')
                    ->schema([
                        Grid::make(3)
                            ->schema([
                                TextInput::make('client_name')
                                    ->label('Nume client')
                                    ->disabled()
                                    ->dehydrated(false),

                                TextInput::make('client_cui')
                                    ->label('CUI')
                                    ->disabled()
                                    ->dehydrated(false),

                                TextInput::make('client_reg_com')
                                    ->label('Reg. Com.')
                                    ->disabled()
                                    ->dehydrated(false),
                            ]),

                        Grid::make(2)
                            ->schema([
                                Textarea::make('client_address')
                                    ->label('Adresă')
                                    ->disabled()
                                    ->dehydrated(false)
                                    ->rows(2),

                                TextInput::make('client_email')
                                    ->label('Email')
                                    ->disabled()
                                    ->dehydrated(false),
                            ]),

                        TextInput::make('client_phone')
                            ->label('Telefon')
                            ->disabled()
                            ->dehydrated(false),
                    ])
                    ->visible(fn (callable $get) => !empty($get('comanda_id')) || !empty($get('client_id'))),

                // Document Items Section
                Section::make('')
                  //  ->description('Elementele din comanda selectata (pot fi modificate izolat aici)')
                    ->schema([
                        Repeater::make('items')
                            ->label('Elementele documentului')
                            
                            ->schema([
                                Grid::make(6)
                                    ->schema([
                                        TextInput::make('name')
                                            ->label('Descriere')
                                            ->required()
                                            ->columnSpan(3),

                                        TextInput::make('unit')
                                            ->label('Unitate')
                                            ->default('buc')
                                            ->required(),

                                        TextInput::make('quantity')
                                            ->label('Cantitate')
                                            ->numeric()
                                            ->required()
                                            ->default(1),

                                        TextInput::make('unit_price')
                                            ->label('Preț unitar')
                                            ->numeric()
                                            ->required()
                                            ->default(0)
                                            ->prefix('€'),

                                        TextInput::make('total_price')
                                            ->label('Preț total')
                                            ->numeric()
                                            ->required()
                                            ->default(0)
                                            ->prefix('€')
                                            ->disabled()
                                            ->dehydrated(false),
                                    ]),
                            ])
                            ->defaultItems(1)
                            ->addActionLabel('Adaugă element')
                            ->reorderable()
                            ->collapsible()
                            ->collapsed()
                            ->itemLabel(fn (array $state): ?string => $state['name'] ?? null)
                            ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                // Auto-calculate total price
                                $items = $get('items') ?? [];
                                foreach ($items as $index => $item) {
                                    if (isset($item['quantity']) && isset($item['unit_price'])) {
                                        $total = floatval($item['quantity']) * floatval($item['unit_price']);
                                        $set("items.{$index}.total_price", number_format($total, 2));
                                    }
                                }
                            }),
                    ])
                    ->visible(fn (callable $get) => !empty($get('comanda_id')) || !empty($get('client_id'))),

                // Custom Fields Section (for proforma-specific fields)
                                                                                                                                                                                                                                    
                Section::make('Termeni si condiții')
                    ->description('Termeni si condiții specifici pentru acest document')
                    ->schema([
                        Grid::make(2)
                            ->schema([


                                TextInput::make('delivery_terms')
                                    ->label('Termeni de livrare')
                                    ->default('Livrare în 7-14 zile lucrătoare')
                                    ->placeholder('Ex: Livrare în 7-14 zile lucrătoare'),
                            ]),

                        Grid::make(2)
                            ->schema([
                                TextInput::make('validity_period')
                                    ->label('Valabilitate (zile)')
                                    ->numeric()
                                    ->default(fn (callable $get) => $get('type') === 'quotation' ? 15 : 30)
                                    ->minValue(1)
                                    ->maxValue(365),

                                DatePicker::make('due_date')
                                    ->label('Data scadentă')
                                    ->placeholder('Ex: Data scadentă'),
                            ]),

                        Textarea::make('notes')
                            ->label('Observații suplimentare')
                            ->rows(3)
                            ->placeholder('Any additional notes or special conditions...'),
                    ])
                    ->visible(fn (callable $get) => in_array($get('type'), ['proforma', 'quotation'])),

                // Currency Selection (for quotation only)
                Section::make('Monedă')
                    ->description('Selectează moneda în care se va genera documentul')
                    ->schema([
                        Select::make('currency')
                            ->label('Monedă')
                            ->options([
                                'EUR' => 'EUR (€)',
                                'RON' => 'RON (Lei)',
                            ])
                            ->default('EUR')
                            ->required()
                            ->reactive()
                            ->helperText(function (callable $get) {
                                if ($get('currency') === 'RON') {
                                    $rate = \App\Models\SystemSetting::get('eur_to_ron_rate', config('company.eur_to_ron_rate', 5.0));
                                    return "Prețurile din comenzi (în EUR) vor fi convertite la RON folosind cursul: 1 EUR = {$rate} RON";
                                }
                                return 'Prețurile din comenzi (în EUR) vor rămâne în EUR';
                            }),
                    ])
                    ->visible(fn (callable $get) => $get('type') === 'quotation'),

                // Document Metadata
                // Section::make('')
                //     ->label('')
                //     ->schema([
                //         Textarea::make('description')
                //             ->label('Comentarii')
                //             ->rows(2)
                //             ->placeholder('Ex: Comentarii suplimentare'),
                //     ])
                //     ->collapsible()
                //     ->collapsed(),
            ])
            ->statePath('data');
    }

    protected function getFormActions(): array
    {
        return [
            Action::make('cancel')
                ->label('Anulează')
                ->color('gray')
                ->url(fn () => route('filament.app.resources.comandas.index'))
                ->keyBindings(['escape']),
                
            Action::make('create')
                ->label('Creează documentul')
                ->action('create')
                ->keyBindings(['mod+s']),
                
            Action::make('createAndGeneratePdf')
                ->label('Creează documentul și generează PDF')
                ->action('createAndGeneratePdf')
                ->color('success')
                ->icon('heroicon-o-document-arrow-down')
                ->keyBindings(['mod+shift+s']),
        ];
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('duplicateModal')
                ->modalHeading(fn () => $this->duplicateDocument ? 
                    ($this->duplicateDocument->type === 'quotation' ? 'Ofertă Duplicată' : 'Proforma Duplicată')
                    : 'Document Duplicat')
                ->modalDescription(fn () => $this->duplicateDocument ? 
                    "Exista deja " . ($this->duplicateDocument->type === 'quotation' ? 'o ofertă' : 'o factură proformă') . 
                    " pentru comanda cu aceste elemente (" . 
                    ($this->duplicateDocument->type === 'quotation' ? 'oferta' : 'proforma') . 
                    " cu numarul {$this->duplicateDocument->document_number} / {$this->duplicateDocument->created_at->format('d.m.Y')}). Modificati elementele comenzii." 
                    : '')
                ->modalSubmitAction(false)
                ->modalCancelActionLabel('Închide')
                ->modalCancelAction(fn () => $this->closeDuplicateModal())
                ->color('warning')
                ->icon('heroicon-o-exclamation-triangle')
                ->visible(fn () => $this->duplicateDocument !== null),
        ];
    }

    public function closeDuplicateModal(): void
    {
        $this->duplicateDocument = null;
    }

    public function create(): void
    {
        try {
            $data = $this->form->getState();
            
            \Log::info('=== CREATE DOCUMENT DEBUG ===', [
                'data_keys' => array_keys($data),
                'source_type' => $data['source_type'] ?? 'NOT SET',
                'type' => $data['type'] ?? 'NOT SET',
                'comanda_id_in_data' => isset($data['comanda_id']) ? $data['comanda_id'] : 'NOT SET',
                'contextComandaId' => $this->contextComandaId,
                'request_comanda_id' => request()->get('comanda_id'),
            ]);
            
            // Check for duplicate document (proforma or quotation) based on type
            if ($data['type'] === 'proforma') {
                \Log::info('Checking for duplicate proforma');
                $duplicateCheck = $this->checkForDuplicateProforma($data);
                if ($duplicateCheck['isDuplicate']) {
                    $this->duplicateDocument = $duplicateCheck['existingDocument'];
                    $this->mountAction('duplicateModal');
                    return;
                }
            } elseif ($data['type'] === 'quotation') {
                \Log::info('Checking for duplicate quotation');
                $duplicateCheck = $this->checkForDuplicateQuotation($data);
                \Log::info('Duplicate check result', $duplicateCheck);
                if ($duplicateCheck['isDuplicate']) {
                    $this->duplicateDocument = $duplicateCheck['existingDocument'];
                    $this->mountAction('duplicateModal');
                    return;
                }
            }
            
            // Get the template
            $template = null;
            if (!isset($data['template_id']) || $data['template_id'] === 'default') {
                // Use the helper method to get or create a default template
                $template = $this->getOrCreateDefaultTemplate($data['type']);
            } else {
                $template = DocumentTemplate::find($data['template_id']);
            }
            
            if (!$template) {
                throw new \Exception('Selected template not found.');
            }

            // Prepare custom fields
            $customFields = [];
            if (isset($data['payment_terms'])) $customFields['payment_terms'] = $data['payment_terms'];
            if (isset($data['delivery_terms'])) $customFields['delivery_terms'] = $data['delivery_terms'];
            if (isset($data['validity_period'])) $customFields['validity_period'] = $data['validity_period'];
            if (isset($data['notes'])) $customFields['notes'] = $data['notes'];
            if (isset($data['currency'])) $customFields['currency'] = $data['currency'];

            // Handle different source types
            $sourceType = $data['source_type'];
            
            // Special handling for proforma advance payment
            if ($sourceType === 'advance_from_quotation') {
                $quotation = GeneratedDocument::with(['comanda.client'])->find($data['quotation_id']);
                if (!$quotation) {
                    throw new \Exception('Selected quotation not found.');
                }
                
                // Process advance payment
                $advanceData = $this->processAdvancePayment($data, $quotation);
                
                // Create standalone document with advance payment item
                $document = GeneratedDocument::create([
                    'title' => $data['title'],
                    'type' => $data['type'],
                    'template_id' => $template->id,
                    'client_id' => $quotation->comanda->client_id,
                    'comanda_id' => $quotation->comanda_id,
                    'status' => 'draft',
                    'description' => $data['description'] ?? null,
                    'custom_field_values' => $customFields,
                    'generated_data' => [
                        'items' => $advanceData['items'],
                        'client_data' => [
                            'name' => $quotation->comanda->client->company_name ?? $quotation->comanda->client->name,
                            'cui' => $quotation->comanda->client->cui,
                            'reg_com' => $quotation->comanda->client->reg_com,
                            'address' => $quotation->comanda->client->address,
                            'email' => $quotation->comanda->client->email,
                            'phone' => $quotation->comanda->client->phone,
                        ],
                    ],
                    'is_advance_payment' => true,
                    'advance_payment_percentage' => $advanceData['advance_payment_percentage'],
                    'source_quotation_id' => $advanceData['source_quotation_id'],
                    'generated_by' => auth()->id(),
                ]);
            } elseif ($sourceType === 'full_from_quotation') {
                // Full payment from quotation
                $quotation = GeneratedDocument::with(['comanda.client'])->find($data['quotation_id']);
                if (!$quotation) {
                    throw new \Exception('Selected quotation not found.');
                }
                
                $comanda = $quotation->comanda;
                $documentGenerationService = app(DocumentGenerationService::class);
                $document = $documentGenerationService->generateDocument(
                    comanda: $comanda,
                    template: $template,
                    customFields: $customFields,
                    title: $data['title']
                );
                
                // Link to source quotation
                $document->update(['source_quotation_id' => $quotation->id]);
            } elseif (in_array($sourceType, ['comanda', 'full_from_comanda'])) {
                // Get comanda_id from multiple sources
                $comandaId = $data['comanda_id'] ?? $this->contextComandaId ?? request()->get('comanda_id');
                
                \Log::info('Getting comanda for document', [
                    'source_type' => $sourceType,
                    'comanda_id_from_data' => $data['comanda_id'] ?? 'NOT SET',
                    'contextComandaId' => $this->contextComandaId,
                    'final_comanda_id' => $comandaId
                ]);
                
                if (!$comandaId) {
                    throw new \Exception('No order selected or found in context.');
                }
                
                // Get the selected comanda
                $comanda = Comanda::with(['client', 'items'])->find($comandaId);
                
                if (!$comanda) {
                    throw new \Exception('Selected order not found.');
                }

                // Create the document using the service
                $documentGenerationService = app(DocumentGenerationService::class);
                $document = $documentGenerationService->generateDocument(
                    comanda: $comanda,
                    template: $template,
                    customFields: $customFields,
                    title: $data['title']
                );
            } else {
                // Standalone mode - create document without comanda
                $client = Client::find($data['client_id']);
                
                if (!$client) {
                    throw new \Exception('Selected client not found.');
                }

                // Create a standalone document
                $document = GeneratedDocument::create([
                    'title' => $data['title'],
                    'type' => $data['type'],
                    'template_id' => $template->id,
                    'client_id' => $client->id,
                    'comanda_id' => null, // No comanda for standalone documents
                    'status' => 'draft',
                    'description' => $data['description'] ?? null,
                    'custom_field_values' => $customFields,
                    'generated_data' => [
                        'items' => $data['items'] ?? [],
                        'client_data' => [
                            'name' => $data['client_name'] ?? null,
                            'cui' => $data['client_cui'] ?? null,
                            'reg_com' => $data['client_reg_com'] ?? null,
                            'address' => $data['client_address'] ?? null,
                            'email' => $data['client_email'] ?? null,
                            'phone' => $data['client_phone'] ?? null,
                        ],
                    ],
                    'generated_by' => auth()->id(),
                ]);
            }

            // Update document with additional data
            $document->update([
                'description' => $data['description'] ?? null,
                'status' => 'draft',
            ]);

            // Generate HTML content for standalone documents or advance payment
            if (in_array($sourceType, ['standalone', 'advance_from_quotation'])) {
                $htmlContent = $this->generateStandaloneHtml($document, $data);
                $document->update(['html_content' => $htmlContent]);
            }

            Notification::make()
                ->title('Document Created Successfully')
                ->body("Document '{$document->title}' has been created.")
                ->success()
                ->send();

            // Redirect to the document view
            $this->redirect(route('filament.app.resources.documents.view', $document));

        } catch (Halt $exception) {
            // Handle form validation errors
            return;
        } catch (\Exception $exception) {
            Notification::make()
                ->title('Error Creating Document')
                ->body($exception->getMessage())
                ->danger()
                ->send();
        }
    }

    public function createAndGeneratePdf(): void
    {
        try {
            $data = $this->form->getState();
            
            // Check for duplicate document (proforma or quotation) based on type
            if ($data['type'] === 'proforma') {
                $duplicateCheck = $this->checkForDuplicateProforma($data);
                if ($duplicateCheck['isDuplicate']) {
                    $this->duplicateDocument = $duplicateCheck['existingDocument'];
                    $this->mountAction('duplicateModal');
                    return;
                }
            } elseif ($data['type'] === 'quotation') {
                $duplicateCheck = $this->checkForDuplicateQuotation($data);
                if ($duplicateCheck['isDuplicate']) {
                    $this->duplicateDocument = $duplicateCheck['existingDocument'];
                    $this->mountAction('duplicateModal');
                    return;
                }
            }
            
            // Get the template
            $template = null;
            if (!isset($data['template_id']) || $data['template_id'] === 'default') {
                // Use the helper method to get or create a default template
                $template = $this->getOrCreateDefaultTemplate($data['type']);
            } else {
                $template = DocumentTemplate::find($data['template_id']);
            }
            
            if (!$template) {
                throw new \Exception('Selected template not found.');
            }

            // Prepare custom fields
            $customFields = [];
            if (isset($data['payment_terms'])) $customFields['payment_terms'] = $data['payment_terms'];
            if (isset($data['delivery_terms'])) $customFields['delivery_terms'] = $data['delivery_terms'];
            if (isset($data['validity_period'])) $customFields['validity_period'] = $data['validity_period'];
            if (isset($data['notes'])) $customFields['notes'] = $data['notes'];
            if (isset($data['currency'])) $customFields['currency'] = $data['currency'];

            // Handle different source types
            $sourceType = $data['source_type'];
            
            // Special handling for proforma advance payment
            if ($sourceType === 'advance_from_quotation') {
                $quotation = GeneratedDocument::with(['comanda.client'])->find($data['quotation_id']);
                if (!$quotation) {
                    throw new \Exception('Selected quotation not found.');
                }
                
                // Process advance payment
                $advanceData = $this->processAdvancePayment($data, $quotation);
                
                // Create standalone document with advance payment item
                $document = GeneratedDocument::create([
                    'title' => $data['title'],
                    'type' => $data['type'],
                    'template_id' => $template->id,
                    'client_id' => $quotation->comanda->client_id,
                    'comanda_id' => $quotation->comanda_id,
                    'status' => 'draft',
                    'description' => $data['description'] ?? null,
                    'custom_field_values' => $customFields,
                    'generated_data' => [
                        'items' => $advanceData['items'],
                        'client_data' => [
                            'name' => $quotation->comanda->client->company_name ?? $quotation->comanda->client->name,
                            'cui' => $quotation->comanda->client->cui,
                            'reg_com' => $quotation->comanda->client->reg_com,
                            'address' => $quotation->comanda->client->address,
                            'email' => $quotation->comanda->client->email,
                            'phone' => $quotation->comanda->client->phone,
                        ],
                    ],
                    'is_advance_payment' => true,
                    'advance_payment_percentage' => $advanceData['advance_payment_percentage'],
                    'source_quotation_id' => $advanceData['source_quotation_id'],
                    'generated_by' => auth()->id(),
                ]);
            } elseif ($sourceType === 'full_from_quotation') {
                // Full payment from quotation
                $quotation = GeneratedDocument::with(['comanda.client'])->find($data['quotation_id']);
                if (!$quotation) {
                    throw new \Exception('Selected quotation not found.');
                }
                
                $comanda = $quotation->comanda;
                $documentGenerationService = app(DocumentGenerationService::class);
                $document = $documentGenerationService->generateDocument(
                    comanda: $comanda,
                    template: $template,
                    customFields: $customFields,
                    title: $data['title']
                );
                
                // Link to source quotation
                $document->update(['source_quotation_id' => $quotation->id]);
            } elseif (in_array($sourceType, ['comanda', 'full_from_comanda'])) {
                // Get comanda_id from multiple sources
                $comandaId = $data['comanda_id'] ?? $this->contextComandaId ?? request()->get('comanda_id');
                
                \Log::info('Getting comanda for document', [
                    'source_type' => $sourceType,
                    'comanda_id_from_data' => $data['comanda_id'] ?? 'NOT SET',
                    'contextComandaId' => $this->contextComandaId,
                    'final_comanda_id' => $comandaId
                ]);
                
                if (!$comandaId) {
                    throw new \Exception('No order selected or found in context.');
                }
                
                // Get the selected comanda
                $comanda = Comanda::with(['client', 'items'])->find($comandaId);
                
                if (!$comanda) {
                    throw new \Exception('Selected order not found.');
                }

                // Create the document using the service
                $documentGenerationService = app(DocumentGenerationService::class);
                $document = $documentGenerationService->generateDocument(
                    comanda: $comanda,
                    template: $template,
                    customFields: $customFields,
                    title: $data['title']
                );
            } else {
                // Standalone mode - create document without comanda
                $client = Client::find($data['client_id']);
                
                if (!$client) {
                    throw new \Exception('Selected client not found.');
                }

                // Create a standalone document
                $document = GeneratedDocument::create([
                    'title' => $data['title'],
                    'type' => $data['type'],
                    'template_id' => $template->id,
                    'client_id' => $client->id,
                    'comanda_id' => null, // No comanda for standalone documents
                    'status' => 'draft',
                    'description' => $data['description'] ?? null,
                    'custom_field_values' => $customFields,
                    'generated_data' => [
                        'items' => $data['items'] ?? [],
                        'client_data' => [
                            'name' => $data['client_name'] ?? null,
                            'cui' => $data['client_cui'] ?? null,
                            'reg_com' => $data['client_reg_com'] ?? null,
                            'address' => $data['client_address'] ?? null,
                            'email' => $data['client_email'] ?? null,
                            'phone' => $data['client_phone'] ?? null,
                        ],
                    ],
                    'generated_by' => auth()->id(),
                ]);
            }

            // Update document with additional data
            $document->update([
                'description' => $data['description'] ?? null,
                'status' => 'generated', // Set to generated since we're creating PDF
            ]);

            // Generate PDF immediately
            try {
                $documentGenerationService = app(DocumentGenerationService::class);
                
                // For standalone documents or advance payment, we need to generate HTML content first
                if (in_array($sourceType, ['standalone', 'advance_from_quotation'])) {
                    $htmlContent = $this->generateStandaloneHtml($document, $data);
                    $document->update(['html_content' => $htmlContent]);
                }
                
                $pdfPath = $documentGenerationService->generatePdf($document);
                
                // Get PDF size from storage
                $pdfSize = Storage::disk('local')->size($pdfPath);
                
                // Update document with PDF info
                $document->markAsGenerated($pdfPath, $pdfSize);
                
                Notification::make()
                    ->title('Document Created and PDF Generated Successfully')
                    ->body("Document '{$document->title}' has been created and PDF generated.")
                    ->success()
                    ->send();

                // Trigger automatic download using JavaScript
                $this->dispatch('download-pdf', url: $document->getDownloadUrl());
            } catch (\Exception $pdfException) {
                // Document was created but PDF generation failed
                Notification::make()
                    ->title('Document Created Successfully')
                    ->body("Document '{$document->title}' has been created, but PDF generation failed: " . $pdfException->getMessage())
                    ->warning()
                    ->send();
            }

            // Redirect to the document view
            $this->redirect(route('filament.app.resources.documents.view', $document));

        } catch (Halt $exception) {
            // Handle form validation errors
            return;
        } catch (\Exception $exception) {
            Notification::make()
                ->title('Error Creating Document')
                ->body($exception->getMessage())
                ->danger()
                ->send();
        }
    }

    /**
     * Process advance payment data and return modified items/data
     */
    private function processAdvancePayment(array $data, $quotation): array
    {
        $percentage = $data['advance_percentage'] ?? 50;
        $quotationData = $quotation->generated_data;
        
        // Calculate total value from quotation items (in EUR)
        $totalValueEur = 0;
        if (!empty($quotationData['items'])) {
            foreach ($quotationData['items'] as $item) {
                $totalValueEur += $item['total_price'] ?? 0;
            }
        }
        
        // IMPORTANT: For proforma advance payments, we need to:
        // 1. First convert the total quotation value from EUR to RON
        // 2. Then calculate the percentage
        // This is because proforma is ALWAYS in RON and rounding matters
        \Log::info('Total value in EUR: ' . $totalValueEur);
        $exchangeRate = \App\Models\SystemSetting::get('eur_to_ron_rate', config('company.eur_to_ron_rate', 5.0));
        $totalValueRon = $totalValueEur * $exchangeRate;
        \Log::info('Total value in RON: ' . $totalValueRon);
        // Calculate advance amount in RON
        $advanceAmountRon = ($totalValueRon * $percentage) / 100;
        
        // Convert back to EUR for storage (the proforma template will convert it back to RON for display)
        $advanceAmountEur = $advanceAmountRon / $exchangeRate;
        \Log::info('Advance amount in EUR: ' . $advanceAmountEur);
        // Format the quotation reference nicely
        $quotationRef = $quotation->document_number;
        
        // Create a single item for the advance payment
        // Store in EUR, but calculated from RON to preserve rounding
        $advanceItem = [
            'name' => "Avans {$percentage}%",
            'description' => "Plată avans {$percentage}% conform ofertei nr. {$quotationRef} din data de {$quotation->created_at->format('d.m.Y')}",
            'unit' => 'serviciu',
            'quantity' => 1,
            'unit_price' => $advanceAmountEur,
            'total_price' => $advanceAmountEur,
        ];
        
  

        return [
            'items' => [$advanceItem],
            'is_advance_payment' => true,
            'advance_payment_percentage' => $percentage,
            'source_quotation_id' => $quotation->id,
        ];
    }

    /**
     * Generate HTML content for standalone documents
     */
    private function generateStandaloneHtml(GeneratedDocument $document, array $data): string
    {
        $template = $document->template;
        $client = $document->client;
        
        $viewData = [
            'document' => $document,
            'template' => $template,
            'comanda' => null, // No comanda for standalone documents
            'client' => $client,
            'custom_fields' => $document->custom_field_values ?? [],
            'document_title' => $document->title,
            'items' => $data['items'] ?? [],
            'client_data' => [
                'name' => $data['client_name'] ?? null,
                'cui' => $data['client_cui'] ?? null,
                'reg_com' => $data['client_reg_com'] ?? null,
                'address' => $data['client_address'] ?? null,
                'email' => $data['client_email'] ?? null,
                'phone' => $data['client_phone'] ?? null,
            ],
        ];
        
        // Check if template view exists, otherwise use default
        $templateView = "documents.{$template->blade_template}";
        if (!\View::exists($templateView)) {
            $templateView = 'documents.default';
        }
        
        return view($templateView, $viewData)->render();
    }

    private function getOrCreateDefaultTemplate(string $type): DocumentTemplate
    {
        // Try to get existing default template
        $template = DocumentTemplate::active()
            ->ofType($type)
            ->default()
            ->first();
            
        if ($template) {
            return $template;
        }
        
        // Try to get any active template of this type
        $template = DocumentTemplate::active()
            ->ofType($type)
            ->first();
            
        if ($template) {
            return $template;
        }
        
        // Create a default template if none exists
        $template = DocumentTemplate::create([
            'name' => 'Default ' . ucfirst($type) . ' Template',
            'type' => $type,
            'blade_template' => $type,
            'is_active' => true,
            'is_default' => true,
            'created_by' => auth()->id(),
            'required_fields' => ['client_id'],
            'optional_fields' => ['payment_terms', 'delivery_terms', 'validity_period'],
            'custom_fields' => [
                'payment_terms' => [
                    'type' => 'text',
                    'label' => 'Termeni de plată',
                    'default' => 'Plata în avans 100%'
                ],
                'delivery_terms' => [
                    'type' => 'text',
                    'label' => 'Termeni de livrare',
                    'default' => 'Livrare în 7-14 zile lucrătoare'
                ],
                'validity_period' => [
                    'type' => 'number',
                    'label' => 'Valabilitate (zile)',
                    'default' => 30
                ],
                'notes' => [
                    'type' => 'textarea',
                    'label' => 'Observații',
                    'default' => ''
                ]
            ],
        ]);
        
        return $template;
    }

    private function generateDefaultTitle(string $type): string
    {
        // Get the next document number for this type
        $nextNumber = $this->getNextDocumentNumber($type);
        
        // Format: "PROFORMĂ CRMQUO-20250001 / 28.10.2025"
        $typeLabel = match($type) {
            'proforma' => 'PROFORMĂ',
            'quotation' => 'OFERTĂ',
            'invoice' => 'FACTURĂ',
            'contract' => 'CONTRACT',
            default => strtoupper($type),
        };
        $today = now()->format('d.m.Y');
        
        return "{$typeLabel} {$nextNumber} / {$today}";
    }

    private function getNextDocumentNumber(string $type): string
    {
        // Create a temporary model instance to use its generateDocumentNumber method
        $tempDoc = new GeneratedDocument(['type' => $type]);
        return $tempDoc->generateDocumentNumber();
    }

    public function loadComandaItems(): void
    {
        $comandaId = $this->data['comanda_id'] ?? null;
        
        if ($comandaId) {
            $comanda = Comanda::with('items')->find($comandaId);
            if ($comanda) {
                $this->comandaItems = $comanda->items->map(function ($item) {
                    return [
                        'name' => $item->name,
                        'description' => $item->description,
                        'unit' => $item->unit ?? 'buc',
                        'quantity' => $item->quantity,
                        'unit_price' => $item->unit_price,
                        'total_price' => $item->total_price,
                    ];
                })->toArray();
                
                $this->form->fill([
                    'items' => $this->comandaItems
                ]);
            }
        }
    }

    /**
     * Check for duplicate proforma with the same items
     */
    private function checkForDuplicateProforma(array $data): array
    {
        $items = $data['items'] ?? [];
        
        if (empty($items)) {
            return ['isDuplicate' => false, 'existingDocument' => null];
        }

        // Get all existing proformas
        $existingProformas = GeneratedDocument::where('type', 'proforma')
            ->whereNotNull('generated_data')
            ->get();

        foreach ($existingProformas as $proforma) {
            $existingItems = $proforma->generated_data['items'] ?? [];
            
            if ($this->areItemsIdentical($items, $existingItems)) {
                return [
                    'isDuplicate' => true,
                    'existingDocument' => $proforma
                ];
            }
        }

        return ['isDuplicate' => false, 'existingDocument' => null];
    }

    private function checkForDuplicateQuotation(array $data): array
    {
        $sourceType = $data['source_type'] ?? null;
        
        \Log::info('=== DUPLICATE QUOTATION CHECK ===', [
            'source_type' => $sourceType,
            'data_has_comanda_id' => isset($data['comanda_id']),
            'data_comanda_id_value' => $data['comanda_id'] ?? 'NOT SET',
            'contextComandaId' => $this->contextComandaId,
            'request_comanda_id' => request()->get('comanda_id'),
        ]);
        
        // For standalone quotations, don't check for duplicates (multiple standalone quotes are allowed)
        if ($sourceType === 'standalone') {
            \Log::info('Standalone quotation - skipping duplicate check');
            return ['isDuplicate' => false, 'existingDocument' => null];
        }

        // Try to get comanda_id from multiple sources
        $comandaId = null;
        
        // 1. From the form data directly
        if (isset($data['comanda_id']) && !empty($data['comanda_id'])) {
            $comandaId = $data['comanda_id'];
            \Log::info('Got comanda_id from form data', ['comanda_id' => $comandaId]);
        }
        
        // 2. From context (URL parameter)
        if (!$comandaId && $this->contextComandaId) {
            $comandaId = $this->contextComandaId;
            \Log::info('Got comanda_id from context', ['comanda_id' => $comandaId]);
        }
        
        // 3. From request
        if (!$comandaId && request()->get('comanda_id')) {
            $comandaId = request()->get('comanda_id');
            \Log::info('Got comanda_id from request', ['comanda_id' => $comandaId]);
        }
        
        // If still no comanda_id, can't check for duplicates
        if (!$comandaId) {
            \Log::info('No comanda_id found - skipping duplicate check');
            return ['isDuplicate' => false, 'existingDocument' => null];
        }

        \Log::info('Checking database for existing quotation', ['comanda_id' => $comandaId]);
        
        // Check if this comanda already has a quotation
        $existingQuotation = GeneratedDocument::where('type', 'quotation')
            ->where('comanda_id', $comandaId)
            ->first();

        if ($existingQuotation) {
            \Log::info('Found existing quotation', ['document_id' => $existingQuotation->id]);
            return [
                'isDuplicate' => true,
                'existingDocument' => $existingQuotation
            ];
        }

        \Log::info('No existing quotation found');
        return ['isDuplicate' => false, 'existingDocument' => null];
    }

    /**
     * Compare two item arrays to see if they are identical
     */
    private function areItemsIdentical(array $items1, array $items2): bool
    {
        if (count($items1) !== count($items2)) {
            return false;
        }

        // Sort both arrays by name to ensure consistent comparison
        $sortedItems1 = collect($items1)->sortBy('name')->values()->toArray();
        $sortedItems2 = collect($items2)->sortBy('name')->values()->toArray();

        foreach ($sortedItems1 as $index => $item1) {
            $item2 = $sortedItems2[$index] ?? null;
            
            if (!$item2) {
                return false;
            }

            // Compare key fields (name, quantity, unit_price)
            if ($item1['name'] !== $item2['name'] ||
                $item1['quantity'] != $item2['quantity'] ||
                $item1['unit_price'] != $item2['unit_price']) {
                return false;
            }
        }

        return true;
    }

}
