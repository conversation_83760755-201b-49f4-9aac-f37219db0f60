<?php

namespace App\Filament\App\Resources\Clients\Schemas;

use App\Services\InfocuiService;
use Filament\Actions\Action;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;
use Illuminate\Support\Facades\Log;

class ClientForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make('Tip client')
                    ->schema([
                        Radio::make('client_type')
                            ->options([
                                'company' => 'Persoană juridică',
                                'individual' => 'Persoană fizică',
                            ])
                            ->default('company')
                            ->reactive()
                            ->afterStateUpdated(function ($state, callable $set) {
                                if ($state === 'company') {
                                    $set('name', null);
                                } else {
                                    $set('company_name', null);
                                    $set('cui', null);
                                    $set('reg_com', null);
                                }
                            }),
                    ])
                    ->columns(2)
                    ->columnSpanFull()
                    ->collapsible()
                    ->collapsed(true),

                Section::make('Informații de bază')
                    ->schema([
                        TextInput::make('company_name')
                            ->label('Nume companie')
                            ->visible(fn ($get) => $get('client_type') === 'company')
                            ->required(fn ($get) => $get('client_type') === 'company')
                            ->columnSpanFull()
                            ->maxLength(255),

                        TextInput::make('name')
                            ->label('Nume persoană fizică')
                            ->visible(fn ($get) => $get('client_type') === 'individual')
                            ->required(fn ($get) => $get('client_type') === 'individual')
                            ->columnSpanFull()
                            ->maxLength(255),

                        TextInput::make('cui')
                            ->label('CUI (Tax ID)')
                            ->visible(fn ($get) => $get('client_type') === 'company')
                            ->placeholder('12345678')
                            ->helperText('Introdu CUI fără prefix RO')
                            ->prefix(fn ($get) => !empty($get('tva')) ? 'RO' : null)
                            ->maxLength(20)
                            ->reactive()
                            ->suffixAction(
                                Action::make('lookup')
                                    ->icon('heroicon-m-magnifying-glass')
                                    ->tooltip('Lookup company data from Infocui')
                                    ->action(function ($get, $set) {
                                        $cui = $get('cui');
                                        
                                        if (empty($cui)) {
                                            \Filament\Notifications\Notification::make()
                                                ->title('Error')
                                                ->body('Please enter a CUI first')
                                                ->danger()
                                                ->send();
                                            return;
                                        }
                                        
                                        try {
                                            $service = new InfocuiService();
                                            $data = $service->getCompanyInfo($cui);
                                            
                                            if ($data === null) {
                                                \Filament\Notifications\Notification::make()
                                                    ->title('Not Found')
                                                    ->body('No company found with this CUI')
                                                    ->warning()
                                                    ->send();
                                                return;
                                            }
                                            
                                            // Populate fields only if they are empty
                                            if (empty($get('company_name'))) {
                                                $set('company_name', $data['company_name'] ?? null);
                                            }
                                            if (empty($get('reg_com'))) {
                                                $set('reg_com', $data['reg_com'] ?? null);
                                            }
                                            if (empty($get('address'))) {
                                                $set('address', $data['address'] ?? null);
                                            }
                                            if (empty($get('phone'))) {
                                                $set('phone', $data['phone'] ?? null);
                                            }
                                            if (empty($get('tva'))) {
                                                $set('tva', $data['tva'] ?? null);
                                            }
                                            if (empty($get('notes'))) {
                                                $set('notes', $data['notes'] ?? null);
                                            }
                                            
                                            // Log TVA for debugging
                                            Log::info('Infocui lookup completed', [
                                                'cui' => $cui,
                                                'tva_from_api' => $data['tva'] ?? 'null',
                                                'tva_in_form' => $get('tva'),
                                            ]);
                                            
                                            \Filament\Notifications\Notification::make()
                                                ->title('Success')
                                                ->body('Company data loaded successfully' . ($data['tva'] ? ' (TVA registered)' : ''))
                                                ->success()
                                                ->send();
                                        } catch (\Exception $e) {
                                            Log::error('Infocui lookup failed', ['error' => $e->getMessage()]);
                                            \Filament\Notifications\Notification::make()
                                                ->title('Error')
                                                ->body('Failed to lookup company data: ' . $e->getMessage())
                                                ->danger()
                                                ->send();
                                        }
                                    })
                            ),

                        TextInput::make('reg_com')
                            ->label('Reg. Com.')
                            ->visible(fn ($get) => $get('client_type') === 'company')
                            ->placeholder('J40/1234/2024')
                            ->columnSpan(2)
                            ->maxLength(50),

                        Textarea::make('address')
                            ->required()
                            ->rows(3)
                            ->columnSpan(3),
                    ])
                    ->columns(3),

                Section::make('Informații de contact')
                    ->schema([
                        TextInput::make('email')
                            ->label('Email Address')
                            ->email()
                            ->maxLength(255),

                        TextInput::make('phone')
                            ->tel()
                            ->maxLength(20),

                        TextInput::make('contact_person')
                            ->label('Persoană de contact')
                            ->maxLength(255),

                        TextInput::make('contact_email')
                            ->label('Email de contact')
                            ->email()
                            ->maxLength(255),

                        TextInput::make('contact_phone')
                            ->label('Telefon de contact')
                            ->tel()
                            ->maxLength(20),
                    ])
                    ->columns(2),

                Section::make('Detalii bancare')
                    ->schema([
                        TextInput::make('bank_name')
                            ->label('Nume bancă')
                            ->maxLength(255),

                        TextInput::make('bank_account')
                            ->label('Cont bancar')
                            ->maxLength(50),
                    ])
                    ->columns(2)
                    ->collapsible(),

                Section::make('Informații suplimentare')
                    ->schema([
                        Textarea::make('notes')
                            ->label('Note')
                            ->rows(3)
                            ->columnSpanFull(),

                        Toggle::make('is_active')
                            ->label('Activ')
                            ->default(true),
                    ])
                    ->collapsible(),
            ]);
    }
}
