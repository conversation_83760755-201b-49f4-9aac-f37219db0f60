<?php

namespace App\Filament\App\Resources\Clients\Tables;

use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;

class ClientsTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('client_type')
                    ->label('Type')
                    ->getStateUsing(fn ($record) => $record->company_name ? 'Company' : 'Individual')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'Company' => 'primary',
                        'Individual' => 'secondary',
                    }),

                TextColumn::make('display_name')
                    ->label('Name')
                    ->getStateUsing(fn ($record) => $record->company_name ?: $record->name)
                    ->searchable(['company_name', 'name'])
                    ->sortable(),

                TextColumn::make('cui')
                    ->label('CUI')
                    ->searchable()
                    ->toggleable()
                    ->placeholder('—'),

                TextColumn::make('email')
                    ->label('Email')
                    ->searchable()
                    ->copyable()
                    ->placeholder('—'),

                TextColumn::make('phone')
                    ->searchable()
                    ->copyable()
                    ->placeholder('—'),

                TextColumn::make('contact_person')
                    ->label('Contact')
                    ->searchable()
                    ->toggleable()
                    ->placeholder('—'),

                TextColumn::make('comenzi_count')
                    ->label('Comenzi')
                    ->counts('comenzi')
                    ->sortable(),

                IconColumn::make('is_active')
                    ->label('Active')
                    ->boolean()
                    ->sortable(),

                TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime('d.m.Y')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('client_type')
                    ->label('Client Type')
                    ->options([
                        'company' => 'Companies',
                        'individual' => 'Individuals',
                    ])
                    ->query(function ($query, $data) {
                        if ($data['value'] === 'company') {
                            return $query->whereNotNull('company_name');
                        } elseif ($data['value'] === 'individual') {
                            return $query->whereNotNull('name')->whereNull('company_name');
                        }
                        return $query;
                    }),

                SelectFilter::make('is_active')
                    ->label('Status')
                    ->options([
                        1 => 'Active',
                        0 => 'Inactive',
                    ]),
            ])
            ->recordActions([
                EditAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }
}
