<?php

namespace App\Filament\App\Resources\Comandas\Pages;

use App\Filament\App\Resources\Comandas\ComandaResource;
use App\Filament\App\Resources\Comandas\Schemas\ComandaEditForm;
use Filament\Resources\Pages\CreateRecord;
use Filament\Schemas\Schema;

class CreateComanda extends CreateRecord
{
    protected static string $resource = ComandaResource::class;

    public function form(Schema $schema): Schema
    {
        return ComandaEditForm::configure($schema);
    }

    protected function afterCreate(): void
    {
        // Ensure default item exists after creation (backup to model logic)
        $comanda = $this->getRecord();

        if ($comanda->items()->count() === 0) {
            $comanda->items()->create([
                'name' => $comanda->name,
                'description' => $comanda->name,
                'quantity' => 1,
                'unit_price' => $comanda->total_value ?: 0,
                'total_price' => $comanda->total_value ?: 0,
                'unit' => 'serviciu',
                'is_default_item' => true,
                'assigned_to' => $comanda->owner_id, // Inherit owner as assigned user
            ]);
        }
    }
}
