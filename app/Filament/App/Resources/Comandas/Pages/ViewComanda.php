<?php

namespace App\Filament\App\Resources\Comandas\Pages;

use App\Filament\App\Resources\Comandas\ComandaResource;
use Filament\Actions\EditAction;
use Filament\Actions\Action;
use Filament\Resources\Pages\ViewRecord;

class ViewComanda extends ViewRecord
{
    protected static string $resource = ComandaResource::class;

    protected function getHeaderActions(): array
    {
        $actions = [];
        
        // Add document download actions (only for owner PM or above)
        if ($this->canSeeDocuments()) {
            $documents = $this->record->generatedDocuments()
                ->whereNotNull('pdf_path')
                ->whereNotNull('pdf_generated_at')
                ->orderBy('type')
                ->orderBy('created_at', 'desc')
                ->get();

            \Log::info('Document downloads check', [
                'comanda_id' => $this->record->id,
                'can_see_documents' => true,
                'total_documents' => $this->record->generatedDocuments()->count(),
                'documents_with_pdf' => $documents->count(),
                'document_types' => $documents->pluck('type')->toArray(),
            ]);

            foreach ($documents as $document) {
                $actions[] = Action::make("download_{$document->id}")
                    ->label($document->getTypeLabel())
                    ->icon('heroicon-o-arrow-down-tray')
                    ->color('gray')
                    ->url(route('documents.download', ['uuid' => $document->uuid]))
                    ->openUrlInNewTab(false);
            }
        } else {
            \Log::info('Cannot see documents', [
                'comanda_id' => $this->record->id,
                'user_id' => auth()->id(),
                'owner_id' => $this->record->owner_id,
            ]);
        }
        
        // Edit action (if allowed)
        $actions[] = EditAction::make()
            ->visible(function () {
                $user = auth()->user();
                return in_array($user->role, ['superadmin', 'admin', 'manager']) || 
                       $this->record->owner_id === $user->id;
            })
            ->disabled(function () {
                $user = auth()->user();
                // Disabled (grayed out) for non-owners who aren't superadmin/admin/manager
                return !in_array($user->role, ['superadmin', 'admin', 'manager']) && 
                       $this->record->owner_id !== $user->id;
            });

        return $actions;
    }

    protected function canSeePricing(): bool
    {
        $user = auth()->user();
        
        // Owner PM can always see pricing
        if ($this->record->owner_id === $user->id) {
            return true;
        }
        
        // Superadmin, Manager, PM roles can see pricing
        return in_array($user->role, ['superadmin', 'manager', 'pm']);
    }

    protected function canSeeDocuments(): bool
    {
        return $this->canSeePricing(); // Same permissions
    }

    public function getTitle(): string
    {
        $record = $this->getRecord();
        return "Viewing {$record->name} - {$record->internal_number}";
    }
}
