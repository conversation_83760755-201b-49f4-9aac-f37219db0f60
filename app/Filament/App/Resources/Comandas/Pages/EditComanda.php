<?php

namespace App\Filament\App\Resources\Comandas\Pages;

use App\Filament\App\Resources\Comandas\ComandaResource;
use App\Filament\App\Resources\Comandas\Schemas\ComandaEditForm;
use Filament\Actions\DeleteAction;
use Filament\Resources\Pages\EditRecord;
use Filament\Schemas\Schema;

class EditComanda extends EditRecord
{
    protected static string $resource = ComandaResource::class;

    public function form(Schema $schema): Schema
    {
        return ComandaEditForm::configure($schema);
    }

    public function getTitle(): string
    {
        $record = $this->getRecord();
        return "Editing {$record->name} - {$record->internal_number}";
    }

    protected function getHeaderActions(): array
    {
        return [
            DeleteAction::make(),
        ];
    }
    
    protected function mutateFormDataBeforeFill(array $data): array
    {
        // Load existing files from the File model into the form field
        $data['comanda_attachments'] = $this->record->files()
            ->where('category', 'general')
            ->pluck('path')
            ->toArray();
        
        return $data;
    }
    
    protected function afterSave(): void
    {
        // Recalculate and update comanda stage based on completed activities
        $this->record->updateStageFromActivities();
        
        // Handle Comanda files - create File model records
        $uploadedFiles = $this->form->getState()['comanda_attachments'] ?? [];
        
        if (!is_array($uploadedFiles)) {
            $uploadedFiles = [];
        }
        
        // Get existing file paths
        $existingPaths = $this->record->files()
            ->where('category', 'general')
            ->pluck('path')
            ->toArray();
        
        // Find new files (files that were just uploaded)
        $newFiles = array_diff($uploadedFiles, $existingPaths);
        
        // Create File records for new uploads
        foreach ($newFiles as $filePath) {
            if (\Storage::disk('private')->exists($filePath)) {
                \App\Models\File::create([
                    'original_name' => basename($filePath),
                    'stored_name' => basename($filePath),
                    'path' => $filePath,
                    'disk' => 'private',
                    'size' => \Storage::disk('private')->size($filePath),
                    'mime_type' => \Storage::disk('private')->mimeType($filePath),
                    'extension' => pathinfo($filePath, PATHINFO_EXTENSION),
                    'fileable_type' => \App\Models\Comanda::class,
                    'fileable_id' => $this->record->id,
                    'category' => 'general',
                    'uploaded_by' => auth()->id(),
                ]);
            }
        }
        
        // Find deleted files (files that were removed)
        $deletedPaths = array_diff($existingPaths, $uploadedFiles);
        
        // Delete File records and physical files
        foreach ($deletedPaths as $filePath) {
            $fileRecord = $this->record->files()
                ->where('path', $filePath)
                ->first();
            
            if ($fileRecord) {
                $fileRecord->delete(); // This will also delete the physical file via model event
            }
        }
        
        // Handle Activity files - create File model records
        foreach ($this->record->activities as $activity) {
            $activityAttachments = $activity->attachments ?? [];
            
            // Get existing file paths for this activity
            $existingActivityPaths = $activity->files()
                ->where('category', 'activity_input')
                ->pluck('path')
                ->toArray();
            
            // Find new files
            $newActivityFiles = array_diff($activityAttachments, $existingActivityPaths);
            
            // Create File records for new activity uploads
            foreach ($newActivityFiles as $filePath) {
                if (\Storage::disk('local')->exists($filePath)) {
                    \App\Models\File::create([
                        'uuid' => \Illuminate\Support\Str::uuid(),
                        'original_name' => basename($filePath),
                        'stored_name' => basename($filePath),
                        'path' => $filePath,
                        'disk' => 'local',
                        'size' => \Storage::disk('local')->size($filePath),
                        'mime_type' => \Storage::disk('local')->mimeType($filePath),
                        'extension' => pathinfo($filePath, PATHINFO_EXTENSION),
                        'fileable_type' => \App\Models\ComandaActivity::class,
                        'fileable_id' => $activity->id,
                        'category' => 'activity_input',
                        'uploaded_by' => auth()->id(),
                        'is_public' => false,
                    ]);
                }
            }
            
            // Find deleted files
            $deletedActivityPaths = array_diff($existingActivityPaths, $activityAttachments);
            
            // Delete File records
            foreach ($deletedActivityPaths as $filePath) {
                $fileRecord = $activity->files()
                    ->where('path', $filePath)
                    ->first();
                
                if ($fileRecord) {
                    $fileRecord->delete();
                }
            }
        }
    }
}
