<?php

namespace App\Filament\App\Resources\Comandas;

use App\Filament\App\Resources\Comandas\Pages\CreateComanda;
use App\Filament\App\Resources\Comandas\Pages\EditComanda;
use App\Filament\App\Resources\Comandas\Pages\ListComandas;
use App\Filament\App\Resources\Comandas\Pages\ViewComanda;
use App\Filament\App\Resources\Comandas\Schemas\ComandaForm;
use App\Filament\App\Resources\Comandas\Schemas\ComandaInfolist;
use App\Filament\App\Resources\Comandas\Tables\ComandasTable;
use App\Models\Comanda;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class ComandaResource extends Resource
{
    protected static ?string $model = Comanda::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedRectangleStack;

    protected static ?string $navigationLabel = 'Comenzi';
    
    protected static \UnitEnum | string | null $navigationGroup = 'Orders & Calendar';
    
    protected static ?int $navigationSort = 1;

    protected static ?string $pluralModelLabel = 'comenzi';

    protected static ?string $modelLabel = 'comandă';

    protected static ?string $recordTitleAttribute = 'name';

    public static function form(Schema $schema): Schema
    {
        return ComandaForm::configure($schema);
    }

    public static function infolist(Schema $schema): Schema
    {
        return ComandaInfolist::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return ComandasTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListComandas::route('/'),
            'create' => CreateComanda::route('/create'),
            'view' => ViewComanda::route('/{record}'),
            'edit' => EditComanda::route('/{record}/edit'),
        ];
    }

    // RBAC Implementation
    public static function canViewAny(): bool
    {
        $user = auth()->user();

        if (!$user || !$user->is_active) {
            return false;
        }

        // All active users can view the comenzi list (filtered by role)
        return $user->hasAnyRole(['superadmin', 'manager', 'pm', 'specialist']);
    }

    public static function canView(Model $record): bool
    {
        $user = auth()->user();

        if (!$user || !$user->is_active) {
            return false;
        }

        return match($user->role) {
            'superadmin', 'manager' => true,
            'pm' => $record->owner_id === $user->id,
            'specialist' => $record->activities()->where('assigned_to', $user->id)->exists(),
            default => false
        };
    }

    public static function canCreate(): bool
    {
        $user = auth()->user();

        if (!$user || !$user->is_active) {
            return false;
        }

        // Only superadmin, manager, and PM can create comandas
        return $user->hasAnyRole(['superadmin', 'manager', 'pm']);
    }

    public static function canEdit(Model $record): bool
    {
        $user = auth()->user();

        if (!$user || !$user->is_active) {
            return false;
        }

        return $user->canEditComanda($record);
    }

    public static function canDelete(Model $record): bool
    {
        $user = auth()->user();

        if (!$user || !$user->is_active) {
            return false;
        }

        return $user->canDeleteComanda($record);
    }

    public static function getEloquentQuery(): Builder
    {
        $user = auth()->user();

        if (!$user || !$user->is_active) {
            return parent::getEloquentQuery()->whereRaw('1 = 0'); // Return empty query
        }

        $query = parent::getEloquentQuery();

        return match($user->role) {
            'superadmin', 'manager' => $query, // See all comenzi
            'pm' => $query->where('owner_id', $user->id), // Only owned comenzi
            'specialist' => $query->whereHas('activities', function (Builder $q) use ($user) {
                $q->where('assigned_to', $user->id);
            }), // Only comenzi with assigned activities
            default => $query->whereRaw('1 = 0') // No access
        };
    }
}
