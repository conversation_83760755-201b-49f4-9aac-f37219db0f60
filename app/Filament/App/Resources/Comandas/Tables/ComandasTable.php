<?php

namespace App\Filament\App\Resources\Comandas\Tables;

use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteAction;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Actions\Action;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use App\Tables\Columns\StageProgressColumn;
use App\Tables\Columns\UserAvatarColumn;

class ComandasTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                // Column 1: ID
                TextColumn::make('internal_number')
                    ->label('#')
                    ->searchable()
                    ->sortable()
                    ->copyable()
                    ->width('80px'),

                // Column 2: Channel
                TextColumn::make('arrival_channel')
                    ->label('Channel')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'email' => 'primary',
                        'whatsapp' => 'success',
                        'website' => 'info',
                        'phone' => 'warning',
                        'walk_in' => 'secondary',
                        default => 'secondary',
                    })
                    ->width('100px'),

                // Column 3: Arrived At / Created At
                TextColumn::make('arrival_time')
                    ->label('Arrived')
                    ->date('d.m.Y')
                    ->sortable()
                    ->width('90px'),

                // Column 4: Order Name
                TextColumn::make('name')
                    ->label('Order Name')
                    ->searchable()
                    ->sortable(),

                // Column 5: Client
                TextColumn::make('client.display_name')
                    ->label('Client')
                    ->getStateUsing(fn ($record) => $record->client->company_name ?: $record->client->name)
                    ->searchable(['client.company_name', 'client.name'])
                    ->sortable(),

                // Column 6: Multi-column Progress Display
                StageProgressColumn::make('stage_progress')
                    ->label('Progress'),

                // Column 7: PM
                UserAvatarColumn::make('owner.name')
                    ->label('PM')
                    ->placeholder('—')
                    ->toggleable(),

                // Column 8: Deadline
                TextColumn::make('deadline')
                    ->label('Deadline')
                    ->date('d.m.Y')
                    ->tooltip(fn ($record) => $record->deadline ? $record->deadline->format('d.m.Y H:i') : null)
                    ->sortable()
                    ->color(fn ($record) => $record->deadline && $record->deadline->isPast() && $record->stage < 5 ? 'danger' : null)
                    ->width('100px'),

                // Column 9: Value
                TextColumn::make('total_value')
                    ->label('Value')
                    ->money('EUR')
                    ->sortable()
                    ->toggleable()
                    ->visible(function ($record) {
                        $user = auth()->user();
                        // Show to superadmin, admin, manager, or the owner PM
                        return in_array($user->role, ['superadmin', 'admin', 'manager']) || 
                               ($record && $record->owner_id === $user->id);
                    }),

                // Column 10: Fast Track Icon
                IconColumn::make('fast_track')
                    ->label('')
                    ->icon(fn (bool $state): string => 'heroicon-o-bolt')
                    ->color(fn (bool $state): string => $state ? 'warning' : 'gray')
                    ->tooltip('Fast Track')
                    ->width('40px')
                    ->toggleable(),

                // Column 11: Priority Icon
                IconColumn::make('priority')
                    ->label('')
                    ->icon(fn (bool $state): string => 'heroicon-o-exclamation-triangle')
                    ->color(fn (bool $state): string => $state ? 'warning' : 'gray')
                    ->tooltip('Priority')
                    ->width('40px')
                    ->toggleable(),

                // Column 12: Payment Status Icon
                IconColumn::make('payment_status')
                    ->label('')
                    ->icon(fn (string $state): string => match ($state) {
                        'paid' => 'heroicon-o-check-circle',
                        'partial' => 'heroicon-o-clock',
                        'overpaid' => 'heroicon-o-exclamation-circle',
                        'unpaid' => 'heroicon-o-x-circle',
                        default => 'heroicon-o-question-mark-circle',
                    })
                    ->color(fn (string $state): string => match ($state) {
                        'paid' => 'success',
                        'partial' => 'warning',
                        'overpaid' => 'info',
                        'unpaid' => 'danger',
                        default => 'gray',
                    })
                    ->tooltip(fn ($record): string => match ($record->payment_status) {
                        'paid' => 'Paid: ' . number_format($record->paid_amount, 2) . ' RON',
                        'partial' => 'Partial: ' . number_format($record->paid_amount, 2) . ' / ' . number_format($record->total_value, 2) . ' RON (' . round($record->payment_percentage) . '%)',
                        'overpaid' => 'Overpaid: ' . number_format($record->paid_amount, 2) . ' RON',
                        'unpaid' => 'Unpaid: ' . number_format($record->total_value, 2) . ' RON',
                        default => 'Unknown payment status',
                    })
                    ->width('40px'),

                // Hidden: Created At (toggleable)
                TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime('d.m.Y')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('stage')
                    ->options([
                        1 => 'Stage 1 - Initial',
                        2 => 'Stage 2 - Confirmed',
                        3 => 'Stage 3 - In Progress',
                        4 => 'Stage 4 - Review',
                        5 => 'Stage 5 - Completed',
                        6 => 'Stage 6 - Delivered',
                        7 => 'Stage 7 - Invoiced',
                    ]),

                SelectFilter::make('arrival_channel')
                    ->label('Arrival Channel')
                    ->options([
                        'email' => 'Email',
                        'whatsapp' => 'WhatsApp',
                        'website' => 'Website',
                        'phone' => 'Phone',
                        'walk_in' => 'Walk-in',
                    ]),

                SelectFilter::make('owner_id')
                    ->label('Project Manager')
                    ->relationship('owner', 'name'),

                SelectFilter::make('priority')
                    ->label('Priority')
                    ->options([
                        1 => 'Yes',
                        0 => 'No',
                    ]),

                SelectFilter::make('fast_track')
                    ->label('Fast Track')
                    ->options([
                        1 => 'Yes',
                        0 => 'No',
                    ]),

                SelectFilter::make('payment_status')
                    ->label('Payment Status')
                    ->options([
                        'unpaid' => 'Unpaid',
                        'partial' => 'Partial Payment',
                        'paid' => 'Fully Paid',
                        'overpaid' => 'Overpaid',
                    ]),
            ])
            ->recordActions([
                Action::make('createDocument')
                    ->label('')
                    ->icon('heroicon-o-document-plus')
                    ->color('success')
                    ->tooltip('Create Document')
                    ->url(fn ($record) => route('filament.app.pages.create-document-page', ['comanda_id' => $record->id]))
                    ->openUrlInNewTab(false)
                    ->visible(function ($record) {
                        $user = auth()->user();
                        // Only owner or superadmin/admin/manager
                        return in_array($user->role, ['superadmin', 'admin', 'manager']) || 
                               $record->owner_id === $user->id;
                    })
                    ->disabled(function ($record) {
                        $user = auth()->user();
                        // Disabled for non-owners (grayed out)
                        return !in_array($user->role, ['superadmin', 'admin', 'manager']) && 
                               $record->owner_id !== $user->id;
                    }),
                
                EditAction::make()
                    ->label('')
                    ->tooltip('Edit')
                    ->visible(function ($record) {
                        $user = auth()->user();
                        return in_array($user->role, ['superadmin', 'admin', 'manager']) || 
                               $record->owner_id === $user->id;
                    })
                    ->disabled(function ($record) {
                        $user = auth()->user();
                        return !in_array($user->role, ['superadmin', 'admin', 'manager']) && 
                               $record->owner_id !== $user->id;
                    }),
                    
                DeleteAction::make()
                    ->label('')
                    ->tooltip('Delete')
                    ->visible(function ($record) {
                        $user = auth()->user();
                        return in_array($user->role, ['superadmin', 'admin', 'manager']) || 
                               $record->owner_id === $user->id;
                    })
                    ->disabled(function ($record) {
                        $user = auth()->user();
                        return !in_array($user->role, ['superadmin', 'admin', 'manager']) && 
                               $record->owner_id !== $user->id;
                    }),
            ])
            ->recordUrl(fn ($record) => route('filament.app.resources.comandas.view', $record))
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('id', 'desc')
            ->extraAttributes([
                'class' => 'table-row-padding-enhanced'
            ]);
    }
}
