<?php

namespace App\Filament\App\Resources\Comandas\Schemas;

use App\Models\Client;
use App\Models\User;
use App\Models\ComandaItem;
use App\Models\File;
use App\Services\InfocuiService;
use App\Services\SubstageConfiguration;
use App\Services\FileStorageService;
use Filament\Actions\Action;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Placeholder;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Components\Flex;
use Filament\Forms\Components\Hidden;
use Filament\Schemas\Components\Grid;
use Filament\Schemas\Components\Wizard;
use Filament\Schemas\Components\Wizard\Step;
use Filament\Schemas\Components\Tabs;
use Filament\Schemas\Components\Tabs\Tab;
use Filament\Schemas\Schema;
use Illuminate\Support\Facades\Log;

class ComandaEditForm
{

    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Grid::make(9)
                    ->schema([
                        // Left Half - Order Details
                        Section::make()
                        ->compact()
                            ->schema([
                                Grid::make(5)
                                    ->schema([
                                        // Left nested section (3/4)
                                        Section::make('Order Information')
                                            ->columns(5)
                                            ->schema([
                                                TextInput::make('name')
                                                    ->label('Name')
                                                    ->required()
                                                    ->maxLength(255)
                                                    ->columnSpan(5),


                                                Select::make('client_id')
                                                    ->label('Client')
                                                    ->relationship('client', 'company_name')
                                                    ->getOptionLabelFromRecordUsing(fn (Client $record) => $record->company_name ?: $record->name)
                                                    ->searchable(['company_name', 'name'])
                                                    ->required()
                                                    ->createOptionForm(self::getClientFormSchema())
                                                    ->createOptionUsing(function (array $data) {
                                                        return Client::create($data)->id;
                                                    })
                                                    ->columnSpan(3),
                                                

                                                Section::make('')
                                                   
                                                    ->compact()
                                                    ->columns(4)
                                                    ->schema([
                                                        Select::make('arrival_channel')
                                                            ->label('Arrival Channel')
                                                            ->options([
                                                                'email' => '📧 Email',
                                                                'whatsapp' => '📱 WhatsApp',
                                                                'website' => '🌐 Website',
                                                                'phone' => '📞 Phone',
                                                                'walk_in' => '🚶 Walk-in',
                                                            ])
                                                            ->required()
                                                            ->columnSpan(2),
                                                        DateTimePicker::make('arrival_time')
                                                            ->label('Arrival Time')
                                                            ->required()
                                                            ->columnSpan(2),
                                                            
                                                        ])->columnSpan(5),

                                               
                                                  

                                                TextInput::make('contact_person')
                                                    ->label('Contact')
                                                    ->maxLength(255)
                                                    ->columnSpan(2),

                                                
                                            ])->columnSpan(5),

                                        // Right switches section (1/4)
                                        Section::make('')
                                        ->schema([
                                                Select::make('owner_id')
                                                    ->label('Project Manager (Owner)')
                                                    ->relationship('owner', 'name', function ($query) {
                                                        return $query->whereIn('role', ['superadmin', 'admin', 'manager', 'pm']);
                                                    })
                                                    ->searchable()
                                                    ->required()
                                                    ->visible(fn () => auth()->user()->hasAnyRole(['superadmin', 'manager']))
                                                    ->disabled(fn () => !auth()->user()->hasAnyRole(['superadmin', 'manager'])),
                                                DateTimePicker::make('deadline')
                                                    ->label('Deadline')
                                                    ->nullable()
                                                    ->columnSpanFull(),

                                                Section::make('Flags')
                                                    ->schema([
                                                    Toggle::make('priority')
                                                        ->label('Priority')
                                                        ->default(false)
                                                        ->visible(fn () => auth()->user()->hasAnyRole(['superadmin', 'manager', 'pm']))
                                                        ->disabled(fn () => !auth()->user()->hasAnyRole(['superadmin', 'manager'])),

                                                    Toggle::make('fast_track')
                                                        ->label('Fast Track')
                                                        ->default(false)
                                                        ->visible(fn () => auth()->user()->hasAnyRole(['superadmin', 'manager', 'pm']))
                                                        ->disabled(fn () => !auth()->user()->hasAnyRole(['superadmin', 'manager'])),

                                                    Toggle::make('is_active')
                                                        ->label('Active')
                                                        ->default(true)
                                                        ->visible(fn () => auth()->user()->hasAnyRole(['superadmin', 'manager', 'pm']))
                                                        ->disabled(fn () => !auth()->user()->hasAnyRole(['superadmin', 'manager'])),

                                                    Toggle::make('is_finished')
                                                    ->label('Finished')
                                                        ->default(false)
                                                        ->visible(fn () => auth()->user()->hasAnyRole(['superadmin', 'manager', 'pm']))
                                                        ->disabled(fn () => !auth()->user()->hasAnyRole(['superadmin', 'manager'])),
                                                    ]),
                                                
                                                // Payment Information
                                                Section::make('Payment')
                                                    ->schema([
                                                        Grid::make(2)
                                                            ->schema([
                                                                TextInput::make('total_value')
                                                                    ->label('Total Value')
                                                                    ->numeric()
                                                                    ->step(0.01)
                                                                    ->suffix('€')
                                                                    ->default(0)
                                                                    ->visible(fn () => auth()->user()->canSeePricing())
                                                                    ->disabled()
                                                                    ->dehydrated(false)
                                                                    ->helperText('Automatically calculated from items')
                                                                    ->reactive()
                                                                    ->columnSpan(1),
                                                                
                                                                TextInput::make('paid_amount')
                                                                    ->label('Paid Amount')
                                                                    ->numeric()
                                                                    ->step(0.01)
                                                                    ->suffix('€')
                                                                    ->default(0)
                                                                    ->visible(fn () => auth()->user()->canSeePricing())
                                                                    ->disabled()
                                                                    ->helperText('Will be handled later')
                                                                    ->columnSpan(1),
                                                            ]),
                                                    ])
                                                    ->compact()
                                                    ->visible(fn () => auth()->user()->canSeePricing()),

                                                
                                           
                                            ])->columnSpan(5),
                                        
                                    ]),

                                // Items section
                                Repeater::make('items')
                                            ->relationship()
                                            ->itemLabel(fn (array $state): ?string => $state['description'] ?? 'New Item')
                                            ->collapsible()
                                            ->schema([
                                                TextInput::make('description')
                                                    ->label('Description')
                                                    ->required()
                                                    ->columnSpanFull(),

                                                TextInput::make('quantity')
                                                    ->label('Quantity')
                                                    ->numeric()
                                                    ->default(1)
                                                    ->minValue(1)
                                                    ->reactive()
                                                    ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                                        $unitPrice = $get('unit_price') ?: 0;
                                                        $set('total_price', $state * $unitPrice);
                                                    })
                                                    ->columnSpan(1),

                                                TextInput::make('unit')
                                                    ->label('Unit')
                                                    ->default('buc')
                                                    ->columnSpan(1),

                                                TextInput::make('unit_price')
                                                    ->label('Unit Price')
                                                    ->numeric()
                                                    ->step(0.01)
                                                    ->suffix('€')
                                                    ->visible(fn () => auth()->user()->canSeePricing())
                                                    ->disabled(fn () => !auth()->user()->hasAnyRole(['superadmin', 'manager']))
                                                    ->reactive()
                                                    ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                                        $quantity = $get('quantity') ?: 1;
                                                        $set('total_price', $state * $quantity);
                                                    })
                                                    ->columnSpan(1),

                                                TextInput::make('total_price')
                                                    ->label('Total Price')
                                                    ->numeric()
                                                    ->step(0.01)
                                                    ->suffix('€')
                                                    ->visible(fn () => auth()->user()->canSeePricing())
                                                    ->disabled()
                                                    ->columnSpan(1),
                                            ])
                                            ->columns(4)
                                            ->addActionLabel('+ Add Item')
                                            ->defaultItems(0)
                                            ->columnSpan(3),
                                    


                            ])
                            ->columnSpan(3),

                        // Right Half
                        Section::make()
                            ->compact()
                            ->schema([
                                Grid::make(2)
                                    ->schema([
                                        // File upload interface (2/4)
                                        Section::make('Files & Documents')
                                            ->schema([
                                                FileUpload::make('comanda_attachments')
                                                    ->label('Upload Files')
                                                    ->multiple()
                                                    ->disk('private')
                                                    ->directory(fn ($record) => $record ? "comandas/{$record->uuid}/general" : 'temp')
                                                    ->visibility('private')
                                                    ->previewable(true)
                                                    ->downloadable(true)
                                                    ->openable()
                                                    ->deletable(true)
                                                    ->maxSize(10240)
                                                    ->acceptedFileTypes([
                                                        'image/*',
                                                        'application/pdf',
                                                        '.doc', '.docx',
                                                        '.xls', '.xlsx',
                                                        '.txt', '.csv'
                                                    ])
                                                    ->reorderable()
                                                    ->appendFiles()
                                                    ->moveFiles()
                                                    ->preserveFilenames()
                                                    ->helperText('Upload documents, images, and other files related to this order.')
                                                    ->columnSpanFull(),
                                            ])
                                            ->columnSpan(1),

                                        // Issue tickets placeholder (2/4)
                                        Section::make('Issue Tickets')
                                            ->schema([
                                                Placeholder::make('tickets_placeholder')
                                                    ->label('')
                                                    ->content('Issue ticket system will be implemented here')
                                                    ->columnSpanFull(),
                                            ])
                                            ->columnSpan(1),
                                    ]),

                                // Wizard section (full 4/4)
                                static::getStageWizard(),
                            ])
                            ->columnSpan(6), 
                    ])->columnSpanFull(),
            ]);
    }

    protected static function getStageWizard(): Wizard
    {
        $stages = [
            1 => 'Ofertare',
            2 => 'Contractare', 
            3 => 'Pregatire',
            4 => 'Aprovizionare',
            5 => 'Executie',
            6 => 'Livrare',
            7 => 'Facturare',
        ];

        $steps = [];
        foreach ($stages as $stageNumber => $stageName) {
            $steps[] = Step::make($stageName)
                ->icon('') // Remove icon
                ->completedIcon('')
                ->schema([
                    Repeater::make("stage_{$stageNumber}_activities")
                        ->label("Activities for {$stageName}")
                        ->relationship('activities', function ($query) use ($stageNumber) {
                            return $query->forStage($stageNumber)->with('files');
                        })
                        ->itemLabel(fn (array $state): ?string => \App\Models\ComandaActivity::getTypesForStage($stageNumber)[$state['type'] ?? ''] ?? 'New Activity')
                        ->schema([
                            Grid::make(2)
                                ->schema([
                                    // Left side - Form fields
                                    Section::make('Activity Details')
                                        ->schema([
                                            Select::make('type')
                                                ->label('Activity Type')
                                                ->options(\App\Models\ComandaActivity::getTypesForStage($stageNumber))
                                                ->required()
                                                ->live(),

                                            Textarea::make('description')
                                                ->label('Description')
                                                ->required()
                                                ->rows(3)
                                                ->columnSpanFull(),

                                            Select::make('assigned_to')
                                                ->label('Assigned To')
                                                ->relationship('assignee', 'name')
                                                ->searchable()
                                                ->required()
                                                ->default(auth()->id()),
                                            
                                            DateTimePicker::make('deadline')
                                                ->label('Deadline')
                                                ->native(false)
                                                ->displayFormat('d.m.Y H:i')
                                                ->seconds(false),
                                            
                                            Toggle::make('is_done')
                                                ->label('Completed')
                                                ->default(false),
                                        ])
                                        ->columnSpan(1),

                                    // Right side - File upload
                                    Section::make('Files')
                                        ->schema([
                                            FileUpload::make('attachments')
                                                ->label('Activity Files')
                                                ->multiple()
                                                ->disk('local')
                                                ->directory('activity-files')
                                                ->visibility('private')
                                                ->deletable(true)
                                                ->maxSize(10240)
                                                ->acceptedFileTypes([
                                                    'image/*',
                                                    'application/pdf',
                                                    '.doc', '.docx',
                                                    '.xls', '.xlsx',
                                                    '.txt', '.csv', '.cdr'
                                                ])
                                                ->reorderable()
                                                ->moveFiles()
                                                ->downloadable(true)
                                                ->preserveFilenames()
                                                ->appendFiles()
                                                ->helperText('Upload files for this activity.')
                                                ->afterStateHydrated(function ($component, $state, $record) {
                                                    if ($record && $record->exists) {
                                                        // Reload files to ensure we have the latest
                                                        $record->load('files');
                                                        $filePaths = $record->files->pluck('path')->toArray();
                                                        
                                                        \Log::info('FileUpload afterStateHydrated() setting state', [
                                                            'activity_id' => $record->id,
                                                            'file_paths' => $filePaths,
                                                            'old_state' => $state,
                                                        ]);
                                                        
                                                        // Set the state with file paths
                                                        $component->state($filePaths);
                                                    }
                                                })
                                                ->columnSpanFull(),
                                            
                                            Placeholder::make('existing_files')
                                                ->label('Download Attached Files')
                                                ->content(function ($record) {
                                                    if (!$record || !$record->exists) {
                                                        return new \Illuminate\Support\HtmlString('<p class="text-sm text-gray-500">No files yet</p>');
                                                    }
                                                    
                                                    $record->load('files');
                                                    
                                                    if ($record->files->isEmpty()) {
                                                        return new \Illuminate\Support\HtmlString('<p class="text-sm text-gray-500">No files attached</p>');
                                                    }
                                                    
                                                    $html = '<div class="space-y-1 mt-2">';
                                                    foreach ($record->files as $file) {
                                                        $isNew = $file->created_at->isToday();
                                                        $newBadge = $isNew ? '<span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-bold bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 ml-2">NOU</span>' : '';
                                                        $downloadUrl = route('files.download', $file->uuid);
                                                        
                                                        $html .= '<div class="flex items-center justify-between text-sm p-2 bg-gray-50 dark:bg-gray-800 rounded">';
                                                        $html .= '<span class="text-gray-700 dark:text-gray-300">' . e($file->original_name) . $newBadge . '</span>';
                                                        $html .= '<a href="' . $downloadUrl . '" target="_blank" class="text-blue-600 hover:text-blue-800">Download</a>';
                                                        $html .= '</div>';
                                                    }
                                                    $html .= '</div>';
                                                    
                                                    return new \Illuminate\Support\HtmlString($html);
                                                })
                                                ->columnSpanFull(),
                                        ])
                                        ->columnSpan(1),
                                ]),

                            Hidden::make('assigned_by')
                                ->default(auth()->id()),
                                
                            Hidden::make('stage')
                                ->default($stageNumber),
                        ])
                        ->addActionLabel('Add Activity')
                        ->collapsible()
                        ->columnSpanFull(),
                ]);
        }

        return Wizard::make($steps)
            ->skippable()
            ->columnSpanFull()
           // ->numbered(false) // Try to disable step numbers
            ->extraAttributes([
                'class' => 'compact-wizard'
            ]);
    }

    /**
     * Get the client form schema for the create modal
     */
    private static function getClientFormSchema(): array
    {
        return [
            Radio::make('client_type')
                ->options([
                    'company' => 'Company',
                    'individual' => 'Individual',
                ])
                ->default('company')
                ->reactive()
                ->afterStateUpdated(function ($state, callable $set) {
                    if ($state === 'company') {
                        $set('name', null);
                    } else {
                        $set('company_name', null);
                        $set('cui', null);
                        $set('reg_com', null);
                    }
                }),

            TextInput::make('company_name')
                ->label('Company Name')
                ->visible(fn ($get) => $get('client_type') === 'company')
                ->required(fn ($get) => $get('client_type') === 'company')
                ->maxLength(255),

            TextInput::make('name')
                ->label('Individual Name')
                ->visible(fn ($get) => $get('client_type') === 'individual')
                ->required(fn ($get) => $get('client_type') === 'individual')
                ->maxLength(255),

            TextInput::make('cui')
                ->label('CUI (Tax ID)')
                ->visible(fn ($get) => $get('client_type') === 'company')
                ->placeholder('12345678')
                ->helperText('Enter CUI without RO prefix')
                ->prefix(fn ($get) => !empty($get('tva')) ? 'RO' : null)
                ->maxLength(20)
                ->reactive()
                ->suffixAction(
                    Action::make('lookup')
                        ->icon('heroicon-m-magnifying-glass')
                        ->tooltip('Lookup company data from Infocui')
                        ->action(function ($get, $set) {
                            $cui = $get('cui');
                            
                            if (empty($cui)) {
                                \Filament\Notifications\Notification::make()
                                    ->title('Error')
                                    ->body('Please enter a CUI first')
                                    ->danger()
                                    ->send();
                                return;
                            }
                            
                            try {
                                $service = new InfocuiService();
                                $data = $service->getCompanyInfo($cui);
                                
                                if ($data === null) {
                                    \Filament\Notifications\Notification::make()
                                        ->title('Not Found')
                                        ->body('No company found with this CUI')
                                        ->warning()
                                        ->send();
                                    return;
                                }
                                
                                // Populate fields only if they are empty
                                if (empty($get('company_name'))) {
                                    $set('company_name', $data['company_name'] ?? null);
                                }
                                if (empty($get('reg_com'))) {
                                    $set('reg_com', $data['reg_com'] ?? null);
                                }
                                if (empty($get('address'))) {
                                    $set('address', $data['address'] ?? null);
                                }
                                if (empty($get('phone'))) {
                                    $set('phone', $data['phone'] ?? null);
                                }
                                if (empty($get('tva'))) {
                                    $set('tva', $data['tva'] ?? null);
                                }
                                if (empty($get('notes'))) {
                                    $set('notes', $data['notes'] ?? null);
                                }
                                
                                \Filament\Notifications\Notification::make()
                                    ->title('Success')
                                    ->body('Company data loaded successfully' . ($data['tva'] ? ' (TVA registered)' : ''))
                                    ->success()
                                    ->send();
                            } catch (\Exception $e) {
                                Log::error('Infocui lookup failed', ['error' => $e->getMessage()]);
                                \Filament\Notifications\Notification::make()
                                    ->title('Error')
                                    ->body('Failed to lookup company data: ' . $e->getMessage())
                                    ->danger()
                                    ->send();
                            }
                        })
                ),

            TextInput::make('reg_com')
                ->label('Reg. Com.')
                ->visible(fn ($get) => $get('client_type') === 'company')
                ->placeholder('J40/1234/2024')
                ->maxLength(50),

            Textarea::make('address')
                ->required()
                ->rows(3)
                ->columnSpanFull(),

            TextInput::make('email')
                ->label('Email Address')
                ->email()
                ->maxLength(255),

            TextInput::make('phone')
                ->tel()
                ->maxLength(20),

            Toggle::make('is_active')
                ->label('Active')
                ->default(true),
        ];
    }
}
