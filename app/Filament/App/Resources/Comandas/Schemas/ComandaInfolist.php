<?php

namespace App\Filament\App\Resources\Comandas\Schemas;

use Filament\Infolists\Components\RepeatableEntry;
use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Components\Grid;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;
use Filament\Support\Enums\FontWeight;
use Filament\Support\Enums\TextSize;
use Illuminate\Support\HtmlString;

class ComandaInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                // TOP ROW - 3 columns
                Grid::make(3)
                    ->schema([
                        // LEFT - Order details (in a card)
                        Section::make()
                            ->schema([
                                TextEntry::make('name')
                                    ->label('Name')
                                    ->size(TextSize::Large)
                                    ->weight(FontWeight::Bold),

                                TextEntry::make('client.name')
                                    ->label('Name')
                                    ->getStateUsing(fn ($record) => $record->client->company_name ?: $record->client->name)
                                    ->size(TextSize::Medium)
                                    ->weight(FontWeight::SemiBold)
                                    ->color('gray'),

                                Grid::make(3)
                                    ->schema([
                                        TextEntry::make('arrival_time')
                                            ->label('Arrived at')
                                            ->date('d.m.Y'),

                                        TextEntry::make('arrival_channel')
                                            ->label('Channel')
                                            ->formatStateUsing(fn ($state) => match($state) {
                                                'email' => 'Email',
                                                'whatsapp' => 'WhatsApp',
                                                'website' => 'Website',
                                                'phone' => 'Phone',
                                                'walk_in' => 'Walk-in',
                                                default => $state ?? 'N/A'
                                            }),

                                        TextEntry::make('owner.name')
                                            ->label('PM')
                                            ->placeholder('—'),
                                    ]),

                                Grid::make(4)
                                    ->schema([
                                        TextEntry::make('priority')
                                            ->label('Priority')
                                            ->formatStateUsing(fn ($state) => $state ? '⭐' : '—'),

                                        TextEntry::make('fast_track')
                                            ->label('Fast Track')
                                            ->formatStateUsing(fn ($state) => $state ? '⚡' : '—'),

                                        TextEntry::make('is_active')
                                            ->label('Active')
                                            ->formatStateUsing(fn ($state) => $state ? '✓' : '—'),

                                        TextEntry::make('deadline')
                                            ->label('Deadline')
                                            ->date('d.m.Y')
                                            ->placeholder('Not set')
                                            ->weight(FontWeight::Bold),
                                    ]),
                            ])
                            ->columnSpan(1),

                        // MIDDLE - Files
                        Section::make('Files & Documents')
                            ->schema([
                                TextEntry::make('id')
                                    ->label('')
                                    ->formatStateUsing(function ($record) {
                                        // Get files directly attached to comanda
                                        $comandaFiles = $record->files;
                                        
                                        // Get files from activities
                                        $activityFileIds = $record->activities()->pluck('id')->toArray();
                                        $activityFiles = \App\Models\File::where('fileable_type', \App\Models\ComandaActivity::class)
                                            ->whereIn('fileable_id', $activityFileIds)
                                            ->get();
                                        
                                        $files = $comandaFiles->merge($activityFiles)->sortByDesc('created_at');

                                        \Log::info('Files debug', [
                                            'comanda_id' => $record->id,
                                            'comanda_files_count' => $comandaFiles->count(),
                                            'activity_ids' => $activityFileIds,
                                            'activity_files_count' => $activityFiles->count(),
                                            'total_files' => $files->count(),
                                            'activity_files_detail' => $activityFiles->map(fn($f) => [
                                                'id' => $f->id,
                                                'name' => $f->original_name,
                                                'fileable_id' => $f->fileable_id,
                                                'fileable_type' => $f->fileable_type,
                                            ])->toArray(),
                                        ]);

                                        if ($files->isEmpty()) {
                                            return new HtmlString('<p style="color: #9ca3af;">No files uploaded</p>');
                                        }

                                        $totalSize = \App\Models\File::formatBytes($files->sum('size'));
                                        $html = "<div style='margin-bottom: 0.75rem; color: #6b7280;'>📁 {$files->count()} files | 💾 {$totalSize}</div>";
                                        
                                        $html .= '<ul style="list-style: none; padding: 0; margin: 0;">';
                                        
                                        foreach ($files as $file) {
                                            $icon = $file->isImage() ? '🖼️' : ($file->isPdf() ? '📄' : '📎');
                                            $downloadUrl = route('files.download', $file->uuid);
                                            $source = $file->fileable_type === 'App\\Models\\Comanda' ? 'Comanda' : 'Activity';
                                            $isNew = $file->created_at->isToday();
                                            
                                            $html .= '<li style="margin-bottom: 0.5rem;">';
                                            $html .= '<a href="' . $downloadUrl . '" style="color: #3b82f6; text-decoration: none;" target="_blank">';
                                            $html .= $icon . ' ' . e($file->original_name);
                                            $html .= '</a>';
                                            if ($isNew) {
                                                $html .= ' <span style="background: #10b981; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px; font-weight: bold; margin-left: 4px;">NOU</span>';
                                            }
                                            $html .= '<br><small style="color: #9ca3af;">' . $source . ' • ' . $file->getHumanReadableSize() . ' • ' . $file->created_at->format('M j') . '</small>';
                                            $html .= '</li>';
                                        }
                                        
                                        $html .= '</ul>';
                                        
                                        return new HtmlString($html);
                                    })
                                    ->html(),
                            ])
                            ->columnSpan(1),

                        // RIGHT - Issues
                        Section::make('Issue Tickets')
                            ->schema([
                                TextEntry::make('id')
                                    ->label('')
                                    ->formatStateUsing(fn () => new HtmlString(
                                        '<p style="color: #9ca3af;">No issues reported yet.</p>
                                        <p style="margin-top: 0.5rem; color: #3b82f6; cursor: pointer;">+ New Issue</p>'
                                    ))
                                    ->html(),
                            ])
                            ->columnSpan(1),
                    ])
                    ->columnSpanFull(),

                // BOTTOM ROW - Items section
                Grid::make(3)
                    ->schema([
                        Section::make('Description & Items')
                            ->schema([
                                TextEntry::make('description')
                                    ->label('Description')
                                    ->placeholder('No description'),

                                TextEntry::make('id')
                                    ->label('Items')
                                    ->formatStateUsing(function ($record) {
                                        $items = $record->items;
                                        
                                        if ($items->isEmpty()) {
                                            return 'No items';
                                        }
                                        
                                        $html = '<ul style="list-style: disc; padding-left: 1.5rem; margin: 0;">';
                                        
                                        foreach ($items as $item) {
                                            $desc = e($item->description ?: $item->name);
                                            $qty = e($item->quantity . ' ' . ($item->unit ?? 'buc'));
                                            
                                            $html .= '<li style="margin-bottom: 0.25rem;">';
                                            $html .= $desc . ' — ' . $qty;
                                            
                                            if (auth()->user()->canSeePricing()) {
                                                $price = number_format($item->unit_price, 2) . ' €';
                                                $html .= ' — <span style="color: #9ca3af;">' . $price . '</span>';
                                            }
                                            
                                            $html .= '</li>';
                                        }
                                        
                                        $html .= '</ul>';
                                        
                                        return new HtmlString($html);
                                    })
                                    ->html(),

                                TextEntry::make('total_value')
                                    ->label('Total value')
                                    ->money('EUR')
                                    ->weight(FontWeight::Bold)
                                    ->size(TextSize::Large)
                                    ->visible(fn () => auth()->user()->canSeePricing()),
                            ])
                            ->columnSpan(1),
                    ])
                    ->columnSpanFull(),
            ]);
    }
}
