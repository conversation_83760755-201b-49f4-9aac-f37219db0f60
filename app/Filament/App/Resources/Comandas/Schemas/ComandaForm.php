<?php

namespace App\Filament\App\Resources\Comandas\Schemas;

use App\Models\Client;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class ComandaForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make('Basic Information')
                    ->schema([
                        Select::make('client_id')
                            ->label('Client')
                            ->relationship('client', 'company_name')
                            ->getOptionLabelFromRecordUsing(fn (Client $record) => $record->company_name ?: $record->name)
                            ->searchable(['company_name', 'name'])
                            ->required(),

                        TextInput::make('internal_number')
                            ->label('Internal Number')
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->placeholder('C001')
                            ->maxLength(50),

                        TextInput::make('name')
                            ->label('Order Name')
                            ->required()
                            ->maxLength(255),

                        Select::make('stage')
                            ->options([
                                1 => 'Stage 1 - Initial',
                                2 => 'Stage 2 - Confirmed',
                                3 => 'Stage 3 - In Progress',
                                4 => 'Stage 4 - Review',
                                5 => 'Stage 5 - Completed',
                                6 => 'Stage 6 - Delivered',
                                7 => 'Stage 7 - Invoiced',
                            ])
                            ->default(1)
                            ->required(),
                    ])
                    ->columns(2),

                Section::make('Arrival Information')
                    ->schema([
                        DateTimePicker::make('arrival_time')
                            ->label('Arrival Time')
                            ->required()
                            ->default(now()),

                        Select::make('arrival_channel')
                            ->label('Arrival Channel')
                            ->options([
                                'email' => 'Email',
                                'whatsapp' => 'WhatsApp',
                                'website' => 'Website',
                                'phone' => 'Phone',
                                'walk_in' => 'Walk-in',
                            ])
                            ->required(),

                        TextInput::make('contact_person')
                            ->label('Contact Person')
                            ->maxLength(255),

                        DateTimePicker::make('deadline')
                            ->label('Deadline')
                            ->nullable(),
                    ])
                    ->columns(2),

                Section::make('Assignment & Flags')
                    ->schema([
                        Select::make('owner_id')
                            ->label('Project Manager (Owner)')
                            ->relationship('owner', 'name')
                            ->searchable()
                            ->required()
                            ->visible(fn () => auth()->user()->hasAnyRole(['superadmin', 'manager']))
                            ->disabled(fn () => !auth()->user()->hasAnyRole(['superadmin', 'manager'])),

                        Toggle::make('fast_track')
                            ->label('Fast Track')
                            ->default(false)
                            ->visible(fn () => auth()->user()->hasAnyRole(['superadmin', 'manager', 'pm']))
                            ->disabled(fn () => !auth()->user()->hasAnyRole(['superadmin', 'manager'])),

                        Toggle::make('priority')
                            ->label('Priority')
                            ->default(false)
                            ->visible(fn () => auth()->user()->hasAnyRole(['superadmin', 'manager', 'pm']))
                            ->disabled(fn () => !auth()->user()->hasAnyRole(['superadmin', 'manager'])),

                        Toggle::make('is_active')
                            ->label('Active')
                            ->default(true)
                            ->visible(fn () => auth()->user()->hasAnyRole(['superadmin', 'manager', 'pm']))
                            ->disabled(fn () => !auth()->user()->hasAnyRole(['superadmin', 'manager'])),
                    ])
                    ->columns(2)
                    ->visible(fn () => auth()->user()->hasAnyRole(['superadmin', 'manager', 'pm'])),

                Section::make('Financial Information')
                    ->schema([
                        TextInput::make('total_value')
                            ->label('Total Value')
                            ->numeric()
                            ->step(0.01)
                            ->suffix('€')
                            ->default(0)
                            ->visible(fn () => auth()->user()->canSeePricing())
                            ->disabled(fn () => !auth()->user()->hasAnyRole(['superadmin', 'manager'])),
                    ])
                    ->columns(1)
                    ->collapsible()
                    ->visible(fn () => auth()->user()->canSeePricing()),

                Section::make('Description')
                    ->schema([
                        Textarea::make('description')
                            ->rows(4)
                            ->columnSpanFull(),
                    ])
                    ->collapsible(),
            ]);
    }
}
