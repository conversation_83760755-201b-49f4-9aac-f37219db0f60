<?php

namespace App\Filament\App\Resources\Documents\Tables;

use App\Filament\App\Resources\Documents\DocumentResource;
use App\Models\GeneratedDocument;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteAction;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\ViewAction;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TernaryFilter;
use Filament\Tables\Table;

class DocumentsTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('document_number')
                    ->label('Document Number')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('title')
                    ->label('Title')
                    ->searchable()
                    ->limit(50),

                TextColumn::make('type')
                    ->label('Type')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'proforma' => 'info',
                        'invoice' => 'success',
                        'contract' => 'warning',
                        'guarantee' => 'primary',
                        'proces_verbal' => 'secondary',
                        'bill' => 'gray',
                        'note' => 'gray',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => GeneratedDocument::TYPES[$state] ?? $state)
                    ->sortable(),

                TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'draft' => 'gray',
                        'generated' => 'info',
                        'sent' => 'warning',
                        'signed' => 'success',
                        'archived' => 'secondary',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => GeneratedDocument::STATUSES[$state] ?? $state)
                    ->sortable(),

                TextColumn::make('comanda.internal_number')
                    ->label('Comandă')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('client.display_name')
                    ->label('Client')
                    ->searchable()
                    ->limit(30),

                TextColumn::make('template.name')
                    ->label('Template')
                    ->toggleable(isToggledHiddenByDefault: true),

                IconColumn::make('pdf_generated')
                    ->label('PDF')
                    ->boolean()
                    ->getStateUsing(fn ($record) => $record->isPdfGenerated())
                    ->sortable(),

                TextColumn::make('generatedBy.name')
                    ->label('Generated By')
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('pdf_generated_at')
                    ->label('PDF Generated')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(),
            ])
            ->filters([
                SelectFilter::make('type')
                    ->options(GeneratedDocument::TYPES),

                SelectFilter::make('status')
                    ->options(GeneratedDocument::STATUSES),

                TernaryFilter::make('pdf_generated')
                    ->label('PDF Status')
                    ->placeholder('All documents')
                    ->trueLabel('PDF generated')
                    ->falseLabel('PDF not generated')
                    ->queries(
                        true: fn ($query) => $query->whereNotNull('pdf_generated_at'),
                        false: fn ($query) => $query->whereNull('pdf_generated_at'),
                    ),

                SelectFilter::make('comanda_id')
                    ->relationship('comanda', 'internal_number')
                    ->searchable()
                    ->preload(),
            ])
            ->recordActions([
                ViewAction::make()
                    ->url(fn ($record) => DocumentResource::getUrl('view', ['record' => $record])),
                DeleteAction::make()
                    ->visible(fn ($record) => in_array($record->status, ['draft', 'generated'])),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make()
                        ->action(function ($records) {
                            // Delete draft and generated documents
                            $records->filter(fn ($record) => in_array($record->status, ['draft', 'generated']))
                                ->each->delete();
                        }),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }
}
