<?php

namespace App\Filament\App\Resources\Documents;

use App\Filament\App\Resources\Documents\Pages\CreateDocument;
use App\Filament\App\Resources\Documents\Pages\ListDocuments;
use App\Filament\App\Resources\Documents\Pages\ViewDocument;
use App\Filament\App\Resources\Documents\Schemas\DocumentForm;
use App\Filament\App\Resources\Documents\Tables\DocumentsTable;
use App\Models\GeneratedDocument;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class DocumentResource extends Resource
{
    protected static ?string $model = GeneratedDocument::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedDocumentText;

    protected static ?string $recordTitleAttribute = 'title';

    protected static ?string $navigationLabel = 'Documents';
    
    protected static \UnitEnum | string | null $navigationGroup = 'Documents';
    
    protected static ?int $navigationSort = 2;

    protected static ?string $pluralModelLabel = 'Generated Documents';

    public static function form(Schema $schema): Schema
    {
        return DocumentForm::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return DocumentsTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListDocuments::route('/'),
            'create' => CreateDocument::route('/create'),
            'view' => ViewDocument::route('/{record}'),
        ];
    }

    // RBAC Implementation
    public static function canViewAny(): bool
    {
        $user = auth()->user();

        if (!$user || !$user->is_active) {
            return false;
        }

        // All active users can view documents (filtered by role)
        return $user->hasAnyRole(['superadmin', 'manager', 'pm', 'specialist']);
    }

    public static function canView(Model $record): bool
    {
        $user = auth()->user();

        if (!$user || !$user->is_active) {
            return false;
        }

        return match($user->role) {
            'superadmin', 'manager' => true,
            'pm' => $record->comanda->owner_id === $user->id,
            'specialist' => $record->comanda->activities()->where('assigned_to', $user->id)->exists(),
            default => false
        };
    }

    public static function canCreate(): bool
    {
        $user = auth()->user();

        if (!$user || !$user->is_active) {
            return false;
        }

        // Only superadmin, manager, and PM can create documents
        return $user->hasAnyRole(['superadmin', 'manager', 'pm']);
    }

    public static function canEdit(Model $record): bool
    {
        $user = auth()->user();

        if (!$user || !$user->is_active) {
            return false;
        }

        // Can only edit draft documents
        if ($record->status !== 'draft') {
            return false;
        }

        return match($user->role) {
            'superadmin', 'manager' => true,
            'pm' => $record->comanda->owner_id === $user->id,
            'specialist' => false,
            default => false
        };
    }

    public static function canDelete(Model $record): bool
    {
        $user = auth()->user();

        if (!$user || !$user->is_active) {
            return false;
        }

        // Allow deletion of draft and generated documents (not sent/signed/archived)
        if (!in_array($record->status, ['draft', 'generated'])) {
            return false;
        }

        return match($user->role) {
            'superadmin' => true,
            'manager' => true,
            'pm' => $record->comanda->owner_id === $user->id,
            'specialist' => false,
            default => false
        };
    }

    public static function getEloquentQuery(): Builder
    {
        $user = auth()->user();

        if (!$user || !$user->is_active) {
            return parent::getEloquentQuery()->whereRaw('1 = 0');
        }

        $query = parent::getEloquentQuery()->with(['comanda', 'client', 'template', 'generatedBy']);

        return match($user->role) {
            'superadmin', 'manager' => $query,
            'pm' => $query->whereHas('comanda', function (Builder $q) use ($user) {
                $q->where('owner_id', $user->id);
            }),
            'specialist' => $query->whereHas('comanda.activities', function (Builder $q) use ($user) {
                $q->where('assigned_to', $user->id);
            }),
            default => $query->whereRaw('1 = 0')
        };
    }
}
