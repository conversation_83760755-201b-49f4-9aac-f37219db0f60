<?php

namespace App\Filament\App\Resources\Documents\Pages;

use App\Filament\App\Resources\Documents\DocumentResource;
use App\Services\DocumentGenerationService;
use Filament\Actions\Action;
use Filament\Resources\Pages\ViewRecord;
use Filament\Notifications\Notification;

class ViewDocument extends ViewRecord
{
    protected static string $resource = DocumentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Action::make('transformToProforma')
                ->label('Transformă în Proformă')
                ->icon('heroicon-o-arrow-right-circle')
                ->color('success')
                ->visible(fn () => $this->record->type === 'quotation')
                ->requiresConfirmation()
                ->modalHeading('Transformă Oferta în Proformă')
                ->modalDescription('Aceasta va crea o factură proformă bazată pe această ofertă, cu prețurile convertite în RON.')
                ->action(function () {
                    $quotation = $this->record;
                    $service = app(DocumentGenerationService::class);
                    
                    // Get the proforma template
                    $proformaTemplate = \App\Models\DocumentTemplate::active()
                        ->ofType('proforma')
                        ->default()
                        ->first();
                    
                    if (!$proformaTemplate) {
                        Notification::make()
                            ->danger()
                            ->title('Eroare')
                            ->body('Nu s-a găsit un template de proformă.')
                            ->send();
                        return;
                    }
                    
                    // Generate proforma title
                    $today = now()->format('d.m.Y');
                    $nextNumber = $this->getNextDocumentNumber('proforma');
                    $proformaTitle = "PROFORMĂ {$nextNumber} / {$today}";
                    
                    // Create proforma document
                    $proforma = $service->generateDocument(
                        comanda: $quotation->comanda,
                        template: $proformaTemplate,
                        customFields: $quotation->custom_field_values ?? [],
                        title: $proformaTitle
                    );
                    
                    // Generate PDF immediately
                    $service->generatePdf($proforma);
                    
                    Notification::make()
                        ->success()
                        ->title('Proformă creată')
                        ->body('Proforma a fost generată cu succes. Click pe butonul de download.')
                        ->send();
                    
                    // Redirect to the proforma view where user can download
                    $this->redirect($this->getResource()::getUrl('view', ['record' => $proforma]));
                }),

            Action::make('generatePdf')
                ->label('Generate PDF')
                ->icon('heroicon-o-document-arrow-down')
                ->visible(fn () => !$this->record->isPdfGenerated())
                ->action(function () {
                    $service = app(DocumentGenerationService::class);
                    $service->generatePdf($this->record);

                    $this->redirect($this->getResource()::getUrl('view', ['record' => $this->record]));
                }),

            Action::make('downloadPdf')
                ->label('Download PDF')
                ->icon('heroicon-o-arrow-down-tray')
                ->visible(fn () => $this->record->isPdfGenerated())
                ->url(fn () => $this->record->getDownloadUrl())
                ->openUrlInNewTab(false),

            Action::make('previewPdf')
                ->label('Preview PDF')
                ->icon('heroicon-o-eye')
                ->visible(fn () => $this->record->isPdfGenerated())
                ->url(fn () => $this->record->getPreviewUrl())
                ->openUrlInNewTab(true),
        ];
    }

    protected function getNextDocumentNumber(string $type): string
    {
        // Create a temporary model instance to use its generateDocumentNumber method
        $tempDoc = new \App\Models\GeneratedDocument(['type' => $type]);
        return $tempDoc->generateDocumentNumber();
    }
}
