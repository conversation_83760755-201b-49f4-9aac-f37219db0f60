<?php

namespace App\Filament\App\Resources\Documents\Schemas;

use App\Models\Comanda;
use App\Models\DocumentTemplate;
use App\Models\GeneratedDocument;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\KeyValue;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class DocumentForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make('Document Information')
                    ->schema([
                        Select::make('type')
                            ->label('Document Type')
                            ->options(GeneratedDocument::TYPES)
                            ->required()
                            ->reactive()
                            ->afterStateUpdated(function ($state, callable $set) {
                                // Clear template when type changes
                                $set('template_id', null);
                            }),

                        Select::make('template_id')
                            ->label('Template')
                            ->options(function (callable $get) {
                                $type = $get('type');
                                if (!$type) {
                                    return [];
                                }
                                
                                return DocumentTemplate::active()
                                    ->ofType($type)
                                    ->pluck('name', 'id')
                                    ->toArray();
                            })
                            ->required()
                            ->reactive(),

                        TextInput::make('title')
                            ->label('Document Title')
                            ->required()
                            ->maxLength(255),

                        Textarea::make('description')
                            ->label('Description')
                            ->rows(3)
                            ->columnSpanFull(),
                    ])
                    ->columns(2),

                Section::make('Related Records')
                    ->schema([
                        Select::make('comanda_id')
                            ->label('Comandă')
                            ->relationship('comanda', 'internal_number')
                            ->getOptionLabelFromRecordUsing(fn ($record) => "{$record->internal_number} - {$record->name}")
                            ->searchable(['internal_number', 'name'])
                            ->required()
                            ->reactive()
                            ->afterStateUpdated(function ($state, callable $set) {
                                if ($state) {
                                    $comanda = Comanda::find($state);
                                    if ($comanda) {
                                        $set('client_id', $comanda->client_id);
                                    }
                                }
                            }),

                        Select::make('client_id')
                            ->label('Client')
                            ->relationship('client', 'company_name')
                            ->getOptionLabelFromRecordUsing(fn ($record) => $record->company_name ?: $record->name)
                            ->searchable(['company_name', 'name'])
                            ->required()
                            ->disabled(),
                    ])
                    ->columns(2),

                Section::make('Custom Fields')
                    ->schema([
                        KeyValue::make('custom_field_values')
                            ->label('Custom Field Values')
                            ->helperText('Additional fields specific to this document')
                            ->keyLabel('Field Name')
                            ->valueLabel('Value')
                            ->columnSpanFull(),
                    ])
                    ->collapsible()
                    ->collapsed(),

                Section::make('Status Information')
                    ->schema([
                        Select::make('status')
                            ->label('Status')
                            ->options(GeneratedDocument::STATUSES)
                            ->default('draft')
                            ->required()
                            ->visible(fn ($context) => $context === 'edit'),
                    ])
                    ->visible(fn ($context) => $context === 'edit')
                    ->collapsible(),
            ]);
    }
}
