<?php

namespace App\Filament\App\Widgets;

use App\Models\Meeting;
use App\Models\Installation;
use App\Models\ServiceTour;
use App\Models\Unavailability;
use App\Models\Deadline;
use Guava\Calendar\Filament\CalendarWidget as BaseCalendarWidget;
use Guava\Calendar\ValueObjects\FetchInfo;
use Guava\Calendar\Filament\Actions\CreateAction;
use Guava\Calendar\Filament\Actions\EditAction;
use Guava\Calendar\Filament\Actions\ViewAction;
use Guava\Calendar\Filament\Actions\DeleteAction;
use Guava\Calendar\ValueObjects\DateClickInfo;
use Guava\Calendar\ValueObjects\CalendarEvent;
use Guava\Calendar\Contracts\Eventable;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Hidden;
use Filament\Schemas\Schema;
use Filament\Actions\Action;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

class CalendarWidget extends BaseCalendarWidget
{
    // Prevent this widget from appearing on the dashboard
    protected static bool $isDiscovered = false;

    protected bool $eventClickEnabled = true;
    protected bool $dateClickEnabled = true;

    public bool $showMeetings = true;
    public bool $showInstallations = true;
    public bool $showServiceTours = true;
    public bool $showUnavailabilities = true;
    public bool $showDeadlines = true;

    protected array $options = [
        'allDaySlot' => true,
        'slotMinTime' => '07:00:00',
        'slotMaxTime' => '22:00:00',
        'eventStartEditable' => false,
        'eventDurationEditable' => false,
        'height' => 'auto',
        'contentHeight' => 'auto',
    ];

    public function getHeading(): ?string
    {
        return null; // Remove the heading
    }

    public function getHeaderActions(): array
    {
        return [
            Action::make('toggleMeetings')
                ->label($this->showMeetings ? 'Meetings' : 'Meetings')
                ->color($this->showMeetings ? 'info' : 'gray') // Blue (#3B82F6)
                ->button()
                ->action(function () {
                    $this->showMeetings = !$this->showMeetings;
                    $this->refreshRecords();
                }),

            Action::make('toggleInstallations')
                ->label($this->showInstallations ? 'Installations' : 'Installations')
                ->extraAttributes([
                    'style' => $this->showInstallations
                        ? 'background-color: #1E40AF; color: white;' // Dark Blue
                        : 'background-color: #9CA3AF; color: white;' // Gray
                ])
                ->button()
                ->action(function () {
                    $this->showInstallations = !$this->showInstallations;
                    $this->refreshRecords();
                }),

            Action::make('toggleServiceTours')
                ->label($this->showServiceTours ? 'Service Tours' : 'Service Tours')
                ->extraAttributes([
                    'style' => $this->showServiceTours
                        ? 'background-color: #9333EA; color: white;' // Purple
                        : 'background-color: #9CA3AF; color: white;' // Gray
                ])
                ->button()
                ->action(function () {
                    $this->showServiceTours = !$this->showServiceTours;
                    $this->refreshRecords();
                }),

            Action::make('toggleUnavailabilities')
                ->label($this->showUnavailabilities ? 'Unavailabilities' : 'Unavailabilities')
                ->extraAttributes([
                    'style' => $this->showUnavailabilities
                        ? 'background-color: #EAB308; color: black;' // Yellow
                        : 'background-color: #9CA3AF; color: white;' // Gray
                ])
                ->button()
                ->action(function () {
                    $this->showUnavailabilities = !$this->showUnavailabilities;
                    $this->refreshRecords();
                }),

            Action::make('toggleDeadlines')
                ->label($this->showDeadlines ? 'Deadlines' : 'Deadlines')
                ->extraAttributes([
                    'style' => $this->showDeadlines
                        ? 'background-color: #DC2626; color: white;' // Red
                        : 'background-color: #9CA3AF; color: white;' // Gray
                ])
                ->button()
                ->action(function () {
                    $this->showDeadlines = !$this->showDeadlines;
                    $this->refreshRecords();
                }),
        ];
    }



    protected function getEvents(FetchInfo $info): Collection | array | Builder
    {
        // Fetch meetings
        $meetings = Meeting::query()
            ->whereDate('date', '>=', $info->start)
            ->whereDate('date', '<=', $info->end)
            ->with('creator')
            ->get();

        // Fetch installations
        $installations = Installation::query()
            ->whereDate('date', '>=', $info->start)
            ->whereDate('date', '<=', $info->end)
            ->with('creator')
            ->get();

        // Fetch service tours (multi-day events)
        $serviceTours = ServiceTour::query()
            ->where(function ($query) use ($info) {
                $query->whereDate('end_date', '>=', $info->start)
                      ->whereDate('start_date', '<=', $info->end);
            })
            ->with('creator')
            ->get();

        // Fetch unavailabilities (multi-day events)
        $unavailabilities = Unavailability::query()
            ->where(function ($query) use ($info) {
                $query->whereDate('end_date', '>=', $info->start)
                      ->whereDate('start_date', '<=', $info->end);
            })
            ->with(['user', 'creator'])
            ->get();

        // Fetch deadlines (single-day events)
        $deadlines = Deadline::query()
            ->whereDate('deadline_date', '>=', $info->start)
            ->whereDate('deadline_date', '<=', $info->end)
            ->with('comanda')
            ->get();

        \Log::info('Calendar Events Fetched', [
            'fetch_start' => $info->start,
            'fetch_end' => $info->end,
            'meetings_count' => $meetings->count(),
            'installations_count' => $installations->count(),
            'service_tours_count' => $serviceTours->count(),
            'unavailabilities_count' => $unavailabilities->count(),
            'deadlines_count' => $deadlines->count(),
            'service_tours' => $serviceTours->map(fn($t) => [
                'title' => $t->title,
                'start' => $t->start_date->format('Y-m-d'),
                'end' => $t->end_date->format('Y-m-d'),
            ])->toArray(),
        ]);

        // Combine all events based on filters
        $events = collect();

        if ($this->showMeetings) {
            $events = $events->merge($meetings);
        }
        if ($this->showInstallations) {
            $events = $events->merge($installations);
        }
        if ($this->showServiceTours) {
            $events = $events->merge($serviceTours);
        }
        if ($this->showUnavailabilities) {
            $events = $events->merge($unavailabilities);
        }
        if ($this->showDeadlines) {
            $events = $events->merge($deadlines);
        }

        return $events;
    }

    public function getEventsJs(array $info): array
    {
        $events = $this->getEvents(new FetchInfo($info));

        if ($events instanceof Builder) {
            $events = $events->get();
        }

        if (is_array($events)) {
            $events = collect($events);
        }

        $result = $events
            ->map(function (Builder | Collection | Eventable | CalendarEvent $event) use ($info): array {
                if ($event instanceof Eventable) {
                    $event = $event->toCalendarEvent();
                }

                // For all-day events, we need to bypass timezone conversion
                $isAllDay = $event->getAllDay();

                if ($isAllDay) {
                    // Manually build the calendar object
                    // Send dates with explicit +03:00 timezone to prevent conversion
                    $calendarObject = [
                        'title' => $event->getTitle(),
                        'start' => $event->getStart()->setTimezone('Europe/Bucharest')->format('Y-m-d\T00:00:00P'),
                        'end' => $event->getEnd()->setTimezone('Europe/Bucharest')->format('Y-m-d\T00:00:00P'),
                        'allDay' => true,
                        'backgroundColor' => $event->getBackgroundColor(),
                        'textColor' => $event->getTextColor(),
                        'styles' => $event->getStyles(),
                        'classNames' => $event->getClassNames(),
                        'resourceIds' => $event->getResourceIds(),
                        'extendedProps' => $event->getExtendedProps(),
                    ];

                    if (($editable = $event->getEditable()) !== null) {
                        $calendarObject['editable'] = $editable;
                    }
                    if (($startEditable = $event->getStartEditable()) !== null) {
                        $calendarObject['startEditable'] = $startEditable;
                    }
                    if (($durationEditable = $event->getDurationEditable()) !== null) {
                        $calendarObject['durationEditable'] = $durationEditable;
                    }
                    if (($display = $event->getDisplay()) !== null) {
                        $calendarObject['display'] = $display;
                    }

                    \Log::info('All-day event - MANUAL BUILD', $calendarObject);
                } else {
                    $calendarObject = $event->toCalendarObject(
                        data_get($info, 'tzOffset'),
                        $this->shouldUseFilamentTimezone()
                    );
                }

                return $calendarObject;
            })
            ->all();

        \Log::info('Total events being returned to frontend', ['count' => count($result)]);

        return $result;
    }

    // Removed getCalendarOptions() - using Guava Calendar's built-in methods instead

    public function createMeetingAction(): CreateAction
    {
        return $this->createAction(Meeting::class)
            ->label('Add Meeting')
            ->icon('heroicon-m-user-group')
            ->form($this->getMeetingFormSchema())
            ->mutateFormDataUsing(function (array $data): array {
                $data['created_by'] = auth()->id();
                return $data;
            })
            ->mountUsing(function (?DateClickInfo $info, $form) {
                if ($info) {
                    $form->fill([
                        'date' => $info->date,
                    ]);
                }
            });
    }

    public function createInstallationAction(): CreateAction
    {
        return $this->createAction(Installation::class)
            ->label('Add Installation')
            ->icon('heroicon-m-wrench-screwdriver')
            ->form($this->getInstallationFormSchema())
            ->mutateFormDataUsing(function (array $data): array {
                $data['created_by'] = auth()->id();
                return $data;
            })
            ->mountUsing(function (?DateClickInfo $info, $form) {
                if ($info) {
                    $form->fill([
                        'date' => $info->date,
                    ]);
                }
            });
    }

    public function createServiceTourAction(): CreateAction
    {
        return $this->createAction(ServiceTour::class)
            ->label('Add Service Tour')
            ->icon('heroicon-m-truck')
            ->form($this->getServiceTourFormSchema())
            ->mutateFormDataUsing(function (array $data): array {
                $data['created_by'] = auth()->id();
                return $data;
            })
            ->mountUsing(function (?DateClickInfo $info, $form) {
                if ($info) {
                    $form->fill([
                        'start_date' => $info->date,
                        'end_date' => $info->date,
                    ]);
                }
            });
    }

    public function createUnavailabilityAction(): CreateAction
    {
        return $this->createAction(Unavailability::class)
            ->label('Add Unavailability')
            ->icon('heroicon-m-no-symbol')
            ->form($this->getUnavailabilityFormSchema())
            ->mutateFormDataUsing(function (array $data): array {
                $data['created_by'] = auth()->id();
                return $data;
            })
            ->mountUsing(function (?DateClickInfo $info, $form) {
                if ($info) {
                    $form->fill([
                        'start_date' => $info->date,
                        'end_date' => $info->date,
                    ]);
                }
            });
    }

    protected function getDateClickContextMenuActions(): array
    {
        return [
            $this->createMeetingAction(),
            $this->createInstallationAction(),
            $this->createServiceTourAction(),
            $this->createUnavailabilityAction(),
        ];
    }

    protected function getEventClickContextMenuActions(): array
    {
        return [
            $this->viewAction(),
            $this->editAction(),
            $this->deleteAction(),
        ];
    }

    protected function getMeetingFormSchema(): array
    {
        return [
            TextInput::make('title')
                ->required()
                ->label('Title')
                ->maxLength(255),

            DatePicker::make('date')
                ->required()
                ->label('Date')
                ->native(false),

            TextInput::make('start_time')
                ->required()
                ->label('Start Time')
                ->type('time')
                ->placeholder('09:00')
                ->helperText('Select the start time'),

            TextInput::make('end_time')
                ->label('End Time (Optional)')
                ->type('time')
                ->placeholder('10:00')
                ->helperText('Select the end time (optional)')
                ->afterOrEqual('start_time'),

            Textarea::make('description')
                ->label('Description')
                ->rows(3),
        ];
    }

    protected function getInstallationFormSchema(): array
    {
        return [
            TextInput::make('title')
                ->required()
                ->label('Title')
                ->maxLength(255),

            DatePicker::make('date')
                ->required()
                ->label('Date')
                ->native(false),

            TextInput::make('start_time')
                ->required()
                ->label('Start Time')
                ->type('time')
                ->placeholder('09:00')
                ->helperText('Select the start time'),

            TextInput::make('end_time')
                ->label('End Time (Optional)')
                ->type('time')
                ->placeholder('10:00')
                ->helperText('Select the end time (optional)')
                ->afterOrEqual('start_time'),

            Textarea::make('description')
                ->label('Description')
                ->rows(3),
        ];
    }

    protected function getServiceTourFormSchema(): array
    {
        return [
            TextInput::make('title')
                ->required()
                ->label('Title')
                ->maxLength(255),

            DatePicker::make('start_date')
                ->required()
                ->label('Start Date')
                ->native(false),

            DatePicker::make('end_date')
                ->required()
                ->label('End Date')
                ->native(false)
                ->after('start_date'),

            Textarea::make('description')
                ->label('Description')
                ->rows(3),
        ];
    }

    protected function getUnavailabilityFormSchema(): array
    {
        return [
            Select::make('user_id')
                ->required()
                ->label('User')
                ->relationship('user', 'name')
                ->searchable()
                ->preload(),

            Select::make('type')
                ->required()
                ->label('Type')
                ->options([
                    'sick_leave' => 'Sick Leave',
                    'personal_time_off' => 'Personal Time Off',
                    'unpaid_time_off' => 'Unpaid Time Off',
                ]),

            DatePicker::make('start_date')
                ->required()
                ->label('Start Date')
                ->native(false),

            DatePicker::make('end_date')
                ->required()
                ->label('End Date')
                ->native(false)
                ->afterOrEqual('start_date'),

            Textarea::make('reason')
                ->label('Reason (Optional)')
                ->rows(3),
        ];
    }

    public function meetingSchema(Schema $schema): Schema
    {
        return $schema->components($this->getMeetingFormSchema());
    }

    public function installationSchema(Schema $schema): Schema
    {
        return $schema->components($this->getInstallationFormSchema());
    }

    public function serviceTourSchema(Schema $schema): Schema
    {
        return $schema->components($this->getServiceTourFormSchema());
    }

    public function unavailabilitySchema(Schema $schema): Schema
    {
        return $schema->components($this->getUnavailabilityFormSchema());
    }
}

