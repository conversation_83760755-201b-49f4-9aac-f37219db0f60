<?php

namespace App\Filament\App\Widgets;

use App\Models\ComandaActivity;
use App\Models\File;
use Filament\Actions\Action;
use Filament\Actions\Concerns\InteractsWithActions;
use Filament\Actions\Contracts\HasActions;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Notifications\Notification;
use Filament\Widgets\Widget;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\StreamedResponse;

class MyActivitiesWidget extends Widget implements HasForms, HasActions
{
    use InteractsWithForms;
    use InteractsWithActions;
    
    protected string $view = 'filament.app.widgets.my-activities-widget';
    
    protected int | string | array $columnSpan = 'full';
    
    public function getActivities()
    {
        return ComandaActivity::query()
            ->where('assigned_to', Auth::id())
            ->where('is_done', false)
            ->with(['comanda.client', 'files', 'activityType'])
            ->orderBy('created_at', 'desc')
            ->paginate(6);
    }
    
    public function markAsDoneAction(): Action
    {
        return Action::make('markAsDone')
            ->label('Marchează ca finalizat')
            ->icon('heroicon-o-check-circle')
            ->color('success')
            ->requiresConfirmation()
            ->modalHeading('Marchează activitatea ca finalizată')
            ->modalDescription('Ești sigur că vrei să marchezi această activitate ca finalizată? Această acțiune nu poate fi anulată.')
            ->modalSubmitActionLabel('Da, marchează ca finalizată')
            ->modalCancelActionLabel('Anulează')
            ->action(function (array $arguments) {
                $activityId = $arguments['activity'];
                $activity = ComandaActivity::findOrFail($activityId);
                
                // Verify the activity is assigned to the current user
                if ($activity->assigned_to !== Auth::id()) {
                    Notification::make()
                        ->title('Neautorizat')
                        ->body('Poți marca ca finalizate doar activitățile tale.')
                        ->danger()
                        ->send();
                    return;
                }
                
                $activity->markAsDone();
                
                Notification::make()
                    ->title('Succes!')
                    ->body("Activitatea a fost marcată ca finalizată.")
                    ->success()
                    ->send();
                
                // Refresh the widget
                $this->dispatch('$refresh');
            });
    }
    
    public function downloadFile($fileId)
    {
        $file = File::findOrFail($fileId);
        
        // Verify the file belongs to an activity assigned to the current user
        $activity = $file->fileable;
        if (!$activity || $activity->assigned_to !== Auth::id()) {
            abort(403, 'Unauthorized access to file');
        }
        
        if (!Storage::disk('private')->exists($file->path)) {
            abort(404, 'File not found');
        }
        
        return Storage::disk('private')->download($file->path, $file->original_name);
    }
    
    public function downloadAttachmentFile($activityId, $filePath)
    {
        $activity = ComandaActivity::findOrFail($activityId);
        
        // Decode the URL-encoded file path
        $filePath = urldecode($filePath);
        
        // Verify the activity is assigned to the current user
        if ($activity->assigned_to !== Auth::id()) {
            abort(403, 'Unauthorized access to file');
        }
        
        // Verify the file path is in the activity's attachments
        $attachments = $activity->attachments ?? [];
        if (!in_array($filePath, $attachments)) {
            abort(403, 'File not associated with this activity');
        }
        
        if (!Storage::disk('private')->exists($filePath)) {
            abort(404, 'File not found');
        }
        
        return Storage::disk('private')->download($filePath, basename($filePath));
    }
    
    public function uploadFileAction(): Action
    {
        return Action::make('uploadFile')
            ->label('Încarcă fișier')
            ->icon('heroicon-o-paper-clip')
            ->color('gray')
            ->form([
                FileUpload::make('files')
                    ->label('Fișiere')
                    ->multiple()
                    ->disk('local')
                    ->directory(fn ($get) => "activity-files")
                    ->visibility('private')
                    ->previewable(true)
                    ->downloadable(true)
                    ->openable()
                    ->deletable(true)
                    ->maxSize(10240)
                    ->acceptedFileTypes([
                        'image/*',
                        'application/pdf',
                        '.doc', '.docx',
                        '.xls', '.xlsx',
                        '.txt', '.csv'
                    ])
                    ->preserveFilenames()
                    ->required()
            ])
            ->action(function (array $data, array $arguments) {
                $activity = ComandaActivity::findOrFail($arguments['activity']);
                
                // Verify the activity is assigned to the current user
                if ($activity->assigned_to !== Auth::id()) {
                    Notification::make()
                        ->title('Neautorizat')
                        ->body('Nu poți încărca fișiere pentru activitățile altora.')
                        ->danger()
                        ->send();
                    return;
                }
                
                // Process uploaded files
                if (isset($data['files']) && is_array($data['files'])) {
                    foreach ($data['files'] as $filePath) {
                        $file = File::create([
                            'uuid' => \Illuminate\Support\Str::uuid(),
                            'original_name' => basename($filePath),
                            'stored_name' => basename($filePath),
                            'path' => $filePath,
                            'disk' => 'local',
                            'size' => Storage::disk('local')->size($filePath),
                            'mime_type' => Storage::disk('local')->mimeType($filePath),
                            'extension' => pathinfo($filePath, PATHINFO_EXTENSION),
                            'fileable_type' => ComandaActivity::class,
                            'fileable_id' => $activity->id,
                            'category' => File::CATEGORY_ACTIVITY_INPUT,
                            'uploaded_by' => Auth::id(),
                            'is_public' => false,
                        ]);
                    }
                    
                    Notification::make()
                        ->title('Succes!')
                        ->body('Fișier(e)le au fost încărcate cu succes.')
                        ->success()
                        ->send();
                }
                
                // Refresh the widget
                $this->dispatch('$refresh');
            });
    }
}

