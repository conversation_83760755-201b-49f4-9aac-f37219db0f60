<?php

namespace App\Filament\Admin\Resources\ActivityTypes\Schemas;

use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Schemas\Schema;

class ActivityTypeForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make('Activity Type Details')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextInput::make('name')
                                    ->label('Name')
                                    ->required()
                                    ->maxLength(255)
                                    ->placeholder('e.g., Client Meeting')
                                    ->columnSpan(1),

                                TextInput::make('key')
                                    ->label('Key')
                                    ->required()
                                    ->unique(ignoreRecord: true)
                                    ->maxLength(255)
                                    ->placeholder('e.g., client_meeting')
                                    ->helperText('Unique identifier (lowercase, underscores only)')
                                    ->regex('/^[a-z_]+$/')
                                    ->columnSpan(1),

                                Select::make('stage')
                                    ->label('Stage')
                                    ->required()
                                    ->options([
                                        1 => 'Stage 1 - Ofertare',
                                        2 => 'Stage 2 - Contractare',
                                        3 => 'Stage 3 - Pregatire',
                                        4 => 'Stage 4 - Aprovizionare',
                                        5 => 'Stage 5 - Executie',
                                        6 => 'Stage 6 - Livrare',
                                        7 => 'Stage 7 - Facturare',
                                    ])
                                    ->searchable()
                                    ->columnSpan(1),

                                TextInput::make('sort_order')
                                    ->label('Sort Order')
                                    ->numeric()
                                    ->default(0)
                                    ->required()
                                    ->helperText('Lower numbers appear first')
                                    ->columnSpan(1),

                                Textarea::make('description')
                                    ->label('Description')
                                    ->rows(3)
                                    ->maxLength(65535)
                                    ->columnSpanFull(),

                                Toggle::make('is_active')
                                    ->label('Active')
                                    ->default(true)
                                    ->helperText('Inactive types will not appear in dropdowns')
                                    ->columnSpanFull(),
                            ]),
                    ]),
            ]);
    }
}
