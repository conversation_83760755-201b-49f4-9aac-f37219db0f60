<?php

namespace App\Filament\Admin\Resources\DocumentTemplates\Tables;

use App\Models\DocumentTemplate;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteAction;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TernaryFilter;
use Filament\Tables\Table;

class DocumentTemplatesTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label('Template Name')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('type')
                    ->label('Type')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'proforma' => 'info',
                        'invoice' => 'success',
                        'contract' => 'warning',
                        'guarantee' => 'primary',
                        'proces_verbal' => 'secondary',
                        'bill' => 'gray',
                        'note' => 'gray',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => DocumentTemplate::TYPES[$state] ?? $state)
                    ->sortable(),

                TextColumn::make('blade_template')
                    ->label('Template File')
                    ->formatStateUsing(fn (string $state): string => $state . '.blade.php')
                    ->toggleable(),

                IconColumn::make('is_active')
                    ->label('Active')
                    ->boolean()
                    ->sortable(),

                IconColumn::make('is_default')
                    ->label('Default')
                    ->boolean()
                    ->sortable(),

                IconColumn::make('requires_approval')
                    ->label('Requires Approval')
                    ->boolean()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('version')
                    ->label('Version')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('language')
                    ->label('Language')
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => strtoupper($state))
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('generatedDocuments_count')
                    ->label('Documents Generated')
                    ->counts('generatedDocuments')
                    ->sortable()
                    ->toggleable(),

                TextColumn::make('createdBy.name')
                    ->label('Created By')
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('updated_at')
                    ->label('Updated')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('type')
                    ->options(DocumentTemplate::TYPES),

                TernaryFilter::make('is_active')
                    ->label('Active Status')
                    ->placeholder('All templates')
                    ->trueLabel('Active templates only')
                    ->falseLabel('Inactive templates only'),

                TernaryFilter::make('is_default')
                    ->label('Default Templates')
                    ->placeholder('All templates')
                    ->trueLabel('Default templates only')
                    ->falseLabel('Non-default templates only'),

                SelectFilter::make('language')
                    ->options([
                        'ro' => 'Romanian',
                        'en' => 'English',
                    ]),
            ])
            ->actions([
                EditAction::make(),
                DeleteAction::make()
                    ->visible(fn ($record) => $record->generatedDocuments()->count() === 0),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make()
                        ->action(function ($records) {
                            // Only delete templates with no generated documents
                            $records->filter(fn ($record) => $record->generatedDocuments()->count() === 0)
                                ->each->delete();
                        }),
                ]),
            ])
            ->defaultSort('type')
            ->defaultSort('name');
    }
}
