<?php

namespace App\Filament\Admin\Resources\DocumentTemplates;

use App\Filament\Admin\Resources\DocumentTemplates\Pages\CreateDocumentTemplate;
use App\Filament\Admin\Resources\DocumentTemplates\Pages\EditDocumentTemplate;
use App\Filament\Admin\Resources\DocumentTemplates\Pages\ListDocumentTemplates;
use App\Filament\Admin\Resources\DocumentTemplates\Schemas\DocumentTemplateForm;
use App\Filament\Admin\Resources\DocumentTemplates\Tables\DocumentTemplatesTable;
use App\Models\DocumentTemplate;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class DocumentTemplateResource extends Resource
{
    protected static ?string $model = DocumentTemplate::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedDocumentText;

    protected static ?string $recordTitleAttribute = 'name';

    protected static ?int $navigationSort = 2;

    protected static ?string $navigationLabel = 'Document Templates';

    protected static ?string $pluralModelLabel = 'Document Templates';

    public static function form(Schema $schema): Schema
    {
        return DocumentTemplateForm::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return DocumentTemplatesTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListDocumentTemplates::route('/'),
            'create' => CreateDocumentTemplate::route('/create'),
            'edit' => EditDocumentTemplate::route('/{record}/edit'),
        ];
    }

    // RBAC Implementation - Admin panel is superadmin only
    public static function canViewAny(): bool
    {
        $user = auth()->user();
        
        if (!$user || !$user->is_active) {
            return false;
        }

        // Only superadmin can manage document templates
        return $user->isRole('superadmin');
    }

    public static function canView(Model $record): bool
    {
        return static::canViewAny();
    }

    public static function canCreate(): bool
    {
        return static::canViewAny();
    }

    public static function canEdit(Model $record): bool
    {
        return static::canViewAny();
    }

    public static function canDelete(Model $record): bool
    {
        $user = auth()->user();
        
        if (!$user || !$user->is_active || !$user->isRole('superadmin')) {
            return false;
        }

        // Cannot delete if there are generated documents using this template
        return $record->generatedDocuments()->count() === 0;
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()->with(['createdBy', 'updatedBy'])->orderBy('type')->orderBy('name');
    }
}
