<?php

namespace App\Filament\Admin\Resources\DocumentTemplates\Schemas;

use App\Models\DocumentTemplate;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\KeyValue;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;

class DocumentTemplateForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make('Basic Information')
                    ->schema([
                        TextInput::make('name')
                            ->label('Template Name')
                            ->required()
                            ->maxLength(255),

                        Select::make('type')
                            ->label('Document Type')
                            ->options(DocumentTemplate::TYPES)
                            ->required()
                            ->reactive()
                            ->afterStateUpdated(function ($state, callable $set) {
                                // Auto-set blade template based on type
                                $set('blade_template', $state === 'proforma' ? 'proforma' : 'default');
                            }),

                        TextInput::make('blade_template')
                            ->label('Blade Template')
                            ->required()
                            ->helperText('Template file name (without .blade.php extension)')
                            ->maxLength(255),

                        Select::make('language')
                            ->label('Language')
                            ->options([
                                'ro' => 'Romanian',
                                'en' => 'English',
                            ])
                            ->default('ro')
                            ->required(),
                    ])
                    ->columns(2),

                Section::make('Configuration')
                    ->schema([
                        Checkbox::make('is_active')
                            ->label('Active Template')
                            ->default(true)
                            ->helperText('Only active templates can be used for document generation'),

                        Checkbox::make('is_default')
                            ->label('Default Template')
                            ->helperText('Default template for this document type'),

                        Checkbox::make('requires_approval')
                            ->label('Requires Approval')
                            ->helperText('Generated documents need approval before being finalized'),

                        TextInput::make('css_framework')
                            ->label('CSS Framework')
                            ->default('page.js')
                            ->maxLength(255),
                    ])
                    ->columns(2),

                Section::make('Custom Styling')
                    ->schema([
                        Textarea::make('custom_css')
                            ->label('Custom CSS')
                            ->rows(10)
                            ->helperText('Additional CSS styles for this template')
                            ->columnSpanFull(),
                    ])
                    ->collapsible()
                    ->collapsed(),

                Section::make('Field Configuration')
                    ->schema([
                        KeyValue::make('required_fields')
                            ->label('Required Fields')
                            ->helperText('Fields that must be provided when generating documents')
                            ->keyLabel('Field Name')
                            ->valueLabel('Description'),

                        KeyValue::make('optional_fields')
                            ->label('Optional Fields')
                            ->helperText('Fields that can be provided when generating documents')
                            ->keyLabel('Field Name')
                            ->valueLabel('Description'),
                    ])
                    ->columns(2)
                    ->collapsible(),

                Section::make('Custom Fields')
                    ->schema([
                        Textarea::make('custom_fields')
                            ->label('Custom Fields Configuration')
                            ->helperText('JSON configuration for custom fields (advanced)')
                            ->rows(8)
                            ->formatStateUsing(fn ($state) => $state ? json_encode($state, JSON_PRETTY_PRINT) : '')
                            ->dehydrateStateUsing(function ($state) {
                                if (empty($state)) {
                                    return null;
                                }

                                $decoded = json_decode($state, true);
                                return json_last_error() === JSON_ERROR_NONE ? $decoded : null;
                            })
                            ->columnSpanFull(),
                    ])
                    ->collapsible()
                    ->collapsed(),

                Section::make('Romanian Compliance')
                    ->schema([
                        Textarea::make('fiscal_requirements')
                            ->label('Fiscal Requirements')
                            ->helperText('JSON configuration for Romanian fiscal requirements')
                            ->rows(6)
                            ->formatStateUsing(fn ($state) => $state ? json_encode($state, JSON_PRETTY_PRINT) : '')
                            ->dehydrateStateUsing(function ($state) {
                                if (empty($state)) {
                                    return null;
                                }

                                $decoded = json_decode($state, true);
                                return json_last_error() === JSON_ERROR_NONE ? $decoded : null;
                            })
                            ->columnSpanFull(),
                    ])
                    ->collapsible()
                    ->collapsed(),
            ]);
    }
}
