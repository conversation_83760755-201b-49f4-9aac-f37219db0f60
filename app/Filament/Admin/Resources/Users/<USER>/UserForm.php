<?php

namespace App\Filament\Admin\Resources\Users\Schemas;

use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\DateTimePicker;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;
use Illuminate\Support\Facades\Hash;

class UserForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make('User Information')
                    ->schema([
                        TextInput::make('name')
                            ->label('Full Name')
                            ->required()
                            ->maxLength(255),

                        TextInput::make('email')
                            ->label('Email Address')
                            ->email()
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->maxLength(255),

                        Select::make('role')
                            ->label('User Role')
                            ->options([
                                'superadmin' => 'Superadmin (Developer)',
                                'manager' => 'Manager',
                                'pm' => 'Project Manager',
                                'specialist' => 'Specialist',
                            ])
                            ->required()
                            ->default('specialist')
                            ->helperText('Role determines panel access and permissions'),

                        Toggle::make('is_active')
                            ->label('Active User')
                            ->default(true)
                            ->helperText('Inactive users cannot log in'),

                        DateTimePicker::make('email_verified_at')
                            ->label('Email Verified At')
                            ->helperText('Leave empty for unverified email'),
                    ])
                    ->columns(2),

                Section::make('Password')
                    ->schema([
                        TextInput::make('password')
                            ->label('Password')
                            ->password()
                            ->dehydrateStateUsing(fn ($state) => filled($state) ? Hash::make($state) : null)
                            ->dehydrated(fn ($state) => filled($state))
                            ->required(fn (string $context): bool => $context === 'create')
                            ->minLength(8)
                            ->helperText('Leave empty to keep current password (when editing)'),

                        TextInput::make('password_confirmation')
                            ->label('Confirm Password')
                            ->password()
                            ->same('password')
                            ->dehydrated(false)
                            ->required(fn (string $context): bool => $context === 'create'),
                    ])
                    ->columns(2)
                    ->collapsible(),

                Section::make('Role Information')
                    ->schema([
                        TextInput::make('role_description')
                            ->label('Role Description')
                            ->disabled()
                            ->dehydrated(false)
                            ->formatStateUsing(function ($get) {
                                return match($get('role')) {
                                    'superadmin' => 'Full system access, user management, system configuration',
                                    'manager' => 'Business operations, all panels except Admin, full pricing visibility',
                                    'pm' => 'Project management, App and Definitions panels, own comandă management',
                                    'specialist' => 'Task execution, Productie panel, assigned work only',
                                    default => 'No role selected'
                                };
                            })
                            ->columnSpanFull(),
                    ])
                    ->collapsible()
                    ->collapsed(),
            ]);
    }
}
