<?php

namespace App\Services;

class SubstageConfiguration
{
    /**
     * Available substages for each stage
     */
    public const STAGE_SUBSTAGES = [
        1 => [ // Ofertare
            'masuratori' => [
                'name' => 'Măsurători',
                'description' => 'Measurements and site survey',
                'scope' => 'both',
                'requires_approval' => false,
            ],
            'design_mockups' => [
                'name' => 'Design / machete',
                'description' => 'Design work and visual mockups',
                'scope' => 'item',
                'requires_approval' => true,
            ],
            'grafica_ofertare' => [
                'name' => 'Grafică',
                'description' => 'Graphic design for quotation',
                'scope' => 'item',
                'requires_approval' => true,
            ],
            'cost_calculation' => [
                'name' => 'Calculul costurilor',
                'description' => 'Calculate costs and pricing',
                'scope' => 'item',
                'requires_approval' => false,
            ],
            'quote_preparation' => [
                'name' => 'Pregătirea ofertei',
                'description' => 'Prepare final quotation',
                'scope' => 'comanda',
                'requires_approval' => true,
            ],
        ],
        
        2 => [ // Contractare
            'contract_draft' => [
                'name' => 'Proiect de contract',
                'description' => 'Draft contract documents',
                'scope' => 'comanda',
                'requires_approval' => true,
            ],
            'terms_finalization' => [
                'name' => 'Finalizarea termenilor',
                'description' => 'Finalize contract terms',
                'scope' => 'comanda',
                'requires_approval' => true,
            ],
            'legal_review' => [
                'name' => 'Revizuire juridică',
                'description' => 'Legal review of contract',
                'scope' => 'comanda',
                'requires_approval' => true,
            ],
            'contract_signing' => [
                'name' => 'Semnarea contractului',
                'description' => 'Contract signing process',
                'scope' => 'comanda',
                'requires_approval' => false,
            ],
        ],
        
        3 => [ // Pregatire
            'project_planning' => [
                'name' => 'Planificarea proiectului',
                'description' => 'Plan project execution',
                'scope' => 'comanda',
                'requires_approval' => true,
            ],
            'resource_allocation' => [
                'name' => 'Alocarea resurselor',
                'description' => 'Allocate resources and personnel',
                'scope' => 'both',
                'requires_approval' => false,
            ],
            'material_specifications' => [
                'name' => 'Specificațiile materialelor',
                'description' => 'Define material specifications',
                'scope' => 'item',
                'requires_approval' => true,
            ],
            'timeline_establishment' => [
                'name' => 'Stabilirea cronogramului',
                'description' => 'Establish project timeline',
                'scope' => 'comanda',
                'requires_approval' => true,
            ],
            'technical_drawings' => [
                'name' => 'Desene tehnice',
                'description' => 'Create technical drawings',
                'scope' => 'item',
                'requires_approval' => true,
            ],
            'grafica_pregatire' => [
                'name' => 'Grafică',
                'description' => 'Graphic design for production preparation',
                'scope' => 'item',
                'requires_approval' => true,
            ],
            'dtp' => [
                'name' => 'DTP',
                'description' => 'Desktop publishing and layout preparation',
                'scope' => 'item',
                'requires_approval' => true,
            ],
        ],
        
        4 => [ // Aprovizionare
            'material_ordering' => [
                'name' => 'Comandarea materialelor',
                'description' => 'Order required materials',
                'scope' => 'item',
                'requires_approval' => false,
            ],
            'supplier_coordination' => [
                'name' => 'Coordonarea furnizorilor',
                'description' => 'Coordinate with suppliers',
                'scope' => 'both',
                'requires_approval' => false,
            ],
            'quality_verification' => [
                'name' => 'Verificarea calității',
                'description' => 'Verify material quality',
                'scope' => 'item',
                'requires_approval' => true,
            ],
        ],
        
        5 => [ // Executie
            'processing_machine_tool' => [
                'name' => 'Prelucrare [mașină / sculă]',
                'description' => 'Machine processing and tooling',
                'scope' => 'item',
                'requires_approval' => false,
            ],
            'manufacturing_processes' => [
                'name' => 'Procese de fabricație',
                'description' => 'Manufacturing and production',
                'scope' => 'item',
                'requires_approval' => false,
            ],
            'assembly_operations' => [
                'name' => 'Operațiuni de asamblare',
                'description' => 'Assembly and integration',
                'scope' => 'both',
                'requires_approval' => false,
            ],
            'quality_control' => [
                'name' => 'Controlul calității',
                'description' => 'Quality control checks',
                'scope' => 'item',
                'requires_approval' => true,
            ],
        ],
        
        6 => [ // Livrare
            'final_inspection' => [
                'name' => 'Inspecția finală',
                'description' => 'Final quality inspection',
                'scope' => 'both',
                'requires_approval' => true,
            ],
            'packaging' => [
                'name' => 'Ambalarea',
                'description' => 'Package for delivery',
                'scope' => 'item',
                'requires_approval' => false,
            ],
            'transportation' => [
                'name' => 'Transportul',
                'description' => 'Transport to client',
                'scope' => 'comanda',
                'requires_approval' => false,
            ],
            'installation' => [
                'name' => 'Instalarea (dacă este cazul)',
                'description' => 'Installation at client site',
                'scope' => 'comanda',
                'requires_approval' => false,
            ],
            'post_delivery_receipt' => [
                'name' => 'Documente post-livrare (dacă este cazul)',
                'description' => 'Handle delivery documentation',
                'scope' => 'comanda',
                'requires_approval' => false,
            ],
        ],
        
        7 => [ // Facturare
            'invoice_preparation' => [
                'name' => 'Pregătirea facturii',
                'description' => 'Prepare invoice documents',
                'scope' => 'comanda',
                'requires_approval' => true,
            ],
            'documentation_completion' => [
                'name' => 'Completarea documentației',
                'description' => 'Complete all documentation',
                'scope' => 'comanda',
                'requires_approval' => false,
            ],
            'payment_processing' => [
                'name' => 'Procesarea plății',
                'description' => 'Process payment',
                'scope' => 'comanda',
                'requires_approval' => false,
            ],
            'project_closure' => [
                'name' => 'Închiderea proiectului',
                'description' => 'Close project',
                'scope' => 'comanda',
                'requires_approval' => true,
            ],
            'payment_follow_up' => [
                'name' => 'Urmărirea plății',
                'description' => 'Follow up on payment',
                'scope' => 'comanda',
                'requires_approval' => false,
            ],
        ],
    ];

    /**
     * Get substages for a specific stage
     */
    public static function getSubstagesForStage(int $stage): array
    {
        return self::STAGE_SUBSTAGES[$stage] ?? [];
    }

    /**
     * Get all substages as options for select fields
     */
    public static function getSubstageOptions(int $stage): array
    {
        $substages = self::getSubstagesForStage($stage);
        $options = [];
        
        foreach ($substages as $key => $substage) {
            $options[$key] = $substage['name'];
        }
        
        return $options;
    }

    /**
     * Get substage configuration
     */
    public static function getSubstageConfig(int $stage, string $substageType): ?array
    {
        return self::STAGE_SUBSTAGES[$stage][$substageType] ?? null;
    }

    /**
     * Check if substage requires approval
     */
    public static function requiresApproval(int $stage, string $substageType): bool
    {
        $config = self::getSubstageConfig($stage, $substageType);
        return $config['requires_approval'] ?? false;
    }

    /**
     * Get substage scope
     */
    public static function getSubstageScope(int $stage, string $substageType): string
    {
        $config = self::getSubstageConfig($stage, $substageType);
        return $config['scope'] ?? 'comanda';
    }

    /**
     * Get stage names
     */
    public static function getStageNames(): array
    {
        return [
            1 => 'Ofertare',
            2 => 'Contractare',
            3 => 'Pregatire',
            4 => 'Aprovizionare',
            5 => 'Executie',
            6 => 'Livrare',
            7 => 'Facturare',
        ];
    }

    /**
     * Get stage name
     */
    public static function getStageName(int $stage): string
    {
        $names = self::getStageNames();
        return $names[$stage] ?? 'Unknown';
    }
}
