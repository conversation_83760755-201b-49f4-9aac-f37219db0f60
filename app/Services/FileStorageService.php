<?php

namespace App\Services;

use App\Models\File;
use App\Models\Comanda;
use App\Models\ComandaActivity;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class FileStorageService
{
    protected string $baseDisk;

    public function __construct()
    {
        // Allow configuration of storage disk - can be 'private', 'google', etc.
        $this->baseDisk = config('filesystems.crm_files_disk', 'private');
    }

    public function storeFile(
        UploadedFile $uploadedFile,
        $fileable,
        string $category = File::CATEGORY_GENERAL,
        ?string $description = null
    ): File {
        // Generate storage path based on fileable type
        $storagePath = $this->generateStoragePath($fileable, $category);
        
        // Generate unique filename
        $extension = $uploadedFile->getClientOriginalExtension();
        $storedName = Str::uuid() . '.' . $extension;
        $fullPath = $storagePath . '/' . $storedName;
        
        // Store the file
        $path = Storage::disk($this->baseDisk)->putFileAs(
            $storagePath,
            $uploadedFile,
            $storedName
        );
        
        // Create file record
        return File::create([
            'original_name' => $uploadedFile->getClientOriginalName(),
            'stored_name' => $storedName,
            'path' => $path,
            'disk' => $this->baseDisk,
            'size' => $uploadedFile->getSize(),
            'mime_type' => $uploadedFile->getMimeType(),
            'extension' => $extension,
            'fileable_type' => get_class($fileable),
            'fileable_id' => $fileable->id,
            'category' => $category,
            'uploaded_by' => auth()->id(),
            'description' => $description,
        ]);
    }

    public function deleteFile(File $file): bool
    {
        // Delete physical file
        if (Storage::disk($file->disk)->exists($file->path)) {
            Storage::disk($file->disk)->delete($file->path);
        }
        
        // Delete database record
        return $file->delete();
    }

    public function getFileUrl(File $file): string
    {
        if ($file->is_public) {
            return Storage::disk($file->disk)->url($file->path);
        }
        
        // For private files, return a route that handles authentication
        return route('files.download', $file->uuid);
    }

    protected function generateStoragePath($fileable, string $category): string
    {
        // Base path structure compatible with Google Drive
        $basePrefix = config('filesystems.crm_files_prefix', 'CRM_Files');

        if ($fileable instanceof Comanda) {
            $basePath = "{$basePrefix}/{$fileable->uuid}";

            return match($category) {
                File::CATEGORY_GENERAL => "{$basePath}/general",
                File::CATEGORY_REFERENCE => "{$basePath}/reference",
                default => "{$basePath}/general"
            };
        }

        if ($fileable instanceof ComandaActivity) {
            $comanda = $fileable->comanda;
            $basePath = "{$basePrefix}/{$comanda->uuid}/activities/{$fileable->substage_type}_{$fileable->id}";

            return match($category) {
                File::CATEGORY_ACTIVITY_INPUT => "{$basePath}/input",
                File::CATEGORY_ACTIVITY_OUTPUT => "{$basePath}/output",
                File::CATEGORY_ACTIVITY_WORK => "{$basePath}/work",
                File::CATEGORY_REFERENCE => "{$basePath}/reference",
                default => "{$basePath}/general"
            };
        }

        // Fallback
        return "{$basePrefix}/misc";
    }

    public function getComandaFileStructure(Comanda $comanda): array
    {
        $files = File::forComanda($comanda->id)->with(['fileable', 'uploadedBy'])->get();
        
        $structure = [
            'comanda_files' => [],
            'activity_files' => [],
            'total_size' => 0,
            'total_count' => 0,
        ];
        
        foreach ($files as $file) {
            $structure['total_size'] += $file->size;
            $structure['total_count']++;
            
            if ($file->fileable_type === Comanda::class) {
                $structure['comanda_files'][] = $file;
            } elseif ($file->fileable_type === ComandaActivity::class) {
                $activityId = $file->fileable_id;
                if (!isset($structure['activity_files'][$activityId])) {
                    $structure['activity_files'][$activityId] = [
                        'activity' => $file->fileable,
                        'files' => []
                    ];
                }
                $structure['activity_files'][$activityId]['files'][] = $file;
            }
        }
        
        return $structure;
    }

    public function moveFile(File $file, $newFileable, string $newCategory = null): bool
    {
        $newCategory = $newCategory ?? $file->category;
        $newPath = $this->generateStoragePath($newFileable, $newCategory);
        $newFullPath = $newPath . '/' . $file->stored_name;
        
        // Move physical file
        if (Storage::disk($file->disk)->move($file->path, $newFullPath)) {
            // Update database record
            $file->update([
                'path' => $newFullPath,
                'fileable_type' => get_class($newFileable),
                'fileable_id' => $newFileable->id,
                'category' => $newCategory,
            ]);
            
            return true;
        }
        
        return false;
    }

    public function copyFile(File $file, $newFileable, string $newCategory = null): ?File
    {
        $newCategory = $newCategory ?? $file->category;
        $newPath = $this->generateStoragePath($newFileable, $newCategory);
        $storedName = Str::uuid() . '.' . $file->extension;
        $newFullPath = $newPath . '/' . $storedName;
        
        // Copy physical file
        if (Storage::disk($file->disk)->copy($file->path, $newFullPath)) {
            // Create new database record
            return File::create([
                'original_name' => $file->original_name,
                'stored_name' => $storedName,
                'path' => $newFullPath,
                'disk' => $file->disk,
                'size' => $file->size,
                'mime_type' => $file->mime_type,
                'extension' => $file->extension,
                'fileable_type' => get_class($newFileable),
                'fileable_id' => $newFileable->id,
                'category' => $newCategory,
                'uploaded_by' => auth()->id(),
                'description' => $file->description,
                'metadata' => $file->metadata,
            ]);
        }
        
        return null;
    }

    public function getStorageStats(Comanda $comanda): array
    {
        $files = File::forComanda($comanda->id)->get();
        
        $stats = [
            'total_files' => $files->count(),
            'total_size' => $files->sum('size'),
            'by_category' => [],
            'by_type' => [],
        ];
        
        foreach ($files as $file) {
            // By category
            if (!isset($stats['by_category'][$file->category])) {
                $stats['by_category'][$file->category] = ['count' => 0, 'size' => 0];
            }
            $stats['by_category'][$file->category]['count']++;
            $stats['by_category'][$file->category]['size'] += $file->size;
            
            // By file type
            $type = $file->isImage() ? 'image' : ($file->isPdf() ? 'pdf' : ($file->isDocument() ? 'document' : 'other'));
            if (!isset($stats['by_type'][$type])) {
                $stats['by_type'][$type] = ['count' => 0, 'size' => 0];
            }
            $stats['by_type'][$type]['count']++;
            $stats['by_type'][$type]['size'] += $file->size;
        }
        
        return $stats;
    }
}
