<?php

namespace App\Services;

use App\Models\Comanda;
use App\Models\DocumentTemplate;
use App\Models\GeneratedDocument;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\View;

class DocumentGenerationService
{
    protected string $gotenbergUrl;

    public function __construct()
    {
        $this->gotenbergUrl = config('services.gotenberg.url', 'http://localhost:3000');
    }

    /**
     * Generate a document from a template and comanda
     */
    public function generateDocument(
        Comanda $comanda,
        DocumentTemplate $template,
        array $customFields = [],
        ?string $title = null
    ): GeneratedDocument {
        // Proforma is always in RON, quotation can be EUR or RON
        $currency = 'EUR';
        $exchangeRate = null;
        
        if ($template->type === 'proforma') {
            // Proforma is always in RON
            $currency = 'RON';
            $exchangeRate = \App\Models\SystemSetting::get('eur_to_ron_rate', config('company.eur_to_ron_rate', 5.0));
        } elseif ($template->type === 'quotation') {
            // Quotation can be EUR or RON based on user selection
            $currency = $customFields['currency'] ?? 'EUR';
            if ($currency === 'RON') {
                $exchangeRate = \App\Models\SystemSetting::get('eur_to_ron_rate', config('company.eur_to_ron_rate', 5.0));
            }
        }
        
        // Create the document record
        $document = GeneratedDocument::create([
            'comanda_id' => $comanda->id,
            'template_id' => $template->id,
            'client_id' => $comanda->client_id,
            'type' => $template->type,
            'title' => $title ?? $this->generateDefaultTitle($template, $comanda),
            'generated_data' => $this->prepareGenerationData($comanda, $template),
            'custom_field_values' => $customFields,
            'generated_by' => auth()->id(),
            'currency' => $currency,
            'exchange_rate_used' => $exchangeRate,
        ]);

        // Generate HTML content
        $htmlContent = $this->renderTemplate($document);
        $document->update(['html_content' => $htmlContent]);

        return $document;
    }

    /**
     * Generate PDF from an existing document
     */
    public function generatePdf(GeneratedDocument $document): string
    {
        if (!$document->html_content) {
            $document->html_content = $this->renderTemplate($document);
            $document->save();
        }

        // Generate PDF using Gotenberg (if available) or fallback
        $pdfContent = $this->convertToPdf($document->html_content);

        // Store PDF
        $pdfPath = $this->storePdf($pdfContent, $document);

        // Update document record
        $document->markAsGenerated($pdfPath, strlen($pdfContent));

        return $pdfPath;
    }

    /**
     * Render the Blade template with data
     */
    protected function renderTemplate(GeneratedDocument $document): string
    {
        $template = $document->template;
        $comanda = $document->comanda;
        $client = $document->client;

        $viewData = [
            'document' => $document,
            'template' => $template,
            'comanda' => $comanda,
            'client' => $client,
            'custom_fields' => $document->custom_field_values ?? [],
            'document_title' => $document->title,
        ];

        // For standalone documents, add additional data
        if (!$comanda && $document->generated_data) {
            $viewData['items'] = $document->generated_data['items'] ?? [];
            $viewData['client_data'] = $document->generated_data['client_data'] ?? [];
        }

        // Check if template view exists, otherwise use default
        $templateView = "documents.{$template->blade_template}";
        if (!View::exists($templateView)) {
            $templateView = 'documents.default';
        }

        return view($templateView, $viewData)->render();
    }

    /**
     * Convert HTML to PDF using Gotenberg or fallback
     */
    protected function convertToPdf(string $html): string
    {
        try {
            // Try Gotenberg first
            if ($this->isGotenbergAvailable()) {
                return $this->callGotenberg($html);
            }
        } catch (\Exception $e) {
            \Log::warning('Gotenberg PDF generation failed: ' . $e->getMessage());
        }

        // Fallback: return HTML content (for now)
        // In production, you might want to use another PDF library like DomPDF
        return $html;
    }

    /**
     * Call Gotenberg API for PDF conversion
     */
    protected function callGotenberg(string $html): string
    {
        $timeout = config('services.gotenberg.timeout', 30);
        
        // Create a temporary file for Gotenberg
        $tempFile = tmpfile();
        fwrite($tempFile, $html);
        $tempPath = stream_get_meta_data($tempFile)['uri'];
        
        try {
            $response = Http::timeout($timeout)
                ->attach('files', file_get_contents($tempPath), 'index.html')
                ->post("{$this->gotenbergUrl}/forms/chromium/convert/html", [
                    'marginTop' => '1.5cm',
                    'marginBottom' => '1cm',
                    'marginLeft' => '2.5cm',
                    'marginRight' => '1cm',
                    'format' => 'A4',
                    'printBackground' => true,
                ]);

            if (!$response->successful()) {
                throw new \Exception('Gotenberg conversion failed: ' . $response->body());
            }

            return $response->body();
        } finally {
            fclose($tempFile);
        }
    }

    /**
     * Store PDF file
     */
    protected function storePdf(string $pdfContent, GeneratedDocument $document): string
    {
        $filename = sprintf(
            '%s_%s_%s.pdf',
            $document->type,
            $document->document_number,
            now()->format('Y-m-d_H-i-s')
        );

        $path = "documents/{$document->type}/{$filename}";
        
        Storage::disk('local')->put($path, $pdfContent);

        return $path;
    }

    /**
     * Check if Gotenberg is available
     */
    protected function isGotenbergAvailable(): bool
    {
        try {
            $timeout = config('services.gotenberg.timeout', 30);
            $response = Http::timeout(min($timeout, 5))->get("{$this->gotenbergUrl}/health");
            return $response->successful();
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Prepare data for document generation
     */
    protected function prepareGenerationData(Comanda $comanda, DocumentTemplate $template): array
    {
        return [
            'comanda' => $comanda->toArray(),
            'client' => $comanda->client->toArray(),
            'items' => $comanda->items->toArray(),
            'template_version' => $template->version,
            'generated_at' => now()->toISOString(),
        ];
    }

    /**
     * Generate default title for document
     */
    protected function generateDefaultTitle(DocumentTemplate $template, Comanda $comanda): string
    {
        $type = $template->type;
        $prefix = \App\Models\SystemSetting::get("{$type}_prefix", strtoupper(substr($type, 0, 3)));
        
        // Get the last document number for this type
        $lastDocument = GeneratedDocument::where('type', $type)
            ->whereNotNull('document_number')
            ->orderBy('id', 'desc')
            ->first();
        
        $nextNumber = 1;
        if ($lastDocument && $lastDocument->document_number) {
            // Extract number from document number (e.g., "PRO-2025-004" -> 4)
            if (preg_match('/-(\d+)$/', $lastDocument->document_number, $matches)) {
                $nextNumber = (int)$matches[1] + 1;
            }
        }
        
        $year = now()->year;
        $number = str_pad($nextNumber, 3, '0', STR_PAD_LEFT);
        
        return "{$prefix}-{$year}-{$number}";
    }

    /**
     * Get available templates for a document type
     */
    public function getAvailableTemplates(string $type): \Illuminate\Database\Eloquent\Collection
    {
        return DocumentTemplate::active()
            ->ofType($type)
            ->orderBy('is_default', 'desc')
            ->orderBy('name')
            ->get();
    }

    /**
     * Get default template for a document type
     */
    public function getDefaultTemplate(string $type): ?DocumentTemplate
    {
        return DocumentTemplate::active()
            ->ofType($type)
            ->default()
            ->first();
    }
}
