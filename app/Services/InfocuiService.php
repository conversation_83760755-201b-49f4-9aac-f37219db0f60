<?php

namespace App\Services;

use App\Models\Client;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class InfocuiService
{
    private string $apiKey;
    private string $baseUrl;

    public function __construct()
    {
        $this->apiKey = config('services.infocui.api_key');
        $this->baseUrl = config('services.infocui.base_url', 'https://infocui.ro/system/api');
    }

    /**
     * Fetch company information by CUI (Cod Unic de Înregistrare)
     *
     * @param string $cui The Romanian tax identification number
     * @return array|null Returns mapped client data or null if not found/invalid
     * @throws \Exception If API request fails
     */
    public function getCompanyInfo(string $cui): ?array
    {
        if (empty($cui) || !$this->isValidCui($cui)) {
            return null;
        }

        try {
            $response = Http::timeout(30)
                ->withHeaders([
                    'Accept' => 'application/json',
                ])
                ->get("{$this->baseUrl}/data", [
                    'key' => $this->apiKey,
                    'cui' => $cui,
                ]);

            if (!$response->successful()) {
                Log::error('Infocui API request failed', [
                    'cui' => $cui,
                    'status' => $response->status(),
                    'response' => $response->body(),
                ]);
                return null;
            }

            $responseData = $response->json();

            // API returns data in {status, message, data} format
            if (!isset($responseData['status']) || $responseData['status'] !== 200) {
                Log::error('Infocui API returned error', [
                    'cui' => $cui,
                    'status' => $responseData['status'] ?? 'unknown',
                    'message' => $responseData['message'] ?? 'no message',
                ]);
                return null;
            }

            $data = $responseData['data'] ?? null;

            // Log the full API response for debugging
            Log::info('Infocui API full response', [
                'cui' => $cui,
                'status' => $responseData['status'] ?? null,
                'message' => $responseData['message'] ?? null,
                'data' => $data,
            ]);

            // Check if we got valid data (based on API docs: "nume" is the company name field)
            if (empty($data) || !isset($data['nume'])) {
                return null;
            }

            // Map API response to our Client model structure
            return $this->mapApiResponseToClient($data);

        } catch (\Exception $e) {
            Log::error('Infocui API error', [
                'cui' => $cui,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * Validate CUI format (basic validation)
     *
     * @param string $cui
     * @return bool
     */
    private function isValidCui(string $cui): bool
    {
        // Romanian CUI is typically 8-13 digits
        return preg_match('/^\d{8,13}$/', preg_replace('/[\s-]/', '', $cui));
    }

    /**
     * Map API response fields to our Client model structure
     * Based on actual API documentation from INFOCUI_API_doc
     *
     * @param array $apiData
     * @return array
     */
    private function mapApiResponseToClient(array $apiData): array
    {
        return [
            // Company identification
            'company_name' => $apiData['nume'] ?? null,
            'cui' => isset($apiData['cod_fiscal']) ? (string)$apiData['cod_fiscal'] : null,
            'reg_com' => $apiData['cod_inmatriculare'] ?? null,
            'tva' => $this->parseTvaDate($apiData['tva'] ?? null),
            
            // Address information (concatenated from API components)
            'address' => $this->concatenateAddress($apiData),
            
            // Contact information
            'email' => null, // API doesn't provide email
            'phone' => $apiData['tel'] ?? null,
            
            // Contact person - not available in API
            'contact_person' => null,
            'contact_email' => null,
            'contact_phone' => null,
            
            // Banking details - not available in API
            'bank_name' => null,
            'bank_account' => null,
            
            // Additional metadata (stored in notes)
            'notes' => $this->generateNotes($apiData),
            
            'is_active' => true,
        ];
    }

    /**
     * Concatenate address fields from API response
     * Based on API docs: adresa, loc, str, nr, sect, judet, cp
     *
     * @param array $apiData
     * @return string
     */
    private function concatenateAddress(array $apiData): string
    {
        $parts = [];
        
        // Use full address field if available
        if (isset($apiData['adresa'])) {
            return $apiData['adresa'];
        }
        
        // Otherwise build from components
        if (isset($apiData['str'])) {
            $street = $apiData['str'];
            if (isset($apiData['nr'])) {
                $street .= ', Nr. ' . $apiData['nr'];
            }
            $parts[] = $street;
        }
        
        if (isset($apiData['sect'])) {
            $parts[] = 'Sectorul ' . $apiData['sect'];
        }
        
        if (isset($apiData['loc'])) {
            $parts[] = $apiData['loc'];
        }
        
        if (isset($apiData['judet'])) {
            $parts[] = $apiData['judet'];
        }
        
        if (isset($apiData['cp'])) {
            $parts[] = 'Cod poștal ' . $apiData['cp'];
        }
        
        if (isset($apiData['detalii_adresa'])) {
            $parts[] = $apiData['detalii_adresa'];
        }
        
        return implode(', ', array_filter($parts));
    }

    /**
     * Parse TVA value from API response
     * Can be: date string ("01-02-2002"), boolean ("1"), or "NU" (not registered)
     *
     * @param string|int|null $tvaValue
     * @return string|null
     */
    private function parseTvaDate($tvaValue): ?string
    {
        if (empty($tvaValue) || $tvaValue === 'NU') {
            return null;
        }

        // If it's just "1", it means TVA registered but no date provided
        if ($tvaValue === '1' || $tvaValue === 1) {
            return '1';
        }

        // Try to parse as a date (format: "01-02-2002" or "dd-mm-yyyy")
        try {
            $date = \Carbon\Carbon::createFromFormat('d-m-Y', $tvaValue);
            return $date->format('Y-m-d');
        } catch (\Exception $e) {
            // If parsing fails, just store the raw value
            Log::info('TVA value is not a date, storing as-is', ['tva' => $tvaValue]);
            return (string)$tvaValue;
        }
    }

    /**
     * Generate notes from additional API data
     * Stores relevant metadata that might be useful for proformas
     *
     * @param array $apiData
     * @return string|null
     */
    private function generateNotes(array $apiData): ?string
    {
        $notes = [];
        
        // Company status (stare_firma)
        if (isset($apiData['stare_firma'])) {
            $notes[] = "Stare firmă: " . $apiData['stare_firma'];
        }
        
        // Registration date components
        if (isset($apiData['jud_com']) || isset($apiData['nr_com']) || isset($apiData['an_com'])) {
            $regParts = array_filter([
                $apiData['jud_com'] ?? null,
                $apiData['nr_com'] ?? null,
                $apiData['an_com'] ?? null,
            ]);
            if (!empty($regParts)) {
                $notes[] = "Nr. înmatriculare: " . implode('/', $regParts);
            }
        }
        
        // VAT registration is now stored in the tva field, no need in notes
        
        // Fax
        if (isset($apiData['fax']) && !empty($apiData['fax'])) {
            $notes[] = "Fax: " . $apiData['fax'];
        }
        
        // Company identifier
        if (isset($apiData['euid'])) {
            $notes[] = "EUID: " . $apiData['euid'];
        }
        
        return !empty($notes) ? implode("\n", $notes) : null;
    }

    /**
     * Check if a company exists by CUI
     *
     * @param string $cui
     * @return bool
     */
    public function companyExists(string $cui): bool
    {
        return $this->getCompanyInfo($cui) !== null;
    }

    /**
     * Populate a Client model with data from API
     * Only fills fields that are not already set
     *
     * @param Client $client
     * @param string $cui
     * @return bool True if data was found and populated
     */
    public function populateClientModel(Client $client, string $cui): bool
    {
        $data = $this->getCompanyInfo($cui);
        
        if ($data === null) {
            return false;
        }

        // Only fill empty fields
        foreach ($data as $key => $value) {
            if (in_array($key, $client->getFillable()) && empty($client->$key)) {
                $client->$key = $value;
            }
        }

        return true;
    }

    /**
     * Create a new Client model from CUI
     *
     * @param string $cui
     * @return Client|null
     */
    public function createClientFromCui(string $cui): ?Client
    {
        $data = $this->getCompanyInfo($cui);
        
        if ($data === null) {
            return null;
        }

        return Client::create($data);
    }
}
