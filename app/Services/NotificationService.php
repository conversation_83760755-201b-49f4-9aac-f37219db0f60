<?php

namespace App\Services;

use App\Models\Comanda;
use App\Models\ComandaActivity;
use App\Models\ComandaItem;
use App\Models\User;
use Filament\Actions\Action;
use Filament\Notifications\Notification;

class NotificationService
{
    /**
     * Send a notification to a user (database + broadcast)
     *
     * This sends the notification to BOTH:
     * 1. Database (for persistence and bell icon)
     * 2. Broadcast (for real-time toast notifications via Reverb)
     *
     * According to Filament 4 documentation, you need BOTH for full functionality.
     */
    public static function send(
        User $recipient,
        string $title,
        string $body,
        string $icon = 'heroicon-o-bell',
        string $iconColor = 'info',
        array $actions = [],
        bool $persistent = false
    ): void {
        $notification = Notification::make()
            ->title($title)
            ->body($body)
            ->icon($icon)
            ->iconColor($iconColor)
            ->actions($actions);

        if ($persistent) {
            $notification->persistent();
        }

        // Send to database for persistence (shows in bell icon)
        $notification->sendToDatabase($recipient, isEventDispatched: true);

        // ALSO broadcast for real-time toast notification
        $notification->broadcast($recipient);
    }

    /**
     * Notify when a comanda is assigned to a PM
     */
    public static function comandaAssignedToPM(Comanda $comanda, User $pm): void
    {
        self::send(
            recipient: $pm,
            title: 'Comandă nouă asignată',
            body: "Ți-a fost asignată comanda #{$comanda->id} - {$comanda->client->name}",
            icon: 'heroicon-o-clipboard-document-check',
            iconColor: 'success',
            actions: [
                Action::make('view')
                    ->button()
                    ->url(route('filament.app.resources.comandas.view', $comanda))
                    ->markAsRead(),
                Action::make('dismiss')
                    ->button()
                    ->color('gray')
                    ->close(),
            ],
            persistent: true
        );
    }

    /**
     * Notify when a comanda item is assigned to a specialist
     */
    public static function itemAssignedToSpecialist(ComandaItem $item, User $specialist): void
    {
        self::send(
            recipient: $specialist,
            title: 'Activitate nouă asignată',
            body: "Ți-a fost asignată activitatea: {$item->name} (Comanda #{$item->comanda_id})",
            icon: 'heroicon-o-wrench-screwdriver',
            iconColor: 'warning',
            actions: [
                Action::make('view')
                    ->button()
                    ->url(route('filament.productie.resources.comanda-items.view', $item))
                    ->markAsRead(),
                Action::make('dismiss')
                    ->button()
                    ->color('gray')
                    ->close(),
            ],
            persistent: true
        );
    }

    /**
     * Notify when an activity is approved
     */
    public static function activityApproved(ComandaActivity $activity, User $specialist): void
    {
        self::send(
            recipient: $specialist,
            title: 'Activitate aprobată',
            body: "Activitatea ta pentru {$activity->comandaItem->name} a fost aprobată!",
            icon: 'heroicon-o-check-circle',
            iconColor: 'success',
            actions: [
                Action::make('view')
                    ->button()
                    ->url(route('filament.productie.resources.comanda-items.view', $activity->comandaItem))
                    ->markAsRead(),
            ]
        );
    }

    /**
     * Notify when an activity is rejected
     */
    public static function activityRejected(ComandaActivity $activity, User $specialist, ?string $reason = null): void
    {
        $body = "Activitatea ta pentru {$activity->comandaItem->name} a fost respinsă.";
        if ($reason) {
            $body .= " Motiv: {$reason}";
        }

        self::send(
            recipient: $specialist,
            title: 'Activitate respinsă',
            body: $body,
            icon: 'heroicon-o-x-circle',
            iconColor: 'danger',
            actions: [
                Action::make('view')
                    ->button()
                    ->url(route('filament.productie.resources.comanda-items.view', $activity->comandaItem))
                    ->markAsRead(),
            ],
            persistent: true
        );
    }

    /**
     * Notify when a deadline is approaching
     */
    public static function deadlineApproaching(Comanda $comanda, User $recipient, int $hoursRemaining): void
    {
        self::send(
            recipient: $recipient,
            title: 'Deadline apropiat!',
            body: "Comanda #{$comanda->id} are deadline în {$hoursRemaining} ore!",
            icon: 'heroicon-o-clock',
            iconColor: 'warning',
            actions: [
                Action::make('view')
                    ->button()
                    ->url(route('filament.app.resources.comandas.view', $comanda))
                    ->markAsRead(),
            ],
            persistent: true
        );
    }

    /**
     * Notify when a deadline is overdue
     */
    public static function deadlineOverdue(Comanda $comanda, User $recipient): void
    {
        self::send(
            recipient: $recipient,
            title: 'Deadline depășit!',
            body: "Comanda #{$comanda->id} a depășit deadline-ul!",
            icon: 'heroicon-o-exclamation-triangle',
            iconColor: 'danger',
            actions: [
                Action::make('view')
                    ->button()
                    ->url(route('filament.app.resources.comandas.view', $comanda))
                    ->markAsRead(),
            ],
            persistent: true
        );
    }

    /**
     * Notify when a comanda status changes
     */
    public static function comandaStatusChanged(Comanda $comanda, User $recipient, string $oldStatus, string $newStatus): void
    {
        self::send(
            recipient: $recipient,
            title: 'Status comandă actualizat',
            body: "Comanda #{$comanda->id} a trecut din '{$oldStatus}' în '{$newStatus}'",
            icon: 'heroicon-o-arrow-path',
            iconColor: 'info',
            actions: [
                Action::make('view')
                    ->button()
                    ->url(route('filament.app.resources.comandas.view', $comanda))
                    ->markAsRead(),
            ]
        );
    }

    /**
     * Send a custom notification to multiple users
     */
    public static function sendToMultiple(
        array $recipients,
        string $title,
        string $body,
        string $icon = 'heroicon-o-bell',
        string $iconColor = 'info',
        array $actions = [],
        bool $persistent = false
    ): void {
        foreach ($recipients as $recipient) {
            self::send($recipient, $title, $body, $icon, $iconColor, $actions, $persistent);
        }
    }

    /**
     * Notify all PMs about a new comanda
     */
    public static function notifyPMsAboutNewComanda(Comanda $comanda): void
    {
        $pms = User::where('role', 'pm')->where('is_active', true)->get();

        self::sendToMultiple(
            recipients: $pms->all(),
            title: 'Comandă nouă în sistem',
            body: "A fost adăugată comanda #{$comanda->id} - {$comanda->client->name}",
            icon: 'heroicon-o-document-plus',
            iconColor: 'info',
            actions: [
                Action::make('view')
                    ->button()
                    ->url(route('filament.app.resources.comandas.view', $comanda))
                    ->markAsRead(),
            ]
        );
    }

    /**
     * Notify managers about important events
     */
    public static function notifyManagers(
        string $title,
        string $body,
        string $icon = 'heroicon-o-bell',
        string $iconColor = 'warning',
        array $actions = []
    ): void {
        $managers = User::whereIn('role', ['superadmin', 'manager'])
            ->where('is_active', true)
            ->get();

        self::sendToMultiple(
            recipients: $managers->all(),
            title: $title,
            body: $body,
            icon: $icon,
            iconColor: $iconColor,
            actions: $actions,
            persistent: true
        );
    }

    /**
     * Notify when an activity is assigned
     */
    public static function activityAssigned(ComandaActivity $activity, User $assignee): void
    {
        $comanda = $activity->comanda;
        
        self::send(
            recipient: $assignee,
            title: 'Activitate nouă asignată',
            body: "Ți-a fost asignată activitatea '{$activity->type->name}' pentru comanda #{$comanda->internal_number}",
            icon: 'heroicon-o-clipboard-document-check',
            iconColor: 'info',
            actions: [
                Action::make('view')
                    ->button()
                    ->url(route('filament.app.resources.comandas.view', $comanda))
                    ->markAsRead(),
            ],
            persistent: true
        );
    }

    /**
     * Notify when a file is uploaded to an activity
     */
    public static function fileUploadedToActivity(ComandaActivity $activity, User $assignee, string $fileName): void
    {
        $comanda = $activity->comanda;
        
        self::send(
            recipient: $assignee,
            title: 'Fișier nou încărcat',
            body: "A fost încărcat fișierul '{$fileName}' la activitatea '{$activity->type->name}' (Comanda #{$comanda->internal_number})",
            icon: 'heroicon-o-document-arrow-up',
            iconColor: 'success',
            actions: [
                Action::make('view')
                    ->button()
                    ->url(route('filament.app.resources.comandas.view', $comanda))
                    ->markAsRead(),
            ],
            persistent: true
        );
    }

    /**
     * Notify when a file is uploaded to a comanda
     */
    public static function fileUploadedToComanda(Comanda $comanda, User $recipient, string $fileName): void
    {
        self::send(
            recipient: $recipient,
            title: 'Fișier nou încărcat la comandă',
            body: "A fost încărcat fișierul '{$fileName}' la comanda #{$comanda->internal_number}",
            icon: 'heroicon-o-document-arrow-up',
            iconColor: 'success',
            actions: [
                Action::make('view')
                    ->button()
                    ->url(route('filament.app.resources.comandas.view', $comanda))
                    ->markAsRead(),
            ],
            persistent: true
        );
    }

    /**
     * Notify PM when an activity is completed
     */
    public static function activityCompleted(ComandaActivity $activity, User $pm): void
    {
        $comanda = $activity->comanda;
        
        self::send(
            recipient: $pm,
            title: 'Activitate finalizată',
            body: "Activitatea '{$activity->type->name}' din comanda #{$comanda->internal_number} a fost marcată ca finalizată",
            icon: 'heroicon-o-check-circle',
            iconColor: 'success',
            actions: [
                Action::make('view')
                    ->button()
                    ->url(route('filament.app.resources.comandas.view', $comanda))
                    ->markAsRead(),
            ],
            persistent: true
        );
    }

    /**
     * Notify PM when a new document (Oferta/Proforma) is created
     */
    public static function documentCreated(Comanda $comanda, User $pm, string $documentType, string $documentNumber): void
    {
        self::send(
            recipient: $pm,
            title: "Document nou: {$documentType}",
            body: "{$documentType} #{$documentNumber} a fost generat pentru comanda #{$comanda->internal_number}",
            icon: 'heroicon-o-document-text',
            iconColor: 'info',
            actions: [
                Action::make('view')
                    ->button()
                    ->url(route('filament.app.resources.comandas.view', $comanda))
                    ->markAsRead(),
            ],
            persistent: true
        );
    }

    /**
     * Notify PM when comanda details change
     */
    public static function comandaDetailsChanged(Comanda $comanda, User $pm, string $changeDescription): void
    {
        self::send(
            recipient: $pm,
            title: 'Comandă modificată',
            body: "Comanda #{$comanda->internal_number}: {$changeDescription}",
            icon: 'heroicon-o-pencil-square',
            iconColor: 'warning',
            actions: [
                Action::make('view')
                    ->button()
                    ->url(route('filament.app.resources.comandas.view', $comanda))
                    ->markAsRead(),
            ],
            persistent: true
        );
    }
}

