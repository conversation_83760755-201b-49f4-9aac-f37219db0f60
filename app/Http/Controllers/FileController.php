<?php

namespace App\Http\Controllers;

use App\Models\File;
use App\Services\FileStorageService;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\StreamedResponse;

class FileController extends Controller
{
    use AuthorizesRequests;
    protected FileStorageService $fileStorageService;

    public function __construct(FileStorageService $fileStorageService)
    {
        $this->fileStorageService = $fileStorageService;
    }

    public function download(string $uuid)
    {
        $file = File::where('uuid', $uuid)->firstOrFail();

        // Check if user has access to this file
        $this->authorize('view', $file);

        if (!Storage::disk($file->disk)->exists($file->path)) {
            abort(404, 'File not found on disk');
        }

        return Storage::disk($file->disk)->download($file->path, $file->original_name);
    }

    public function stream(string $uuid)
    {
        $file = File::where('uuid', $uuid)->firstOrFail();

        // Check if user has access to this file
        $this->authorize('view', $file);

        if (!Storage::disk($file->disk)->exists($file->path)) {
            abort(404, 'File not found on disk');
        }

        $stream = Storage::disk($file->disk)->readStream($file->path);

        return new StreamedResponse(function () use ($stream) {
            fpassthru($stream);
            fclose($stream);
        }, 200, [
            'Content-Type' => $file->mime_type,
            'Content-Length' => $file->size,
            'Content-Disposition' => 'inline; filename="' . $file->original_name . '"',
        ]);
    }

    public function delete(string $uuid)
    {
        $file = File::where('uuid', $uuid)->firstOrFail();

        // Check if user has access to delete this file
        $this->authorize('delete', $file);

        $this->fileStorageService->deleteFile($file);

        return response()->json(['message' => 'File deleted successfully']);
    }

    public function upload(Request $request)
    {
        $request->validate([
            'file' => 'required|file|max:10240', // 10MB max
            'fileable_type' => 'required|string',
            'fileable_id' => 'required|integer',
            'category' => 'required|string|in:general,activity_input,activity_output,activity_work,reference',
            'description' => 'nullable|string|max:1000',
        ]);

        $fileableClass = 'App\\Models\\' . $request->fileable_type;
        $fileable = $fileableClass::findOrFail($request->fileable_id);

        // Check if user has access to upload files to this entity
        $this->authorize('update', $fileable);

        $file = $this->fileStorageService->storeFile(
            $request->file('file'),
            $fileable,
            $request->category,
            $request->description
        );

        return response()->json([
            'message' => 'File uploaded successfully',
            'file' => [
                'uuid' => $file->uuid,
                'original_name' => $file->original_name,
                'size' => $file->size,
                'mime_type' => $file->mime_type,
                'category' => $file->category,
                'download_url' => route('files.download', $file->uuid),
            ]
        ]);
    }
}
