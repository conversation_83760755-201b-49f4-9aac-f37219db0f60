<?php

namespace App\Http\Middleware;

use Closure;
use Filament\Facades\Filament;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class FilamentPanelAccess
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = auth()->user();
        
        if (!$user) {
            return redirect()->route('filament.app.auth.login');
        }

        if (!$user->is_active) {
            auth()->logout();
            return redirect()->route('filament.app.auth.login')
                ->withErrors(['email' => 'Your account has been deactivated.']);
        }

        $panel = Filament::getCurrentPanel();
        
        if ($panel && !$user->canAccessPanel($panel)) {
            // Redirect to appropriate panel based on user role
            $redirectPanel = match($user->role) {
                'superadmin' => 'admin',
                'manager', 'pm' => 'app',
                'specialist' => 'productie',
                default => 'app'
            };
            
            // If they're trying to access the correct panel but still can't, show error
            if ($panel->getId() === $redirectPanel) {
                abort(403, 'You do not have permission to access this panel.');
            }
            
            return redirect()->to("/{$redirectPanel}");
        }

        return $next($request);
    }
}
