<?php

namespace App\Tables\Columns;

use Filament\Tables\Columns\ViewColumn;

class UserAvatarColumn extends ViewColumn
{
    protected string $view = 'tables.columns.user-avatar';

    public static function make(?string $name = null): static
    {
        return parent::make($name ?? 'user_avatar');
    }

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->width('48px');
        $this->grow(false);
        $this->alignment('center');
        
        $this->getStateUsing(function ($record, $column) {
            $relationshipName = str_replace('.name', '', $column->getName());
            $user = data_get($record, $relationshipName);
            
            return [
                'name' => $user ? $user->name : 'Unknown User',
                'id'   => $user ? $user->id : null,
            ];
        });
    }
}
