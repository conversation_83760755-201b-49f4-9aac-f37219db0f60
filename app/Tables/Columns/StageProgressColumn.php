<?php

namespace App\Tables\Columns;

use Filament\Tables\Columns\ViewColumn;

class StageProgressColumn extends ViewColumn
{
    protected string $view = 'tables.columns.stage-progress';

    public static function make(?string $name = null): static
    {
        return parent::make($name ?? 'stage_progress');
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->label('Progress');
        $this->width('320px');
        $this->grow(false);
        $this->alignment('center');
    }
}
