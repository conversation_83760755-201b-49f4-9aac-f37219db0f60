<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcastNow;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class FilamentNotification implements ShouldBroadcastNow
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public string $title;
    public string $body;
    public string $status;
    public ?string $icon;
    public ?int $duration;
    public ?int $userId;

    public function __construct(
        string $title,
        string $body,
        string $status = 'info',
        ?string $icon = null,
        ?int $duration = 5000,
        ?int $userId = null
    ) {
        $this->title = $title;
        $this->body = $body;
        $this->status = $status;
        $this->icon = $icon ?? $this->getDefaultIcon($status);
        $this->duration = $duration;
        $this->userId = $userId ?? auth()->id();
    }

    public function broadcastOn(): array
    {
        // If userId is specified, send to private user channel
        // Otherwise, send to public notifications channel
        if ($this->userId) {
            return [
                new PrivateChannel('user.' . $this->userId),
            ];
        }

        return [
            new Channel('notifications'),
        ];
    }

    public function broadcastWith(): array
    {
        return [
            'title' => $this->title,
            'body' => $this->body,
            'status' => $this->status,
            'icon' => $this->icon,
            'duration' => $this->duration,
            'timestamp' => now()->toISOString(),
        ];
    }

    public function broadcastAs(): string
    {
        return 'filament.notification';
    }

    private function getDefaultIcon(string $status): string
    {
        return match($status) {
            'success' => 'heroicon-o-check-circle',
            'warning' => 'heroicon-o-exclamation-triangle',
            'danger', 'error' => 'heroicon-o-x-circle',
            'info' => 'heroicon-o-information-circle',
            default => 'heroicon-o-bell',
        };
    }
}
