<?php

namespace App\Observers;

use App\Models\Comanda;
use App\Models\Deadline;

class ComandaObserver
{
    /**
     * Handle the Comanda "created" event.
     */
    public function created(Comanda $comanda): void
    {
        if ($comanda->deadline) {
            Deadline::create([
                'comanda_id' => $comanda->id,
                'deadline_date' => $comanda->deadline,
            ]);
        }
    }

    /**
     * Handle the Comanda "updated" event.
     */
    public function updated(Comanda $comanda): void
    {
        // If deadline was changed
        if ($comanda->isDirty('deadline')) {
            // Delete existing deadline event
            Deadline::where('comanda_id', $comanda->id)->delete();
            
            // Create new deadline event if deadline is set
            if ($comanda->deadline) {
                Deadline::create([
                    'comanda_id' => $comanda->id,
                    'deadline_date' => $comanda->deadline,
                ]);
            }
        }
    }

    /**
     * Handle the Comanda "deleted" event.
     */
    public function deleted(Comanda $comanda): void
    {
        // Delete associated deadline event
        Deadline::where('comanda_id', $comanda->id)->delete();
    }
}

