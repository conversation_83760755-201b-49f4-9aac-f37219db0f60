<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Client extends Model
{
    use HasFactory;

    protected $fillable = [
        'company_name',
        'name',
        'cui',
        'reg_com',
        'tva',
        'address',
        'email',
        'phone',
        'contact_person',
        'contact_email',
        'contact_phone',
        'bank_name',
        'bank_account',
        'notes',
        'is_active',
    ];

    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
            // tva is stored as string - can be a date or "1" for registered
        ];
    }

    // Accessors
    public function getDisplayNameAttribute(): string
    {
        return $this->company_name ?: $this->name;
    }

    public function getIsCompanyAttribute(): bool
    {
        return !empty($this->company_name);
    }

    public function getIsIndividualAttribute(): bool
    {
        return !empty($this->name) && empty($this->company_name);
    }

    // Relationships
    public function comenzi()
    {
        return $this->hasMany(Comanda::class);
    }

    public function generatedDocuments()
    {
        return $this->hasMany(GeneratedDocument::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeCompanies($query)
    {
        return $query->whereNotNull('company_name');
    }

    public function scopeIndividuals($query)
    {
        return $query->whereNotNull('name')->whereNull('company_name');
    }
}
