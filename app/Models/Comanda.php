<?php

namespace App\Models;

use App\Services\NotificationService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Support\Str;
use App\Models\CalendarEvent;

class Comanda extends Model
{
    use HasFactory;

    protected $table = 'comenzi';

    protected $fillable = [
        'uuid',
        'internal_number',
        'name',
        'description',
        'client_id',
        'owner_id',
        'arrival_time',
        'arrival_channel',
        'contact_person',
        'deadline',
        'stage',
        'blocked_stages',
        'fast_track',
        'priority',
        'total_value',
        'paid_amount',
        'payment_status',
        'is_active',
        'is_finished',
        'attachments', // For file uploads
    ];

    protected function casts(): array
    {
        return [
            'arrival_time' => 'datetime',
            'deadline' => 'datetime',
            'fast_track' => 'boolean',
            'priority' => 'boolean',
            'is_active' => 'boolean',
            'is_finished' => 'boolean',
            'total_value' => 'decimal:2',
            'paid_amount' => 'decimal:2',
            'attachments' => 'array', // For file uploads
            'blocked_stages' => 'array', // Stages with unfinished activities
        ];
    }

    // Boot method for auto-generation
    protected static function booted()
    {
        static::creating(function ($comanda) {
            if (empty($comanda->uuid)) {
                $comanda->uuid = Str::uuid();
            }

            if (empty($comanda->internal_number)) {
                $comanda->internal_number = static::generateInternalNumber();
            }
            
            // Calculate initial payment status
            $comanda->updatePaymentStatus();
        });
        
        static::updating(function ($comanda) {
            // Recalculate payment status if relevant fields changed
            if ($comanda->isDirty(['paid_amount', 'total_value'])) {
                $comanda->updatePaymentStatus();
            }
        });
        
        static::updated(function ($comanda) {
            // Notify PM about changes (if PM is not the one making the change)
            $currentUserId = auth()->id();
            
            if ($comanda->owner_id && $comanda->owner_id !== $currentUserId) {
                $pm = User::find($comanda->owner_id);
                if ($pm) {
                    $changes = [];
                    
                    // Check for client change
                    if ($comanda->isDirty('client_id')) {
                        $oldClient = Client::find($comanda->getOriginal('client_id'));
                        $newClient = $comanda->client;
                        $changes[] = "Clientul a fost schimbat de la '{$oldClient?->company_name}' la '{$newClient?->company_name}'";
                    }
                    
                    // Check for deadline change
                    if ($comanda->isDirty('deadline')) {
                        $oldDeadline = $comanda->getOriginal('deadline') ? \Carbon\Carbon::parse($comanda->getOriginal('deadline'))->format('d.m.Y') : 'nesetat';
                        $newDeadline = $comanda->deadline ? $comanda->deadline->format('d.m.Y') : 'nesetat';
                        $changes[] = "Deadline-ul a fost schimbat de la {$oldDeadline} la {$newDeadline}";
                    }
                    
                    // Send notification if there are relevant changes
                    if (!empty($changes)) {
                        NotificationService::comandaDetailsChanged(
                            $comanda,
                            $pm,
                            implode('; ', $changes)
                        );
                    }
                }
            }
        });

        // Auto-create
        static::created(function ($comanda) {


            // Always create a default item if none provided
            if ($comanda->items()->count() === 0) {
                $comanda->items()->create([
                    'name' => $comanda->name,
                    'description' => $comanda->name,
                    'quantity' => 1,
                    'unit_price' => $comanda->total_value ?: 0,
                    'total_price' => $comanda->total_value ?: 0,
                    'unit' => 'serviciu',
                    'is_default_item' => true,
                    'assigned_to' => $comanda->owner_id, // Inherit owner as assigned user
                ]);
            }
        });
        
        static::saved(function ($comanda) {
            // Recalculate total value from items after comanda is saved
            $comanda->recalculateTotalValue();
        });
        
    }
    
    /**
     * Recalculate total_value from all items
     */
    public function recalculateTotalValue(): void
    {
        $total = $this->items()->sum('total_price');
        
        // Only update if changed to avoid infinite loops
        if ($this->total_value != $total) {
            $this->updateQuietly(['total_value' => $total]);
        }
    }

    // Generate internal number (C001, C002, etc.)
    public static function generateInternalNumber(): string
    {
        $lastComanda = static::orderBy('id', 'desc')->first();
        $nextNumber = $lastComanda ? $lastComanda->id + 1 : 1;
        return 'C' . str_pad($nextNumber, 4, '0', STR_PAD_LEFT);
    }

    // Relationships
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function owner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    public function items(): HasMany
    {
        return $this->hasMany(ComandaItem::class);
    }

    public function defaultItem(): HasOne
    {
        return $this->hasOne(ComandaItem::class)->where('is_default_item', true);
    }

    public function activities(): HasMany
    {
        return $this->hasMany(ComandaActivity::class);
    }

    public function assignedItems(): HasMany
    {
        return $this->hasMany(ComandaItem::class);
    }

    public function generatedDocuments(): HasMany
    {
        return $this->hasMany(GeneratedDocument::class);
    }

    public function proformas(): HasMany
    {
        return $this->generatedDocuments()->where('type', 'proforma');
    }

    public function invoices(): HasMany
    {
        return $this->generatedDocuments()->where('type', 'invoice');
    }

    public function contracts(): HasMany
    {
        return $this->generatedDocuments()->where('type', 'contract');
    }

    public function calendarEvents(): HasMany
    {
        return $this->hasMany(CalendarEvent::class);
    }

    public function deadlineEvent(): HasOne
    {
        return $this->hasOne(Deadline::class);
    }

    public function files(): MorphMany
    {
        return $this->morphMany(File::class, 'fileable');
    }

    public function allFiles(): HasMany
    {
        // Get all files related to this comanda (direct files + activity files)
        return $this->hasMany(File::class, 'fileable_id')
            ->where(function ($query) {
                $query->where('fileable_type', self::class)
                      ->orWhere(function ($subQuery) {
                          $subQuery->where('fileable_type', ComandaActivity::class)
                                   ->whereHas('fileable', function ($activityQuery) {
                                       $activityQuery->where('comanda_id', $this->id);
                                   });
                      });
            });
    }

    // Accessors
    public function getStageNameAttribute(): string
    {
        return match($this->stage) {
            1 => 'Ofertare',
            2 => 'Contractare',
            3 => 'Pregatire',
            4 => 'Aprovizionare',
            5 => 'Executie',
            6 => 'Livrare',
            7 => 'Facturare',
            default => 'Unknown'
        };
    }
    
    /**
     * Get human-readable names of blocked stages
     */
    public function getBlockedStageNamesAttribute(): array
    {
        if (empty($this->blocked_stages)) {
            return [];
        }
        
        return collect($this->blocked_stages)->map(function($stageNumber) {
            return match($stageNumber) {
                1 => 'Ofertare',
                2 => 'Contractare',
                3 => 'Pregatire',
                4 => 'Aprovizionare',
                5 => 'Executie',
                6 => 'Livrare',
                7 => 'Facturare',
                default => 'Unknown'
            };
        })->toArray();
    }
    
    /**
     * Check if comanda has any blockers (unfinished activities in lower stages)
     */
    public function hasBlockersAttribute(): bool
    {
        return !empty($this->blocked_stages);
    }

    public function getArrivalChannelIconAttribute(): string
    {
        return match($this->arrival_channel) {
            'email' => '📧',
            'whatsapp' => '📱',
            'website' => '🌐',
            'phone' => '📞',
            'walk_in' => '🚶',
            default => '❓'
        };
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByStage($query, int $stage)
    {
        return $query->where('stage', $stage);
    }

    public function scopeByOwner($query, int $userId)
    {
        return $query->where('owner_id', $userId);
    }

    public function scopeFastTrack($query)
    {
        return $query->where('fast_track', true);
    }

    public function scopePriority($query)
    {
        return $query->where('priority', true);
    }

    public function scopeOverdue($query)
    {
        return $query->where('deadline', '<', now())->where('stage', '<', 7);
    }
    
    /**
     * Recalculate and update the comanda's stage based on completed activities
     * Also calculate which stages have unfinished activities (blockers)
     * This should be called when the comanda form is saved
     */
    public function updateStageFromActivities(): void
    {
        // Get all activities for this comanda
        $allActivities = $this->activities()->whereNotNull('stage')->get();
        
        // Get completed activities
        $completedActivities = $allActivities->where('is_done', true);
        
        // Calculate current stage from highest completed activity
        if ($completedActivities->isEmpty()) {
            // No completed activities, keep current stage or default to 1
            if (!$this->stage) {
                $this->stage = 1;
            }
        } else {
            // Find the highest stage number among completed activities
            $highestStage = $completedActivities->max('stage');
            
            if ($highestStage && $highestStage !== $this->stage) {
                $this->stage = $highestStage;
                \Log::info("Recalculated Comanda #{$this->id} stage to {$highestStage} based on completed activities");
            }
        }
        
        // Calculate blocked stages (stages with unfinished activities below or at current stage)
        $blockedStages = [];
        
        // Get incomplete activities
        $incompleteActivities = $allActivities->where('is_done', false);
        
        foreach ($incompleteActivities as $activity) {
            $activityStage = $activity->stage;
            
            // Only track stages that are at or below current stage (these are blockers)
            if ($activityStage && $activityStage <= $this->stage && !in_array($activityStage, $blockedStages)) {
                $blockedStages[] = $activityStage;
            }
        }
        
        // Sort blocked stages
        sort($blockedStages);
        
        $this->blocked_stages = $blockedStages;
        $this->save();
        
        if (!empty($blockedStages)) {
            \Log::info("Comanda #{$this->id} has blocked stages: " . implode(', ', $blockedStages));
        }
    }
    
    /**
     * Update payment status based on paid_amount and total_value
     */
    public function updatePaymentStatus(): void
    {
        $totalValue = (float) $this->total_value;
        $paidAmount = (float) $this->paid_amount;
        
        if ($paidAmount <= 0) {
            $this->payment_status = 'unpaid';
        } elseif ($paidAmount >= $totalValue) {
            $this->payment_status = $paidAmount > $totalValue ? 'overpaid' : 'paid';
        } else {
            $this->payment_status = 'partial';
        }
    }
    
    /**
     * Get remaining amount to be paid
     */
    public function getRemainingAmountAttribute(): float
    {
        return max(0, (float) $this->total_value - (float) $this->paid_amount);
    }
    
    /**
     * Get percentage paid
     */
    public function getPaymentPercentageAttribute(): float
    {
        if ($this->total_value <= 0) {
            return 0;
        }
        
        return min(100, ($this->paid_amount / $this->total_value) * 100);
    }
    
    /**
     * Check if fully paid
     */
    public function getIsPaidAttribute(): bool
    {
        return $this->payment_status === 'paid' || $this->payment_status === 'overpaid';
    }
    
    /**
     * Check if has advance payment
     */
    public function getHasAdvanceAttribute(): bool
    {
        return $this->payment_status === 'partial';
    }
}
