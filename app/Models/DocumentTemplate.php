<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class DocumentTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'uuid',
        'name',
        'type',
        'blade_template',
        'css_framework',
        'custom_css',
        'is_active',
        'is_default',
        'requires_approval',
        'required_fields',
        'optional_fields',
        'custom_fields',
        'fiscal_requirements',
        'language',
        'created_by',
        'updated_by',
        'version',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_default' => 'boolean',
        'requires_approval' => 'boolean',
        'required_fields' => 'array',
        'optional_fields' => 'array',
        'custom_fields' => 'array',
        'fiscal_requirements' => 'array',
        'version' => 'integer',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->uuid)) {
                $model->uuid = (string) Str::uuid();
            }
            
            if (empty($model->version)) {
                $model->version = 1;
            }

            if (empty($model->language)) {
                $model->language = 'ro';
            }

            if (empty($model->css_framework)) {
                $model->css_framework = 'page.js';
            }
        });

        // Ensure only one default template per type
        static::saving(function ($model) {
            if ($model->is_default) {
                static::where('type', $model->type)
                    ->where('id', '!=', $model->id)
                    ->update(['is_default' => false]);
            }
        });
    }

    // Document types enum
    public const TYPES = [
        'proforma' => 'Factură Proformă',
        'quotation' => 'Ofertă',
        'invoice' => 'Factură',
        'contract' => 'Contract',
        'guarantee' => 'Certificat de Garanție',
        'proces_verbal' => 'Proces Verbal de Predare/Primire',
        'bill' => 'Bon Fiscal',
        'note' => 'Notă',
    ];

    // Relationships
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function generatedDocuments(): HasMany
    {
        return $this->hasMany(GeneratedDocument::class, 'template_id');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }

    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    // Helper methods
    public function getTypeLabel(): string
    {
        return self::TYPES[$this->type] ?? $this->type;
    }

    public function getBladeTemplatePath(): string
    {
        return "documents.{$this->blade_template}";
    }

    public function incrementVersion(): void
    {
        $this->increment('version');
    }

    public function isRequiredField(string $field): bool
    {
        return in_array($field, $this->required_fields ?? []);
    }

    public function isOptionalField(string $field): bool
    {
        return in_array($field, $this->optional_fields ?? []);
    }

    public function hasCustomField(string $field): bool
    {
        return isset($this->custom_fields[$field]);
    }

    public function getCustomFieldConfig(string $field): ?array
    {
        return $this->custom_fields[$field] ?? null;
    }
}
