<?php

namespace App\Models;

use App\Services\NotificationService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;

class File extends Model
{
    use HasFactory;

    protected $fillable = [
        'uuid',
        'original_name',
        'stored_name',
        'path',
        'disk',
        'size',
        'mime_type',
        'extension',
        'fileable_type',
        'fileable_id',
        'category',
        'uploaded_by',
        'description',
        'metadata',
        'is_public',
    ];

    protected $casts = [
        'metadata' => 'array',
        'is_public' => 'boolean',
        'size' => 'integer',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($file) {
            if (empty($file->uuid)) {
                $file->uuid = Str::uuid();
            }
        });
        
        static::created(function ($file) {
            // Notify on file upload
            $currentUserId = auth()->id();
            
            // If file is attached to a ComandaActivity
            if ($file->fileable_type === ComandaActivity::class) {
                $activity = ComandaActivity::find($file->fileable_id);
                if ($activity) {
                    // Sync file paths to attachments JSON
                    $activity->load('files');
                    $filePaths = $activity->files->pluck('path')->toArray();
                    $activity->withoutEvents(function () use ($activity, $filePaths) {
                        $activity->update(['attachments' => $filePaths]);
                    });
                    
                    // Send notification if assignee is different from uploader
                    if ($activity->assigned_to && $activity->assigned_to !== $currentUserId) {
                        $assignee = User::find($activity->assigned_to);
                        if ($assignee) {
                            NotificationService::fileUploadedToActivity($activity, $assignee, $file->original_name);
                        }
                    }
                }
            }
            
            // If file is attached to a Comanda
            if ($file->fileable_type === Comanda::class) {
                $comanda = Comanda::find($file->fileable_id);
                if ($comanda) {
                    // Notify all activity assignees for this comanda (if they're not the uploader)
                    $assignees = $comanda->activities()
                        ->where('assigned_to', '!=', $currentUserId)
                        ->whereNotNull('assigned_to')
                        ->with('assignee')
                        ->get()
                        ->pluck('assignee')
                        ->unique('id');
                    
                    foreach ($assignees as $assignee) {
                        if ($assignee) {
                            NotificationService::fileUploadedToComanda($comanda, $assignee, $file->original_name);
                        }
                    }
                    
                    // Also notify PM if they're not the uploader
                    if ($comanda->owner_id && $comanda->owner_id !== $currentUserId) {
                        $pm = User::find($comanda->owner_id);
                        if ($pm) {
                            NotificationService::fileUploadedToComanda($comanda, $pm, $file->original_name);
                        }
                    }
                }
            }
        });

        static::deleting(function ($file) {
            // Delete the actual file when model is deleted
            if (Storage::disk($file->disk)->exists($file->path)) {
                Storage::disk($file->disk)->delete($file->path);
            }
        });
    }

    // Relationships
    public function fileable(): MorphTo
    {
        return $this->morphTo();
    }

    public function uploadedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }

    // Helper methods
    public function getUrl(): string
    {
        if ($this->is_public) {
            return Storage::disk($this->disk)->url($this->path);
        }

        // For private files, you might want to create a route that serves them with authentication
        return route('files.download', $this->uuid);
    }

    public function getHumanReadableSize(): string
    {
        return self::formatBytes($this->size);
    }

    public static function formatBytes($bytes, $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }

    public function isImage(): bool
    {
        return str_starts_with($this->mime_type, 'image/');
    }

    public function isPdf(): bool
    {
        return $this->mime_type === 'application/pdf';
    }

    public function isDocument(): bool
    {
        return in_array($this->mime_type, [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        ]);
    }

    // Scopes
    public function scopeForComanda($query, $comandaId)
    {
        return $query->where(function ($q) use ($comandaId) {
            $q->where('fileable_type', Comanda::class)
              ->where('fileable_id', $comandaId)
              ->orWhereHas('fileable', function ($subQ) use ($comandaId) {
                  $subQ->where('comanda_id', $comandaId);
              });
        });
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    // Constants
    public const CATEGORY_GENERAL = 'general';
    public const CATEGORY_ACTIVITY_INPUT = 'activity_input';
    public const CATEGORY_ACTIVITY_OUTPUT = 'activity_output';
    public const CATEGORY_ACTIVITY_WORK = 'activity_work';
    public const CATEGORY_REFERENCE = 'reference';

    public static function getCategories(): array
    {
        return [
            self::CATEGORY_GENERAL => 'General Files',
            self::CATEGORY_ACTIVITY_INPUT => 'Activity Input Files',
            self::CATEGORY_ACTIVITY_OUTPUT => 'Activity Output Files',
            self::CATEGORY_ACTIVITY_WORK => 'Working Files',
            self::CATEGORY_REFERENCE => 'Reference Files',
        ];
    }
}
