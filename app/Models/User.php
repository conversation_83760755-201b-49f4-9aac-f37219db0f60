<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Filament\Models\Contracts\FilamentUser;
use Filament\Panel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Str;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable implements FilamentUser
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasRoles;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
        'is_active',
        'notes',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Get the user's initials
     */
    public function initials(): string
    {
        return Str::of($this->name)
            ->explode(' ')
            ->take(2)
            ->map(fn ($word) => Str::substr($word, 0, 1))
            ->implode('');
    }

    /**
     * The channels the user receives notification broadcasts on.
     * This is required for Filament broadcast notifications to work.
     */
    public function receivesBroadcastNotificationsOn(): string
    {
        return 'App.Models.User.' . $this->id;
    }

    // Role Management Methods
    public function isRole(string $role): bool
    {
        return $this->role === $role;
    }

    public function hasAnyRole(array $roles): bool
    {
        return in_array($this->role, $roles);
    }

    // Permission Checks
    public function canSeePricing(): bool
    {
        return $this->hasAnyRole(['superadmin', 'manager']);
    }

    public function canEditComanda(Comanda $comanda): bool
    {
        return match($this->role) {
            'superadmin', 'manager' => true,
            'pm' => $comanda->owner_id === $this->id,
            'specialist' => false,
            default => false
        };
    }

    public function canDeleteComanda(Comanda $comanda): bool
    {
        return match($this->role) {
            'superadmin' => true,
            'manager' => true,
            'pm' => $comanda->owner_id === $this->id && $comanda->stage <= 3, // Can only delete if not too advanced
            'specialist' => false,
            default => false
        };
    }



    public function canAssignWork(): bool
    {
        return $this->hasAnyRole(['superadmin', 'manager', 'pm']);
    }

    public function canApproveActivities(): bool
    {
        return $this->hasAnyRole(['superadmin', 'manager', 'pm']);
    }

    /**
     * Determine if the user can access the given Filament panel.
     */
    public function canAccessPanel(Panel $panel): bool
    {
        if (!$this->is_active) {
            return false;
        }

        return match($panel->getId()) {
            'admin' => $this->isRole('superadmin'),
            'app' => $this->hasAnyRole(['superadmin', 'manager', 'pm', 'specialist']),
            'definitions' => $this->hasAnyRole(['superadmin', 'manager', 'pm']),
            'productie' => $this->hasAnyRole(['superadmin', 'manager', 'pm', 'specialist']),
            default => false
        };
    }

    // Relationships
    public function ownedComenzi()
    {
        return $this->hasMany(Comanda::class, 'owner_id');
    }

    public function createdDocumentTemplates()
    {
        return $this->hasMany(DocumentTemplate::class, 'created_by');
    }

    public function updatedDocumentTemplates()
    {
        return $this->hasMany(DocumentTemplate::class, 'updated_by');
    }

    public function generatedDocuments()
    {
        return $this->hasMany(GeneratedDocument::class, 'generated_by');
    }

    public function approvedDocuments()
    {
        return $this->hasMany(GeneratedDocument::class, 'approved_by');
    }
}
