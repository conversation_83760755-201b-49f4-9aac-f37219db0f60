<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Guava\Calendar\Contracts\Eventable;
use Guava\Calendar\ValueObjects\CalendarEvent;

class Unavailability extends Model implements Eventable
{
    use SoftDeletes;

    protected $fillable = [
        'user_id',
        'type',
        'start_date',
        'end_date',
        'reason',
        'created_by',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function toCalendarEvent(): CalendarEvent
    {
        // Multi-day, all-day event
        $startDate = $this->start_date->format('Y-m-d');
        $endDate = $this->end_date->copy()->addDay()->format('Y-m-d'); // End is exclusive

        $title = $this->user->name . ' - ' . $this->getTypeLabel();

        // Set color based on type
        $backgroundColor = match($this->type) {
            'personal_time_off' => '#FEF9C3', // Light yellow
            'sick_leave' => '#D9F99D',         // Green-ish pastel yellow
            'unpaid_time_off' => '#FCD34D',    // Amber 300 (pale amber)
            default => '#EAB308',              // Default yellow
        };

        return CalendarEvent::make($this)
            ->title($title)
            ->start($startDate)
            ->end($endDate)
            ->allDay(true)
            ->backgroundColor($backgroundColor)
            ->textColor('#000000'); // Black text for better contrast
    }

    public function getTypeLabel(): string
    {
        return match($this->type) {
            'sick_leave' => 'Sick Leave',
            'personal_time_off' => 'Personal Time Off',
            'unpaid_time_off' => 'Unpaid Time Off',
            default => ucfirst(str_replace('_', ' ', $this->type)),
        };
    }
}

