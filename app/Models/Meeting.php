<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Guava\Calendar\Contracts\Eventable;
use Guava\Calendar\ValueObjects\CalendarEvent;
use Carbon\Carbon;

class Meeting extends Model implements Eventable
{
    use SoftDeletes;

    protected $fillable = [
        'title',
        'date',
        'start_time',
        'end_time',
        'description',
        'created_by',
    ];

    protected $casts = [
        'date' => 'date',
    ];

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function toCalendarEvent(): CalendarEvent
    {
        // Combine date and time for the calendar
        // start_time and end_time are stored as TIME columns (HH:MM:SS format)
        $startDateTime = Carbon::parse($this->date->format('Y-m-d') . ' ' . $this->start_time);

        // If end_time is provided, use it; otherwise default to 1 hour after start
        if ($this->end_time) {
            $endDateTime = Carbon::parse($this->date->format('Y-m-d') . ' ' . $this->end_time);
        } else {
            $endDateTime = $startDateTime->copy()->addHour();
        }

        return CalendarEvent::make($this)
            ->title($this->title)
            ->start($startDateTime)
            ->end($endDateTime)
            ->backgroundColor('#3B82F6') // Blue (meetings)
            ->textColor('#FFFFFF');
    }
}
