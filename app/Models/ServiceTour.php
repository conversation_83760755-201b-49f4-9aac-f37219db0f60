<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Guava\Calendar\Contracts\Eventable;
use Guava\Calendar\ValueObjects\CalendarEvent;

class ServiceTour extends Model implements Eventable
{
    use SoftDeletes;

    protected $fillable = [
        'user_id',
        'title',
        'start_date',
        'end_date',
        'description',
        'created_by',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
    ];

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'service_tour_user')
            ->withTimestamps();
    }

    public function toCalendarEvent(): CalendarEvent
    {
        // Multi-day, all-day event
        // For all-day events, vkurko/calendar expects date-only strings (YYYY-MM-DD)
        // NOT datetime strings with timezone info
        $startDate = $this->start_date->format('Y-m-d');
        $endDate = $this->end_date->copy()->addDay()->format('Y-m-d'); // End is exclusive

        $event = CalendarEvent::make($this)
            ->title($this->title)
            ->start($startDate)
            ->end($endDate)
            ->allDay(true)
            ->backgroundColor('#ab6ee4ff') // Purple (service tours)
            ->textColor('#FFFFFF');

        // Debug: log what we're creating
        \Log::info('ServiceTour Event Created', [
            'title' => $this->title,
            'start_input' => $startDate,
            'end_input' => $endDate,
            'event_start' => $event->getStart()->toIso8601String(),
            'event_end' => $event->getEnd()->toIso8601String(),
            'allDay' => $event->getAllDay(),
        ]);

        return $event;
    }
}
