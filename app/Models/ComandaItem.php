<?php

namespace App\Models;

use App\Services\NotificationService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ComandaItem extends Model
{
    protected static function booted()
    {
        parent::boot();
        
        static::created(function ($item) {
            // Notify PM when an item is added (if PM is not the creator)
            $currentUserId = auth()->id();
            $comanda = $item->comanda;
            
            if ($comanda && $comanda->owner_id && $comanda->owner_id !== $currentUserId) {
                // Don't notify for default items
                if (!$item->is_default_item) {
                    $pm = User::find($comanda->owner_id);
                    if ($pm) {
                        NotificationService::comandaDetailsChanged(
                            $comanda,
                            $pm,
                            "A fost adăugat un item nou: '{$item->name}' (Cantitate: {$item->quantity}, Preț: €{$item->unit_price})"
                        );
                    }
                }
            }
        });
        
        static::updated(function ($item) {
            // Notify PM when an item is edited (if PM is not the editor)
            $currentUserId = auth()->id();
            $comanda = $item->comanda;
            
            if ($comanda && $comanda->owner_id && $comanda->owner_id !== $currentUserId) {
                // Don't notify for default items unless significant changes
                if (!$item->is_default_item || $item->isDirty(['name', 'quantity', 'unit_price'])) {
                    $changes = [];
                    
                    if ($item->isDirty('name')) {
                        $changes[] = "nume: '{$item->getOriginal('name')}' → '{$item->name}'";
                    }
                    if ($item->isDirty('quantity')) {
                        $changes[] = "cantitate: {$item->getOriginal('quantity')} → {$item->quantity}";
                    }
                    if ($item->isDirty('unit_price')) {
                        $changes[] = "preț: €{$item->getOriginal('unit_price')} → €{$item->unit_price}";
                    }
                    
                    if (!empty($changes)) {
                        $pm = User::find($comanda->owner_id);
                        if ($pm) {
                            NotificationService::comandaDetailsChanged(
                                $comanda,
                                $pm,
                                "Item-ul '{$item->name}' a fost modificat: " . implode(', ', $changes)
                            );
                        }
                    }
                }
            }
        });
        
        static::saved(function ($item) {
            // Recalculate comanda total when item is saved
            if ($item->comanda) {
                $item->comanda->recalculateTotalValue();
            }
        });
        
        static::deleted(function ($item) {
            // Recalculate comanda total when item is deleted
            if ($item->comanda) {
                $item->comanda->recalculateTotalValue();
            }
        });
    }
    protected $fillable = [
        'comanda_id',
        'name',
        'description',
        'quantity',
        'unit',
        'unit_price',
        'total_price',
        'status',
        'completion_percentage',
        'assigned_to',
        'metadata',
        'sort_order',
        'is_default_item',
    ];

    protected $casts = [
        'metadata' => 'array',
        'unit_price' => 'decimal:2',
        'total_price' => 'decimal:2',
        'completion_percentage' => 'integer',
        'quantity' => 'integer',
        'sort_order' => 'integer',
        'is_default_item' => 'boolean',
    ];

    // Relationships
    public function comanda(): BelongsTo
    {
        return $this->belongsTo(Comanda::class);
    }

    public function assignedTo(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    public function activities(): HasMany
    {
        return $this->hasMany(ComandaActivity::class);
    }

    // Accessors & Mutators
    public function getDisplayNameAttribute(): string
    {
        return $this->name . ($this->quantity > 1 ? " (x{$this->quantity})" : '');
    }

    // Scopes
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeInProgress($query)
    {
        return $query->where('status', 'in_progress');
    }
}
