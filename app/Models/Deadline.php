<?php

namespace App\Models;

use Guava\Calendar\Contracts\Eventable;
use Guava\Calendar\ValueObjects\CalendarEvent;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class Deadline extends Model implements Eventable
{
    use HasFactory;

    protected $fillable = [
        'comanda_id',
        'deadline_date',
    ];

    protected $casts = [
        'deadline_date' => 'date',
    ];

    public function comanda(): BelongsTo
    {
        return $this->belongsTo(Comanda::class);
    }

    public function toCalendarEvent(): CalendarEvent
    {
        $title = 'Deadline: ' . $this->comanda->name;
        
        return CalendarEvent::make($this)
            ->title($title)
            ->start($this->deadline_date->format('Y-m-d'))
            ->end($this->deadline_date->copy()->addDay()->format('Y-m-d'))
            ->allDay(true)
            ->backgroundColor('#DC2626') // Red (deadlines)
            ->textColor('#FFFFFF');
    }
}

