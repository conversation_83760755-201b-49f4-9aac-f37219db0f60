<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ActivityType extends Model
{
    protected $fillable = [
        'name',
        'key',
        'stage',
        'description',
        'sort_order',
        'is_active',
    ];

    protected $casts = [
        'stage' => 'integer',
        'sort_order' => 'integer',
        'is_active' => 'boolean',
    ];

    // Relationships
    public function activities(): HasMany
    {
        return $this->hasMany(ComandaActivity::class, 'type', 'key');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeForStage($query, int $stage)
    {
        return $query->where('stage', $stage);
    }

    // Get stage name
    public function getStageNameAttribute(): string
    {
        return match($this->stage) {
            1 => 'Ofertare',
            2 => 'Contractare',
            3 => 'Pregatire',
            4 => 'Aprovizionare',
            5 => 'Executie',
            6 => 'Livrare',
            7 => 'Facturare',
            default => 'Unknown'
        };
    }

    // Static method to get types for a stage (for backward compatibility)
    public static function getTypesForStage(int $stage): array
    {
        return static::active()
            ->forStage($stage)
            ->orderBy('sort_order')
            ->pluck('name', 'key')
            ->toArray();
    }
}
