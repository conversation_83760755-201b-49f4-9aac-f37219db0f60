<?php

namespace App\Models;

use App\Services\NotificationService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Support\Str;

class ComandaActivity extends Model
{
    protected static function booted()
    {
        static::creating(function ($activity) {
            if (empty($activity->uuid)) {
                $activity->uuid = Str::uuid();
            }

            if (empty($activity->assigned_at) && $activity->assigned_to) {
                $activity->assigned_at = now();
            }

            // Set assigned_by to current user if not set
            if (empty($activity->assigned_by)) {
                $activity->assigned_by = auth()->id();
            }

            // Auto-populate stage from activity type
            if (empty($activity->stage) && $activity->type) {
                $activityType = ActivityType::where('key', $activity->type)->first();
                if ($activityType) {
                    $activity->stage = $activityType->stage;
                }
            }
        });
        
        static::created(function ($activity) {
            // Notify assignee when activity is created with assignment (if different from owner)
            if ($activity->assigned_to) {
                $comanda = $activity->comanda;
                if ($comanda && $comanda->owner_id !== $activity->assigned_to) {
                    $assignee = User::find($activity->assigned_to);
                    if ($assignee) {
                        NotificationService::activityAssigned($activity, $assignee);
                    }
                }
            }
        });
        
        static::updating(function ($activity) {
            // Update stage if type changed
            if ($activity->isDirty('type') && $activity->type) {
                $activityType = ActivityType::where('key', $activity->type)->first();
                if ($activityType) {
                    $activity->stage = $activityType->stage;
                }
            }
        });
        
        static::updated(function ($activity) {
            // If activity was just marked as done, update parent comanda stage
            if ($activity->isDirty('is_done') && $activity->is_done) {
                $activity->updateParentComandaStage();
                
                // Notify PM when activity is completed (if PM != assignee)
                $comanda = $activity->comanda;
                if ($comanda && $activity->assigned_to && $comanda->owner_id !== $activity->assigned_to) {
                    $pm = User::find($comanda->owner_id);
                    if ($pm) {
                        NotificationService::activityCompleted($activity, $pm);
                    }
                }
            }
            
            // If assignee changed, notify new assignee (if different from owner)
            if ($activity->isDirty('assigned_to') && $activity->assigned_to) {
                $comanda = $activity->comanda;
                if ($comanda && $comanda->owner_id !== $activity->assigned_to) {
                    $assignee = User::find($activity->assigned_to);
                    if ($assignee) {
                        NotificationService::activityAssigned($activity, $assignee);
                    }
                }
            }
        });
        
        static::saved(function ($activity) {
            // Also update parent stage on save (covers both create and update)
            if ($activity->is_done) {
                $activity->updateParentComandaStage();
            }
        });
    }
    
    protected $fillable = [
        'comanda_id',
        'uuid',
        'type',
        'description',
        'deadline',
        'attachments',
        'assigned_to',
        'assigned_by',
        'assigned_at',
        'completed_at',
        'is_done',
        'stage',
    ];

    protected $casts = [
        'assigned_at' => 'datetime',
        'completed_at' => 'datetime',
        'deadline' => 'datetime',
        'is_done' => 'boolean',
        'attachments' => 'array',
        'stage' => 'integer',
    ];

    // Relationships
    public function comanda(): BelongsTo
    {
        return $this->belongsTo(Comanda::class);
    }

    public function assignee(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    public function assigner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_by');
    }

    public function files(): MorphMany
    {
        return $this->morphMany(File::class, 'fileable');
    }
    
    public function activityType(): BelongsTo
    {
        return $this->belongsTo(ActivityType::class, 'type', 'key');
    }

    // Scopes
    public function scopeAssignedTo($query, $userId)
    {
        return $query->where('assigned_to', $userId);
    }
    
    public function scopePending($query)
    {
        return $query->where('is_done', false);
    }
    
    public function scopeCompleted($query)
    {
        return $query->where('is_done', true);
    }

    // Helper Methods
    public function isDone(): bool
    {
        return $this->is_done;
    }

    public function markAsDone(): void
    {
        $this->update([
            'is_done' => true,
            'completed_at' => now(),
        ]);
    }
    
    public function markAsIncomplete(): void
    {
        $this->update([
            'is_done' => false,
            'completed_at' => null,
        ]);
    }
    
    // Get available activity types for a stage
    public static function getTypesForStage(int $stage): array
    {
        return ActivityType::getTypesForStage($stage);
    }
    
    // Get activity type name
    public function getTypeNameAttribute(): ?string
    {
        return ActivityType::where('key', $this->type)->value('name');
    }
    
    // Get stage from activity type
    public function getStageAttribute(): ?int
    {
        return ActivityType::where('key', $this->type)->value('stage');
    }
    
    // Scope to filter by stage
    public function scopeForStage($query, int $stage)
    {
        return $query->where('stage', $stage);
    }
    
    /**
     * Update parent comanda stage and blocked stages based on activities
     * This method calculates the comanda's current stage by finding the highest-numbered
     * stage among all completed activities, and identifies which stages have unfinished work.
     */
    public function updateParentComandaStage(): void
    {
        if (!$this->comanda_id) {
            return;
        }
        
        // Load the parent comanda and trigger full recalculation
        $comanda = Comanda::find($this->comanda_id);
        
        if ($comanda) {
            $comanda->updateStageFromActivities();
        }
    }
    
    /**
     * Sync File model paths with attachments JSON column
     * This ensures that files uploaded via the widget appear in the FileUpload component
     */
    protected static function boot()
    {
        parent::boot();
        
        static::saved(function ($activity) {
            \Log::info('ComandaActivity boot() called', [
                'activity_id' => $activity->id,
                'has_files_loaded' => $activity->relationLoaded('files'),
                'files_count' => $activity->relationLoaded('files') ? $activity->files->count() : 'N/A',
            ]);
            
            // Always reload files from database to ensure we have the latest
            $activity->load('files');
            
            $filePaths = $activity->files->pluck('path')->toArray();
            
            \Log::info('ComandaActivity file paths', [
                'activity_id' => $activity->id,
                'file_paths' => $filePaths,
                'file_count' => count($filePaths),
            ]);
            
            // Get current attachments from database
            $currentAttachments = is_array($activity->attributes['attachments']) 
                ? $activity->attributes['attachments'] 
                : (is_string($activity->attributes['attachments']) 
                    ? json_decode($activity->attributes['attachments'], true) 
                    : []);
            
            \Log::info('ComandaActivity current attachments', [
                'activity_id' => $activity->id,
                'current_attachments' => $currentAttachments,
                'attachment_count' => count($currentAttachments),
            ]);
            
            // Always sync if there's any difference
            if (array_values($filePaths) !== array_values($currentAttachments)) {
                \Log::info('ComandaActivity syncing attachments', [
                    'activity_id' => $activity->id,
                    'old_attachments' => $currentAttachments,
                    'new_attachments' => $filePaths,
                ]);
                
                // Update without triggering observers
                $activity->withoutEvents(function () use ($activity, $filePaths) {
                    $activity->update(['attachments' => $filePaths]);
                });
            } else {
                \Log::info('ComandaActivity attachments already in sync', [
                    'activity_id' => $activity->id,
                ]);
            }
        });
    }
}
