<?php

namespace App\Models;

use App\Services\NotificationService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class GeneratedDocument extends Model
{
    use HasFactory;

    protected $fillable = [
        'uuid',
        'document_number',
        'comanda_id',
        'template_id',
        'client_id',
        'type',
        'title',
        'description',
        'generated_data',
        'custom_field_values',
        'html_content',
        'pdf_path',
        'pdf_size',
        'pdf_generated_at',
        'status',
        'generated_by',
        'approved_by',
        'approved_at',
        'fiscal_data',
        'legal_requirements_met',
        'currency',
        'exchange_rate_used',
        'is_advance_payment',
        'advance_payment_percentage',
        'source_quotation_id',
    ];

    protected $casts = [
        'generated_data' => 'array',
        'custom_field_values' => 'array',
        'pdf_size' => 'integer',
        'pdf_generated_at' => 'datetime',
        'approved_at' => 'datetime',
        'fiscal_data' => 'array',
        'legal_requirements_met' => 'boolean',
        'is_advance_payment' => 'boolean',
        'advance_payment_percentage' => 'decimal:2',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->uuid)) {
                $model->uuid = (string) Str::uuid();
            }

            if (empty($model->document_number)) {
                $model->document_number = $model->generateDocumentNumber();
            }

            if (empty($model->status)) {
                $model->status = 'draft';
            }

            if (empty($model->legal_requirements_met)) {
                $model->legal_requirements_met = false;
            }
        });
        
        static::created(function ($document) {
            // Notify PM when a new document is created (if PM is not the creator)
            if ($document->comanda_id && in_array($document->type, ['quotation', 'proforma'])) {
                $comanda = Comanda::find($document->comanda_id);
                $currentUserId = auth()->id();
                
                if ($comanda && $comanda->owner_id && $comanda->owner_id !== $currentUserId) {
                    $pm = User::find($comanda->owner_id);
                    if ($pm) {
                        $documentType = $document->type === 'quotation' ? 'Ofertă' : 'Proformă';
                        NotificationService::documentCreated(
                            $comanda,
                            $pm,
                            $documentType,
                            $document->document_number
                        );
                    }
                }
            }
        });
    }

    // Document statuses
    public const STATUSES = [
        'draft' => 'Ciornă',
        'generated' => 'Generat',
        'sent' => 'Trimis',
        'signed' => 'Semnat',
        'archived' => 'Arhivat',
    ];

    // Document types (same as DocumentTemplate)
    public const TYPES = [
        'proforma' => 'Factură Proformă',
        'quotation' => 'Ofertă',
        'invoice' => 'Factură',
        'contract' => 'Contract',
        'guarantee' => 'Certificat de Garanție',
        'proces_verbal' => 'Proces Verbal de Predare/Primire',
        'bill' => 'Bon Fiscal',
        'note' => 'Notă',
    ];

    // Relationships
    public function comanda(): BelongsTo
    {
        return $this->belongsTo(Comanda::class);
    }

    public function template(): BelongsTo
    {
        return $this->belongsTo(DocumentTemplate::class);
    }

    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function generatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'generated_by');
    }

    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function sourceQuotation(): BelongsTo
    {
        return $this->belongsTo(GeneratedDocument::class, 'source_quotation_id');
    }

    // Scopes
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }

    public function scopeWithStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    public function scopeGenerated($query)
    {
        return $query->whereNotNull('pdf_generated_at');
    }

    public function scopeApproved($query)
    {
        return $query->whereNotNull('approved_at');
    }

    // Helper methods
    public function getTypeLabel(): string
    {
        return self::TYPES[$this->type] ?? $this->type;
    }

    public function getStatusLabel(): string
    {
        return self::STATUSES[$this->status] ?? $this->status;
    }

    public function generateDocumentNumber(): string
    {
        $prefix = match($this->type) {
            'proforma' => 'CRMPRO',
            'quotation' => 'CRMQUO',
            'invoice' => 'CRMFAC',
            'contract' => 'CRMCON',
            'guarantee' => 'CRMGAR',
            'proces_verbal' => 'CRMPV',
            'bill' => 'CRMBIL',
            'note' => 'CRMNOT',
            default => 'CRMDOC',
        };

        $year = now()->year;
        
        // Use a loop to find the next available number (handles gaps from failed transactions)
        $maxAttempts = 100;
        $sequenceNumber = 1;
        
        // Get the last document number to start from
        $lastNumber = static::where('type', $this->type)
            ->whereYear('created_at', $year)
            ->orderBy('document_number', 'desc')
            ->value('document_number');

        if ($lastNumber) {
            // Extract number from format like "CRMQUO-20250001"
            $parts = explode('-', $lastNumber);
            $lastYearNumber = end($parts); // e.g., "20250001"
            $sequenceNumber = intval(substr($lastYearNumber, -4)) + 1; // Extract last 4 digits
        }
        
        // Try to find an available number (in case of gaps)
        for ($i = 0; $i < $maxAttempts; $i++) {
            $candidateNumber = sprintf('%s-%d%04d', $prefix, $year, $sequenceNumber);
            
            // Check if this number already exists
            $exists = static::where('document_number', $candidateNumber)->exists();
            
            if (!$exists) {
                return $candidateNumber;
            }
            
            $sequenceNumber++;
        }
        
        // Fallback: if we couldn't find a number (shouldn't happen), return with timestamp
        return sprintf('%s-%d%04d-%s', $prefix, $year, $sequenceNumber, now()->format('His'));
    }

    public function isPdfGenerated(): bool
    {
        return !is_null($this->pdf_generated_at) && !empty($this->pdf_path);
    }

    public function isApproved(): bool
    {
        return !is_null($this->approved_at) && !is_null($this->approved_by);
    }

    public function canBeApproved(): bool
    {
        return $this->status === 'generated' && !$this->isApproved();
    }

    public function approve(User $user): void
    {
        $this->update([
            'approved_by' => $user->id,
            'approved_at' => now(),
        ]);
    }

    public function markAsGenerated(string $pdfPath, int $pdfSize): void
    {
        $this->update([
            'pdf_path' => $pdfPath,
            'pdf_size' => $pdfSize,
            'pdf_generated_at' => now(),
            'status' => 'generated',
        ]);
    }

    public function getDownloadUrl(): ?string
    {
        if (!$this->isPdfGenerated()) {
            return null;
        }

        return route('documents.download', ['uuid' => $this->uuid]);
    }

    public function getPreviewUrl(): ?string
    {
        return route('documents.preview', ['uuid' => $this->uuid]);
    }
}
