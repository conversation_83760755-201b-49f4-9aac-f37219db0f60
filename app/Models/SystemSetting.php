<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SystemSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'key',
        'value',
        'type',
        'description',
    ];

    protected $casts = [
        'value' => 'string',
    ];

    /**
     * Get a setting value by key
     */
    public static function get(string $key, $default = null)
    {
        $setting = static::where('key', $key)->first();
        
        if (!$setting) {
            return $default;
        }

        return match($setting->type) {
            'boolean' => (bool) $setting->value,
            'number' => (float) $setting->value,
            'json' => json_decode($setting->value, true),
            default => $setting->value,
        };
    }

    /**
     * Set a setting value by key
     */
    public static function set(string $key, $value, string $type = 'string', ?string $description = null): void
    {
        $processedValue = match($type) {
            'boolean' => $value ? '1' : '0',
            'number' => (string) $value,
            'json' => json_encode($value),
            default => (string) $value,
        };

        static::updateOrCreate(
            ['key' => $key],
            [
                'value' => $processedValue,
                'type' => $type,
                'description' => $description,
            ]
        );
    }

    /**
     * Get document series configuration
     */
    public static function getDocumentSeries(string $type = 'proforma'): string
    {
        $defaultSeries = match($type) {
            'proforma' => 'CRMPRF',
            'quotation' => 'CRMQUO',
            'invoice' => 'CRMFAC',
            'contract' => 'CRMCON',
            default => 'CRMDOC',
        };
        
        return static::get("document_series_{$type}", $defaultSeries);
    }

    /**
     * Set document series configuration
     */
    public static function setDocumentSeries(string $type, string $series): void
    {
        static::set(
            "document_series_{$type}",
            $series,
            'string',
            "Document series prefix for {$type} documents"
        );
    }
}