<?php

namespace App\Providers;

use App\Models\Comanda;
use App\Models\GeneratedDocument;
use App\Observers\ComandaObserver;
use App\Policies\GeneratedDocumentPolicy;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Comanda::observe(ComandaObserver::class);
        
        // Register policies
        Gate::policy(GeneratedDocument::class, GeneratedDocumentPolicy::class);
    }
}
