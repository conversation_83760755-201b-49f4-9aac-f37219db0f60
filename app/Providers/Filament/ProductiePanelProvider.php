<?php

namespace App\Providers\Filament;

use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\AuthenticateSession;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Pages\Dashboard;
use App\Filament\Productie\Pages\SpecialistDashboard;
use App\Filament\Productie\Widgets\MyActivitiesWidget;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Filament\Widgets\AccountWidget;
use Filament\Widgets\FilamentInfoWidget;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use Illuminate\Support\Facades\Route;

class ProductiePanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->id('productie')
            ->path('productie')
            ->colors([
                'primary' => Color::Orange,
            ])
            ->brandName('Caramel Productie')
            ->brandLogo(asset('images/logo.png'))
            ->darkModeBrandLogo(asset('images/logo_dark.png'))
            ->brandLogoHeight('2rem')
            ->favicon(asset('favicon.ico'))
            ->viteTheme('resources/css/filament/productie/theme.css')
            ->discoverResources(in: app_path('Filament/Productie/Resources'), for: 'App\Filament\Productie\Resources')
            ->discoverPages(in: app_path('Filament/Productie/Pages'), for: 'App\Filament\Productie\Pages')
            ->pages([
                SpecialistDashboard::class,
            ])
            ->discoverWidgets(in: app_path('Filament/Productie/Widgets'), for: 'App\Filament\Productie\Widgets')
            ->widgets([
                AccountWidget::class,
                FilamentInfoWidget::class,
            ])
            ->databaseNotifications()
            ->databaseNotificationsPolling('30s')
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([
                Authenticate::class,
                'filament.panel.access',
            ])
            ->sidebarCollapsibleOnDesktop()
            ->sidebarWidth('14rem')
            ->collapsedSidebarWidth('4rem')
            ->maxContentWidth('full')
            ->routes(function () {
                Route::get('/files/{file}/download', function ($fileId) {
                    $widget = new MyActivitiesWidget();
                    return $widget->downloadFile($fileId);
                })->name('files.download');
                
                Route::get('/activities/{activity}/download', function ($activityId) {
                    $widget = new MyActivitiesWidget();
                    $filePath = request()->query('file');
                    return $widget->downloadAttachmentFile($activityId, $filePath);
                })->name('attachments.download');
            });
    }
}
