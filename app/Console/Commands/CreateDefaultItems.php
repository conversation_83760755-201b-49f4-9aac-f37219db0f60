<?php

namespace App\Console\Commands;

use App\Models\Comanda;
use Illuminate\Console\Command;

class CreateDefaultItems extends Command
{
    protected $signature = 'comanda:create-default-items';
    protected $description = 'Create default items for comandas that don\'t have any items';

    public function handle()
    {
        $this->info('Checking comandas for missing default items...');

        // Handle comandas without any items
        $comandasWithoutItems = Comanda::whereDoesntHave('items')->get();
        $this->info("Found {$comandasWithoutItems->count()} comandas without items.");

        foreach ($comandasWithoutItems as $comanda) {
            $comanda->items()->create([
                'name' => $comanda->name,
                'description' => $comanda->name,
                'quantity' => 1,
                'unit_price' => $comanda->total_value ?: 0,
                'total_price' => $comanda->total_value ?: 0,
                'unit' => 'serviciu',
                'is_default_item' => true,
            ]);

            $this->line("Created default item for comanda: {$comanda->internal_number}");
        }

        // Handle comandas with items but no default item
        $comandasWithoutDefaultItems = Comanda::whereHas('items')
            ->whereDoesntHave('items', function($query) {
                $query->where('is_default_item', true);
            })->get();

        $this->info("Found {$comandasWithoutDefaultItems->count()} comandas with items but no default item.");

        foreach ($comandasWithoutDefaultItems as $comanda) {
            // Mark the first item as default
            $firstItem = $comanda->items()->first();
            if ($firstItem) {
                $firstItem->update(['is_default_item' => true]);
                $this->line("Marked first item as default for comanda: {$comanda->internal_number}");
            }
        }

        $this->info('Default items creation completed!');

        return 0;
    }
}
