function d({getContextMenuActionsUsing:o}){return{open:!1,size:{width:0,height:0},position:{x:0,y:0},mountData:{},context:null,actions:[],isLoading:!1,onCloseCallback:null,menu:{"x-show"(){return this.open},"x-bind:style"(){return`
                    position: absolute;
                    z-index: 40;
                    top: ${this.position.y}px;
                    left: ${this.position.x}px;
                `},"x-on:click.away"(){this.closeMenu()}},init:async function(){let t=this.$el.querySelector('[x-bind="menu"]');this.size={width:t.offsetWidth,height:t.offsetHeight},this.$el.addEventListener("calendar--open-menu",e=>this.openMenu(e))},loadActions:async function(t,e={}){this.isLoading=!0,this.actions=[],o(t,e).then(n=>{this.actions=n}).finally(()=>this.isLoading=!1)},openMenu:async function(t,e=null){this.$nextTick(()=>{let n=t.clientX,i=t.clientY,s=t.pageX,c=t.pageY,a=n+this.size.width>window.innerWidth?n+this.size.width-window.innerWidth:0,h=i+this.size.height>window.innerHeight?i+this.size.height-window.innerHeight:0;if(this.position.x=s-a,this.position.y=c-h,this.open=!0,e){let l=e.getAttribute("data-event-id");document.querySelectorAll(`.ec-event[data-event-id="${l}"]`).forEach(u=>u.classList.add("gu-context-menu-open"))}})},closeMenu:function(){this.open=!1,document.querySelectorAll(".ec-event.gu-context-menu-open").forEach(t=>t.classList.remove("gu-context-menu-open")),this.onCloseCallback&&this.onCloseCallback()}}}export{d as default};
