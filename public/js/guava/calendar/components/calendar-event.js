function o({event:t,timeText:r,view:d,hasContextMenu:n}){return{event:t,contextMenu:null,init:function(){n&&this.initializeContextMenu(),this.$el.setAttribute("data-event-id",t.id),this.$el.addEventListener("mouseenter",()=>{document.querySelectorAll(`.ec-event[data-event-id="${t.id}"]`).forEach(e=>{e.classList.add("gu-hover")})}),this.$el.addEventListener("mouseleave",()=>{document.querySelectorAll(`.ec-event[data-event-id="${t.id}"]`).forEach(e=>{e.classList.remove("gu-hover")})})},initializeContextMenu:function(){let e=document.querySelector("[calendar-context-menu]");this.contextMenu=Alpine.$data(e)},onClick:function(e){if(e.event.extendedProps.url){window.open(this.event.extendedProps.url,this.event.extendedProps.url_target??"_blank");return}let i={event:e.event,view:e.view,tzOffset:-new Date().getTimezoneOffset()};if(n){this.contextMenu.loadActions("eventClick",i),this.contextMenu.openMenu(e.jsEvent,this.$el);return}this.$wire.onEventClickJs(i)}}}export{o as default};
