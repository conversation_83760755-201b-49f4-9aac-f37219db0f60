function M({view:c="dayGridMonth",locale:v="en",firstDay:f=1,dayMaxEvents:w=!1,eventContent:l=null,eventClickEnabled:m=!1,eventDragEnabled:i=!1,eventResizeEnabled:d=!1,noEventsClickEnabled:h=!1,dateClickEnabled:D=!1,dateSelectEnabled:o=!1,datesSetEnabled:E=!1,viewDidMountEnabled:S=!1,eventAllUpdatedEnabled:O=!1,hasDateClickContextMenu:g=null,hasDateSelectContextMenu:p=null,hasEventClickContextMenu:z=null,hasNoEventsClickContextMenu:x=null,resources:C=null,resourceLabelContent:r=null,theme:u=null,options:$={},eventAssetUrl:y}){return{init:function(){let n=this.mountCalendar();window.addEventListener("calendar--refresh",()=>{n.refetchEvents()}),this.$wire.on("calendar--set",e=>{n.setOption(e.key,e.value)})},mountCalendar:function(){return EventCalendar.create(this.$el.querySelector("[data-calendar]"),this.getSettings())},getSettings:function(){let n={view:c,locale:v,firstDay:f,dayMaxEvents:w,eventSources:[{events:e=>this.$wire.getEventsJs({...e,tzOffset:-new Date().getTimezoneOffset()})}],resources:C,selectable:o,eventStartEditable:i,eventDurationEditable:d,dayCellFormat:e=>e.getDate().toString()};return l!==null&&(n.eventContent=e=>{let t=e.event.extendedProps.model,s=l[t]??l._default;if(s!==void 0)return{html:s}}),r!==null&&(n.resourceLabelContent=e=>{let t=e.resource.extendedProps.model,s=r[t]??r._default;if(s!==void 0)return{html:this.wrapContent(s,e)}}),D&&(n.dateClick=e=>{let t={date:e.date,dateStr:e.dateStr,allDay:e.allDay,view:e.view,resource:e.resource,tzOffset:-new Date().getTimezoneOffset()};g?this.openContextMenu(e.jsEvent,t,"dateClick"):this.$wire.onDateClickJs(t)}),o&&(n.select=e=>{let t={start:e.start,startStr:e.startStr,end:e.end,endStr:e.endStr,allDay:e.allDay,view:e.view,resource:e.resource,tzOffset:-new Date().getTimezoneOffset()};p?this.openContextMenu(e.jsEvent,t,"dateSelect"):this.$wire.onDateSelectJs(t)}),E&&(n.datesSet=e=>{this.$wire.onDatesSetJs({start:e.start,startStr:e.startStr,end:e.end,endStr:e.endStr,view:e.view,tzOffset:-new Date().getTimezoneOffset()})}),m&&(n.eventClick=e=>{Alpine.$data(e.el).onClick(e)}),n.eventResize=async e=>{let t=e.event.durationEditable,s=d;t!==void 0&&(s=t),s&&await this.$wire.onEventResizeJs({event:e.event,oldEvent:e.oldEvent,endDelta:e.endDelta,view:e.view,tzOffset:-new Date().getTimezoneOffset()}).then(a=>{a===!1&&e.revert()})},n.eventDrop=async e=>{let t=e.event.startEditable,s=i;t!==void 0&&(s=t),s&&await this.$wire.onEventDropJs({event:e.event,oldEvent:e.oldEvent,oldResource:e.oldResource,newResource:e.newResource,delta:e.delta,view:e.view,tzOffset:-new Date().getTimezoneOffset()}).then(a=>{a===!1&&e.revert()})},n.eventDidMount=e=>{e.el.setAttribute("x-load"),e.el.setAttribute("x-load-src",y),e.el.setAttribute("x-data",`calendarEvent({
                    event: ${JSON.stringify(e.event)},
                    timeText: "${e.timeText}",
                    view: ${JSON.stringify(e.view)},
                    hasContextMenu: ${z},
                })`)},h&&(n.noEventsClick=e=>{let t={view:e.view,tzOffset:-new Date().getTimezoneOffset()};x?this.openContextMenu(e.jsEvent,t,"noEventsClick"):this.$wire.onNoEventsClickJs(t)}),S&&(n.viewDidMount=e=>{this.$wire.onViewDidMountJs({view:e.view,tzOffset:-new Date().getTimezoneOffset()})}),O&&(n.eventAllUpdated=e=>{this.$wire.onEventAllUpdatedJs({view:e.view,tzOffset:-new Date().getTimezoneOffset()})}),u&&(n.theme=function(e){return{...e,...u}}),{...n,...$}},wrapContent:function(n,e){let t=document.createElement("div");return t.innerHTML=n,t.setAttribute("x-data",JSON.stringify(e)),t.classList.add("w-full"),t.outerHTML},openContextMenu:function(n,e,t){let s=document.querySelector("[calendar-context-menu]"),a=Alpine.$data(s);a.loadActions(t,e),a.openMenu(n)}}}export{M as default};
