# Caramel CRM

A streamlined and configurable Customer Relationship Management system designed specifically for the Romanian market, starting with niche business sectors.

## 🎯 Vision

Caramel CRM aims to provide Romanian businesses with a modern, intuitive, and culturally-adapted CRM solution that understands local business practices, regulations, and market needs.

## 🚀 Project Status

**Current Phase**: Phase 7 - Real-time Notifications & System Integration ✅

### Recently Completed
- ✅ **Real-time Notification System**: WebSocket-based instant notifications with Laravel Reverb
- ✅ **Calendar System**: Full calendar integration with event management and automatic deadline tracking
- ✅ **Document Generation**: PDF generation system with customizable templates
- ✅ **File Management**: Complete file upload/download system with activity attachments
- ✅ **7-Stage Workflow**: Complete order lifecycle management with wizard interface

### Infrastructure
- ✅ Laravel 12 application framework
- ✅ Filament 4 admin panels (4 specialized panels)
- ✅ Docker containerization with PHP 8.4
- ✅ Database setup (MariaDB)
- ✅ Redis caching and session management
- ✅ Laravel Reverb for WebSocket notifications
- ✅ Queue system for background processing
- ✅ Supervisor process management

## 🎯 Target Market

**Primary Focus**: Romanian niche businesses requiring:
- Customer relationship management
- Sales pipeline tracking
- Romanian-specific compliance features
- Local language support
- Integration with Romanian business tools

## 🛠️ Technology Stack

- **Backend**: Laravel 12 (PHP 8.4)
- **Admin UI**: Filament 4 (4 specialized panels)
- **Database**: MariaDB 11.7
- **Cache/Sessions**: Redis 7
- **Real-time**: Laravel Reverb (WebSockets)
- **Queue System**: Redis-based queues
- **Containerization**: Docker with Supervisor
- **Frontend**: Livewire 3 + Alpine.js
- **Calendar**: Guava Calendar for Filament
- **PDF Generation**: Gotenberg integration

## ✅ Implemented Features

### Core CRM Features
- ✅ **Client Management**: Full CRUD with Romanian commercial data (CUI, Reg.Com)
- ✅ **Order Management (Comenzi)**: 7-stage workflow system with internal numbering
- ✅ **Activity Logging**: Comprehensive activity tracking per order and item
- ✅ **Calendar System**: Event management with automatic deadline tracking
- ⏳ **Document Generation**: PDF generation with customizable templates (Proforma, Invoice, Contract)
- ✅ **File Management**: Upload/download system with activity attachments
- ✅ **Real-time Notifications**: WebSocket-based instant notifications with toast popups

### Romanian Market Specific
- ✅ **Romanian Commercial Data**: CUI, Reg.Com, bank details
- ⏳ **Client Types**: Company vs Individual with conditional fields
- ⏳ **Romanian Document Templates**: Proforma, Invoice, Contract templates
- ✅ **Local Currency**: RON pricing and calculations
- ⏳ **ANAF Integration**: Planned for future phase
- ⏳ **Romanian Language Interface**: Partially implemented

### Technical Features
- ✅ **Role-based Access Control**: 4 user roles (superadmin, manager, pm, specialist)
- ✅ **Multi-Panel Architecture**: 4 specialized Filament panels
- ✅ **Real-time Updates**: Laravel Reverb WebSocket integration
- ✅ **Mobile-responsive Design**: Filament 4 responsive UI
- ⏳ **Data Export**: Built-in table export capabilities
- ✅ **File Storage**: Private file storage with Google Drive integration ready
- ⏳ **API for Integrations**: Planned for future phase
- ⏳ **Multi-tenant Architecture**: Planned for future phase

## 🎯 Current Capabilities

### 4 Specialized Panels
1. **App Panel** (`/app`) - Main CRM interface (Blue theme)
   - Order management with 7-stage workflow
   - Client overview and quick actions
   - Calendar with event management
   - Document generation and management
   - Real-time notifications

2. **Definitions Panel** (`/definitions`) - Client & Data Management (Green theme)
   - Client CRUD operations
   - Romanian commercial data management
   - Client type handling (Company/Individual)

3. **Productie Panel** (`/productie`) - PM Dashboard (Orange theme)
   - Specialist task overview
   - Activity management
   - Work assignment tracking

4. **Admin Panel** (`/admin`) - System Administration (Red theme)
   - User management
   - Document template management
   - System configuration
   - Echo/Reverb diagnostic tools (superadmin only)

### Workflow System
- **7 Stages**: Ofertare → Proiectare → Producție → Montaj → Recepție → Garanție → Facturare
- **Activity Management**: Flexible activity tracking per stage
- **Item Management**: Line items with pricing, status, and specialist assignment
- **File Attachments**: Per-activity file uploads
- **Status Tracking**: Done, Closed, Needs Rework, Requires Approval

### Notification System
- **Real-time Toast Notifications**: Instant WebSocket-based notifications
- **Database Persistence**: Notification history in bell icon
- **Notification Types**: Success, Warning, Danger, Info
- **Persistent Notifications**: Won't auto-dismiss for important alerts
- **Notification Actions**: View, Dismiss, Mark as Read
- **Pre-built Notifications**: Comanda assignments, activity approvals, deadline alerts

## 🚧 Planned Features

### Next Phase: Business Logic & Automation
- [ ] Automatic workflow progression rules
- [ ] Email notifications for offline users
- [ ] Advanced reporting and analytics
- [ ] Dashboard widgets with statistics
- [ ] Issue/ticket system integration
- [ ] Bulk operations and batch processing

### Future Enhancements
- [ ] ANAF integration for tax compliance
- [ ] Multi-tenant architecture
- [ ] Public API for integrations
- [ ] Mobile app (iOS/Android)
- [ ] Advanced analytics and BI tools
- [ ] Automated backup and restore

## 🏗️ Development Setup

### Prerequisites
- Docker and Docker Compose
- Git

### Quick Start
```bash
# Clone the repository
git clone <repository-url>
cd caramel

# Copy environment configuration
cp .misc/.env.docker.example .env

# Build and start the application
.misc/build.sh build-and-test
docker-compose up -d

# Initialize the database
docker-compose exec crmlxii php artisan migrate
docker-compose exec crmlxii php artisan db:seed
```

### Development Workflow
- **Main branch**: Production-ready code
- **Dev branch**: Active development
- **Feature branches**: Individual features (feature/feature-name)

## 📁 Project Structure

```
caramel/
├── .misc/                  # Docker and deployment configurations
│   ├── Dockerfile         # Application container
│   ├── supervisorcrml12.conf  # Process management
│   ├── php.ini           # PHP configuration
│   └── build.sh          # Build automation
├── .reqs/                 # Project requirements and specifications
│   ├── IMPLEMENTATION_STATUS.md  # Detailed implementation status
│   └── *.md              # Feature specifications
├── app/
│   ├── Filament/         # Filament panels and resources
│   │   ├── App/         # Main CRM panel
│   │   ├── Admin/       # Admin panel
│   │   ├── Definitions/ # Client management panel
│   │   └── Productie/   # PM dashboard panel
│   ├── Models/          # Eloquent models
│   ├── Services/        # Business logic services
│   └── Observers/       # Model observers
├── database/
│   ├── migrations/      # Database migrations
│   └── seeders/         # Database seeders
├── resources/
│   ├── views/           # Blade templates
│   └── css/             # Panel-specific themes
├── docs/                # Documentation
│   ├── NOTIFICATIONS_FINAL_SETUP.md
│   └── *.md            # Feature documentation
├── public/              # Web-accessible files
└── docker-compose.yml   # Development stack
```

## 🔗 Live Demo

- **Live URL**: https://crm.concept-42.com
- **App Panel**: https://crm.concept-42.com/app
- **Definitions Panel**: https://crm.concept-42.com/definitions
- **Productie Panel**: https://crm.concept-42.com/productie
- **Admin Panel**: https://crm.concept-42.com/admin

### Test Accounts
- **Superadmin**: <EMAIL> / password
- **Project Manager**: <EMAIL> / password
- **Specialist**: <EMAIL> / password

## 🤝 Contributing

This is currently a private project in early development. Contribution guidelines will be established as the project matures.

## 📄 License

Private project - All rights reserved.

## 📞 Contact

For inquiries about this project, please contact the development team.

---

**Note**: This project is in **active development**. The system is currently in **Phase 7** with core CRM functionality, real-time notifications, calendar system, and document generation fully operational. See [IMPLEMENTATION_STATUS.md](.reqs/IMPLEMENTATION_STATUS.md) for detailed progress tracking.
