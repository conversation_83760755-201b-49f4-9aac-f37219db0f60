<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;

class AdditionalUsersSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Creating additional users for each role tier...');

        // Manager tier users (2 additional users)
        $managers = [
            [
                'name' => '<PERSON>',
                'email' => 'alexandra.<PERSON><PERSON><PERSON>@caramel.ro',
                'role' => 'manager',
            ],
            [
                'name' => '<PERSON><PERSON>',
                'email' => 'mihai.ion<PERSON>@caramel.ro',
                'role' => 'manager',
            ],
        ];

        foreach ($managers as $managerData) {
            User::firstOrCreate(
                ['email' => $managerData['email']],
                array_merge($managerData, [
                    'password' => bcrypt('password'),
                    'is_active' => true,
                    'email_verified_at' => now(),
                ])
            );
        }

        // Project Manager tier users (2 additional users)
        $pms = [
            [
                'name' => '<PERSON>',
                'email' => 'cristina.marine<PERSON><PERSON>@caramel.ro',
                'role' => 'pm',
            ],
            [
                'name' => '<PERSON>',
                'email' => 'andrei.geor<PERSON><PERSON>@caramel.ro',
                'role' => 'pm',
            ],
        ];

        foreach ($pms as $pmData) {
            User::firstOrCreate(
                ['email' => $pmData['email']],
                array_merge($pmData, [
                    'password' => bcrypt('password'),
                    'is_active' => true,
                    'email_verified_at' => now(),
                ])
            );
        }

        // Specialist tier users (3 additional users)
        $specialists = [
            [
                'name' => 'Elena Radu',
                'email' => '<EMAIL>',
                'role' => 'specialist',
            ],
            [
                'name' => 'Bogdan Stoica',
                'email' => '<EMAIL>',
                'role' => 'specialist',
            ],
            [
                'name' => 'Diana Popa',
                'email' => '<EMAIL>',
                'role' => 'specialist',
            ],
        ];

        foreach ($specialists as $specialistData) {
            User::firstOrCreate(
                ['email' => $specialistData['email']],
                array_merge($specialistData, [
                    'password' => bcrypt('password'),
                    'is_active' => true,
                    'email_verified_at' => now(),
                ])
            );
        }

        $this->command->info('Additional users created successfully!');
        $this->command->info('');
        $this->command->info('Manager tier users:');
        $this->command->info('- <EMAIL> (manager) - password: password');
        $this->command->info('- <EMAIL> (manager) - password: password');
        $this->command->info('');
        $this->command->info('Project Manager tier users:');
        $this->command->info('- <EMAIL> (pm) - password: password');
        $this->command->info('- <EMAIL> (pm) - password: password');
        $this->command->info('');
        $this->command->info('Specialist tier users:');
        $this->command->info('- <EMAIL> (specialist) - password: password');
        $this->command->info('- <EMAIL> (specialist) - password: password');
        $this->command->info('- <EMAIL> (specialist) - password: password');
        $this->command->info('');
        $this->command->info('All users have the same password: password');
    }
}
