<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;

class RolesAndPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions
        $permissions = [
            // Comanda permissions
            'view comandas',
            'view any comanda',
            'create comandas',
            'edit comandas',
            'delete comandas',
            
            // Client permissions
            'view clients',
            'create clients',
            'edit clients',
            'delete clients',
            
            // Document permissions
            'view documents',
            'create documents',
            'edit documents',
            'delete documents',
            'download documents',
            
            // Activity permissions
            'view activities',
            'create activities',
            'edit activities',
            'delete activities',
            'complete activities',
            
            // User management
            'view users',
            'create users',
            'edit users',
            'delete users',
            
            // Settings
            'view settings',
            'edit settings',
            
            // Reports
            'view reports',
            'export reports',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Create roles and assign permissions

        // SuperAdmin - has all permissions
        $superadmin = Role::firstOrCreate(['name' => 'superadmin']);
        $superadmin->givePermissionTo(Permission::all());

        // Admin (Manager) - can do most things except user management
        $admin = Role::firstOrCreate(['name' => 'admin']);
        $admin->givePermissionTo([
            'view comandas',
            'view any comanda',
            'create comandas',
            'edit comandas',
            'delete comandas',
            'view clients',
            'create clients',
            'edit clients',
            'delete clients',
            'view documents',
            'create documents',
            'edit documents',
            'delete documents',
            'download documents',
            'view activities',
            'create activities',
            'edit activities',
            'delete activities',
            'complete activities',
            'view reports',
            'export reports',
            'view settings',
        ]);

        // Project Manager (PM) - can manage their own comandas
        $pm = Role::firstOrCreate(['name' => 'pm']);
        $pm->givePermissionTo([
            'view comandas',
            'create comandas',
            'edit comandas',
            'view clients',
            'create clients',
            'edit clients',
            'view documents',
            'create documents',
            'download documents',
            'view activities',
            'create activities',
            'edit activities',
            'complete activities',
        ]);

        // Specialist - can only view and complete assigned activities
        $specialist = Role::firstOrCreate(['name' => 'specialist']);
        $specialist->givePermissionTo([
            'view activities',
            'complete activities',
        ]);

        // Migrate existing users from role column to Spatie roles
        $this->migrateExistingUsers();
    }

    /**
     * Migrate users from the old 'role' column to Spatie roles
     */
    private function migrateExistingUsers(): void
    {
        $users = User::all();

        foreach ($users as $user) {
            // Skip if user already has roles
            if ($user->roles->isNotEmpty()) {
                continue;
            }

            // Assign role based on the 'role' column
            if ($user->role) {
                try {
                    $user->assignRole($user->role);
                    \Log::info("Migrated user {$user->id} ({$user->name}) to role: {$user->role}");
                } catch (\Exception $e) {
                    \Log::warning("Failed to migrate user {$user->id}: " . $e->getMessage());
                }
            }
        }
    }
}
