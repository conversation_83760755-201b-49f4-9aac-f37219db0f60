<?php

namespace Database\Seeders;

use App\Models\Client;
use App\Models\Comanda;
use App\Models\ComandaActivity;
use App\Models\ComandaItem;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class TestDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create test users if they don't exist
        $superadmin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Super Admin',
                'role' => 'superadmin',
                'password' => bcrypt('password'),
            ]
        );

        $pm = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Project Manager',
                'role' => 'pm',
                'password' => bcrypt('password'),
            ]
        );

        $specialist = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Specialist',
                'role' => 'specialist',
                'password' => bcrypt('password'),
            ]
        );

        // Create test clients
        $client1 = Client::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'company_name' => 'Test Company SRL',
                'cui' => 'RO12345678',
                'reg_com' => 'J40/1234/2024',
                'address' => 'Str. Test 123, Bucuresti',
                'phone' => '+40721234567',
                'contact_person' => 'Ion Popescu',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+40721234568',
                'is_active' => true,
            ]
        );

        $client2 = Client::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Maria Ionescu',
                'address' => 'Str. Individual 456, Cluj',
                'phone' => '+40731234567',
                'is_active' => true,
            ]
        );

        // Create test comenzi
        $comanda1 = Comanda::firstOrCreate(
            ['internal_number' => 'C001'],
            [
                'name' => 'Website Development Project',
                'description' => 'Complete website development with CMS integration',
                'client_id' => $client1->id,
                'owner_id' => $pm->id,
                'arrival_time' => now()->subDays(5),
                'arrival_channel' => 'email',
                'contact_person' => 'Ion Popescu',
                'deadline' => now()->addDays(30),
                'stage' => 3,
                'fast_track' => false,
                'priority' => true,
                'total_value' => 15000.00,
                'is_active' => true,
            ]
        );

        $comanda2 = Comanda::firstOrCreate(
            ['internal_number' => 'C002'],
            [
                'name' => 'Logo Design',
                'description' => 'Brand identity and logo design',
                'client_id' => $client2->id,
                'owner_id' => $pm->id,
                'arrival_time' => now()->subDays(2),
                'arrival_channel' => 'whatsapp',
                'contact_person' => 'Maria Ionescu',
                'deadline' => now()->addDays(10),
                'stage' => 2,
                'fast_track' => true,
                'priority' => false,
                'total_value' => 2500.00,
                'is_active' => true,
            ]
        );

        // Create test items for comanda1
        $item1 = ComandaItem::firstOrCreate(
            ['comanda_id' => $comanda1->id, 'name' => 'Frontend Development'],
            [
                'description' => 'HTML, CSS, JavaScript development',
                'quantity' => 1,
                'unit' => 'project',
                'unit_price' => 8000.00,
                'total_price' => 8000.00,
                'status' => 'in_progress',
                'completion_percentage' => 60,
                'assigned_to' => $specialist->id,
                'sort_order' => 1,
            ]
        );

        $item2 = ComandaItem::firstOrCreate(
            ['comanda_id' => $comanda1->id, 'name' => 'Backend Development'],
            [
                'description' => 'PHP Laravel backend with database',
                'quantity' => 1,
                'unit' => 'project',
                'unit_price' => 7000.00,
                'total_price' => 7000.00,
                'status' => 'pending',
                'completion_percentage' => 0,
                'assigned_to' => $specialist->id,
                'sort_order' => 2,
            ]
        );

        // Create test activities
        ComandaActivity::firstOrCreate(
            [
                'comanda_id' => $comanda1->id,
                'type' => 'status_change',
                'title' => 'Project started',
            ],
            [
                'description' => 'Project moved to stage 3 - In Progress',
                'user_id' => $pm->id,
                'activity_time' => now()->subDays(3),
                'old_values' => ['stage' => 2],
                'new_values' => ['stage' => 3],
                'is_internal' => false,
            ]
        );

        ComandaActivity::firstOrCreate(
            [
                'comanda_id' => $comanda1->id,
                'comanda_item_id' => $item1->id,
                'type' => 'comment',
                'title' => 'Progress update',
            ],
            [
                'description' => 'Frontend development is 60% complete. Working on responsive design.',
                'user_id' => $specialist->id,
                'activity_time' => now()->subDays(1),
                'is_internal' => false,
            ]
        );

        $this->command->info('Test data created successfully!');
        $this->command->info('Users created:');
        $this->command->info('- <EMAIL> (superadmin) - password: password');
        $this->command->info('- <EMAIL> (pm) - password: password');
        $this->command->info('- <EMAIL> (specialist) - password: password');
    }
}
