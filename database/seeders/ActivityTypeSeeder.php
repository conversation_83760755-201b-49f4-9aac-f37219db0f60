<?php

namespace Database\Seeders;

use App\Models\ActivityType;
use Illuminate\Database\Seeder;

class ActivityTypeSeeder extends Seeder
{
    public function run(): void
    {
        $activityTypes = [
            // Stage 1 - Ofertare
            ['name' => 'Calcul Ofertă', 'key' => 'calcul_oferta', 'stage' => 1, 'sort_order' => 1],
            ['name' => 'Grafică Prezentare', 'key' => 'grafica_prezentare', 'stage' => 1, 'sort_order' => 2],
            ['name' => 'Măsurători', 'key' => 'masuratori', 'stage' => 1, 'sort_order' => 3],
            ['name' => 'Întâlnire Client', 'key' => 'intalnire_client', 'stage' => 1, 'sort_order' => 4],
            ['name' => 'Vizită La Fața Locului', 'key' => 'vizita_fata_locului', 'stage' => 1, 'sort_order' => 5],
            ['name' => 'Revizui<PERSON>ert<PERSON>', 'key' => 'revizuire_oferta', 'stage' => 1, 'sort_order' => 6],
            
            // Stage 2 - Contractare
            ['name' => 'Transmitere Contract', 'key' => 'transmitere_contract', 'stage' => 2, 'sort_order' => 1],
            ['name' => 'Transmitere Proformă', 'key' => 'transmitere_proforma', 'stage' => 2, 'sort_order' => 2],
            ['name' => 'Semnare Contract', 'key' => 'semnare_contract', 'stage' => 2, 'sort_order' => 3],
            ['name' => 'Verificare Avans', 'key' => 'verificare_avans', 'stage' => 2, 'sort_order' => 4],
            ['name' => 'Negociere Termeni', 'key' => 'negociere_termeni', 'stage' => 2, 'sort_order' => 5],
            
            // Stage 3 - Pregătire
            ['name' => 'Schiță Producție', 'key' => 'schita_productie', 'stage' => 3, 'sort_order' => 1],
            ['name' => 'Proiect Structură', 'key' => 'proiect_structura', 'stage' => 3, 'sort_order' => 2],
            ['name' => 'Simulare cu Cote', 'key' => 'simulare_cote', 'stage' => 3, 'sort_order' => 3],
            ['name' => 'Aprobare Design Client', 'key' => 'aprobare_design_client', 'stage' => 3, 'sort_order' => 4],
            ['name' => 'Specificații Tehnice', 'key' => 'specificatii_tehnice', 'stage' => 3, 'sort_order' => 5],
            ['name' => 'Plan de Lucru', 'key' => 'plan_lucru', 'stage' => 3, 'sort_order' => 6],
            
            // Stage 4 - Aprovizionare
            ['name' => 'Comandă Materiale', 'key' => 'comanda_materiale', 'stage' => 4, 'sort_order' => 1],
            ['name' => 'Recepție Materiale', 'key' => 'receptie_materiale', 'stage' => 4, 'sort_order' => 2],
            ['name' => 'Verificare Calitate Materiale', 'key' => 'verificare_calitate', 'stage' => 4, 'sort_order' => 3],
            ['name' => 'Comandă Consumabile', 'key' => 'comanda_consumabile', 'stage' => 4, 'sort_order' => 4],
            ['name' => 'Rezervare Materii Prime', 'key' => 'rezervare_materii_prime', 'stage' => 4, 'sort_order' => 5],
            ['name' => 'Verificare Stoc', 'key' => 'verificare_stoc', 'stage' => 4, 'sort_order' => 6],
            
            // Stage 5 - Execuție
            ['name' => 'CNC', 'key' => 'cnc', 'stage' => 5, 'sort_order' => 1],
            ['name' => 'Printing', 'key' => 'printing', 'stage' => 5, 'sort_order' => 2],
            ['name' => 'Cutting', 'key' => 'cutting', 'stage' => 5, 'sort_order' => 3],
            ['name' => 'Welding', 'key' => 'welding', 'stage' => 5, 'sort_order' => 4],
            ['name' => 'Coating', 'key' => 'coating', 'stage' => 5, 'sort_order' => 5],
            ['name' => 'Letter Bending', 'key' => 'letter_bending', 'stage' => 5, 'sort_order' => 6],
            ['name' => 'Tăiere Polistiren', 'key' => 'taiere_polistiren', 'stage' => 5, 'sort_order' => 7],
            ['name' => 'Tăiere Laser', 'key' => 'taiere_laser', 'stage' => 5, 'sort_order' => 8],
            ['name' => 'Asamblare Litere', 'key' => 'asamblare_litere', 'stage' => 5, 'sort_order' => 9],
            ['name' => 'Asamblare Casetă', 'key' => 'asamblare_caseta', 'stage' => 5, 'sort_order' => 10],
            ['name' => 'Print Banner cu Finisări', 'key' => 'print_banner_finisari', 'stage' => 5, 'sort_order' => 11],
            ['name' => 'Print Folie + Laminat', 'key' => 'print_folie_laminat', 'stage' => 5, 'sort_order' => 12],
            ['name' => 'Print + Decupare', 'key' => 'print_decupare', 'stage' => 5, 'sort_order' => 13],
            ['name' => 'Colantare', 'key' => 'colantare', 'stage' => 5, 'sort_order' => 14],
            ['name' => 'Recondiționare', 'key' => 'reconditionare', 'stage' => 5, 'sort_order' => 15],
            ['name' => 'Control Calitate', 'key' => 'control_calitate', 'stage' => 5, 'sort_order' => 16],
            ['name' => 'Ambalare', 'key' => 'ambalare', 'stage' => 5, 'sort_order' => 17],
            
            // Stage 6 - Livrare
            ['name' => 'Livrare', 'key' => 'livrare', 'stage' => 6, 'sort_order' => 1],
            ['name' => 'Livrare și Montaj', 'key' => 'livrare_montaj', 'stage' => 6, 'sort_order' => 2],
            ['name' => 'Anunțare pentru Ridicare', 'key' => 'anuntare_ridicare', 'stage' => 6, 'sort_order' => 3],
            ['name' => 'Curier', 'key' => 'curier', 'stage' => 6, 'sort_order' => 4],
            ['name' => 'Montaj La Fața Locului', 'key' => 'montaj_fata_locului', 'stage' => 6, 'sort_order' => 5],
            ['name' => 'Instructaj Client', 'key' => 'instructaj_client', 'stage' => 6, 'sort_order' => 6],
            ['name' => 'Proces Verbal Recepție', 'key' => 'proces_verbal_receptie', 'stage' => 6, 'sort_order' => 7],
            
            // Stage 7 - Facturare
            ['name' => 'Factură Finală', 'key' => 'factura_finala', 'stage' => 7, 'sort_order' => 1],
            ['name' => 'Follow-up Factură Finală', 'key' => 'followup_factura', 'stage' => 7, 'sort_order' => 2],
            ['name' => 'Verificare Plată', 'key' => 'verificare_plata', 'stage' => 7, 'sort_order' => 3],
            ['name' => 'Emitere Garanție', 'key' => 'emitere_garantie', 'stage' => 7, 'sort_order' => 4],
            ['name' => 'Raport Financiar', 'key' => 'raport_financiar', 'stage' => 7, 'sort_order' => 5],
            ['name' => 'Arhivare Documentație', 'key' => 'arhivare_documentatie', 'stage' => 7, 'sort_order' => 6],
        ];

        foreach ($activityTypes as $type) {
            ActivityType::updateOrCreate(
                ['key' => $type['key']],
                array_merge($type, [
                    'is_active' => true,
                    'description' => null,
                ])
            );
        }

        $this->command->info('Created ' . count($activityTypes) . ' activity types.');
    }
}

