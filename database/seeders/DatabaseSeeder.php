<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Client;
use App\Models\Comanda;
use App\Models\ComandaItem;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Seed system settings first
        $this->call(SystemSettingSeeder::class);
        
        // Seed company settings
        $this->call(CompanySettingsSeeder::class);
        
        // Create clients
        $clients = $this->createClients();

        // Create comandas with items
        $this->createComandas($clients);
    }

    

  
    private function createClients(): array
    {
        $clients = [];

        $clientData = [
            ['company_name' => 'SC TechSoft SRL', 'email' => '<EMAIL>', 'phone' => '0721234567', 'address' => 'Str. Aviatorilor nr. 12, București, Sector 1'],
            ['company_name' => 'SC WebDesign Pro SRL', 'email' => '<EMAIL>', 'phone' => '0722345678', 'address' => 'Bd. Unirii nr. 45, Cluj-Napoca'],
            ['company_name' => 'SC Marketing Plus SRL', 'email' => '<EMAIL>', 'phone' => '0723456789', 'address' => 'Str. Republicii nr. 78, Timișoara'],
            ['company_name' => 'SC Digital Solutions SRL', 'email' => '<EMAIL>', 'phone' => '0724567890', 'address' => 'Calea Victoriei nr. 156, București, Sector 2'],
            ['company_name' => 'SC Creative Agency SRL', 'email' => '<EMAIL>', 'phone' => '0725678901', 'address' => 'Str. Mihail Kogălniceanu nr. 23, Iași'],
            ['company_name' => 'SC Business Consulting SRL', 'email' => '<EMAIL>', 'phone' => '0726789012', 'address' => 'Bd. Eroilor nr. 89, Brașov'],
            ['company_name' => 'SC Innovation Hub SRL', 'email' => '<EMAIL>', 'phone' => '0727890123', 'address' => 'Str. Dorobanților nr. 34, Constanța'],
            ['company_name' => 'SC Smart Systems SRL', 'email' => '<EMAIL>', 'phone' => '0728901234', 'address' => 'Bd. Carol I nr. 67, Craiova'],
        ];

        foreach ($clientData as $data) {
            $clients[] = Client::create($data);
        }

        return $clients;
    }

    private function createComandas(array $clients): void
    {
        $pms = User::where('role', 'pm')->get();
        $specialists = User::where('role', 'specialist')->get();

        $comandaTemplates = [
            ['name' => 'Website Redesign', 'description' => 'Complete website redesign with modern UI/UX', 'value' => 15000],
            ['name' => 'Mobile App Development', 'description' => 'Native mobile app for iOS and Android', 'value' => 25000],
            ['name' => 'SEO Optimization', 'description' => 'Complete SEO audit and optimization', 'value' => 5000],
            ['name' => 'Brand Identity Package', 'description' => 'Logo, business cards, and brand guidelines', 'value' => 8000],
            ['name' => 'E-commerce Platform', 'description' => 'Custom e-commerce solution with payment integration', 'value' => 30000],
            ['name' => 'Social Media Campaign', 'description' => '3-month social media marketing campaign', 'value' => 6000],
            ['name' => 'CRM System Implementation', 'description' => 'Custom CRM system for sales management', 'value' => 20000],
            ['name' => 'Content Management System', 'description' => 'Custom CMS with blog and portfolio', 'value' => 12000],
            ['name' => 'Digital Marketing Strategy', 'description' => 'Comprehensive digital marketing plan', 'value' => 7000],
            ['name' => 'Cloud Migration', 'description' => 'Migration of infrastructure to cloud', 'value' => 18000],
            ['name' => 'API Integration', 'description' => 'Third-party API integration and documentation', 'value' => 9000],
            ['name' => 'Security Audit', 'description' => 'Complete security audit and penetration testing', 'value' => 11000],
            ['name' => 'Database Optimization', 'description' => 'Database performance tuning and optimization', 'value' => 4500],
            ['name' => 'UI/UX Consulting', 'description' => 'User experience consulting and wireframing', 'value' => 6500],
            ['name' => 'Training Program', 'description' => 'Staff training on new systems', 'value' => 3500],
        ];

        foreach ($comandaTemplates as $index => $template) {
            $client = $clients[array_rand($clients)];
            $pm = $pms->random();

            // Determine if this comanda should have a deadline (at least half)
            $hasDeadline = $index < 8; // First 8 will have deadlines

            $comanda = Comanda::create([
                'name' => $template['name'],
                'description' => $template['description'],
                'client_id' => $client->id,
                'owner_id' => $pm->id,
                'total_value' => $template['value'],
                'stage' => rand(1, 7),
                'priority' => rand(0, 1) == 1,
                'fast_track' => rand(0, 1) == 1,
                'arrival_time' => now()->subDays(rand(1, 30)),
                'arrival_channel' => ['email', 'whatsapp', 'website', 'phone', 'walk_in'][rand(0, 4)],
                'deadline' => $hasDeadline ? now()->addDays(rand(1, 30)) : null,
            ]);

            // Decide if this comanda should have multiple items or just the default
            $hasMultipleItems = rand(0, 1) == 1;

            if ($hasMultipleItems) {
                // Delete the default item that was auto-created
                $comanda->items()->where('is_default_item', true)->delete();

                // Create 2-5 items
                $itemCount = rand(2, 5);
                $itemTemplates = [
                    ['name' => 'Design Phase', 'description' => 'UI/UX design and mockups', 'unit' => 'hours'],
                    ['name' => 'Development Phase', 'description' => 'Frontend and backend development', 'unit' => 'hours'],
                    ['name' => 'Testing Phase', 'description' => 'QA testing and bug fixes', 'unit' => 'hours'],
                    ['name' => 'Deployment', 'description' => 'Production deployment and monitoring', 'unit' => 'hours'],
                    ['name' => 'Documentation', 'description' => 'Technical and user documentation', 'unit' => 'pages'],
                    ['name' => 'Training', 'description' => 'User training sessions', 'unit' => 'sessions'],
                ];

                shuffle($itemTemplates);

                for ($i = 0; $i < $itemCount; $i++) {
                    $itemTemplate = $itemTemplates[$i];
                    $quantity = rand(10, 100);
                    $unitPrice = rand(50, 200);

                    ComandaItem::create([
                        'comanda_id' => $comanda->id,
                        'name' => $itemTemplate['name'],
                        'description' => $itemTemplate['description'],
                        'quantity' => $quantity,
                        'unit' => $itemTemplate['unit'],
                        'unit_price' => $unitPrice,
                        'total_price' => $quantity * $unitPrice,
                        'status' => ['pending', 'in_progress', 'completed'][rand(0, 2)],
                        'completion_percentage' => rand(0, 100),
                        'assigned_to' => $specialists->random()->id,
                        'sort_order' => $i,
                    ]);
                }
            }
            // If not multiple items, the default item was already created by the model observer
        }
    }
}
