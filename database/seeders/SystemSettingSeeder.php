<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\SystemSetting;

class SystemSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Document series settings
        SystemSetting::setDocumentSeries('proforma', 'CRMPRF');
        SystemSetting::setDocumentSeries('invoice', 'CRMINV');
        SystemSetting::setDocumentSeries('contract', 'CRMCTR');
        SystemSetting::setDocumentSeries('guarantee', 'CRMGAR');
        SystemSetting::setDocumentSeries('proces_verbal', 'CRMPV');
        SystemSetting::setDocumentSeries('bill', 'CRMBILL');
        SystemSetting::setDocumentSeries('note', 'CRMNOTE');

        $this->command->info('System settings seeded successfully.');
    }
}