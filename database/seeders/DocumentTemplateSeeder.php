<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DocumentTemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $superadmin = \App\Models\User::where('role', 'superadmin')->first();

        if (!$superadmin) {
            $this->command->warn('No superadmin user found. Creating basic user for templates.');
            $superadmin = \App\Models\User::create([
                'name' => 'System Admin',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'role' => 'superadmin',
                'is_active' => true,
                'email_verified_at' => now(),
            ]);
        }

        $templates = [
            [
                'name' => 'Proforma Standard',
                'type' => 'proforma',
                'blade_template' => 'proforma',
                'is_default' => true,
                'required_fields' => ['client_id', 'comanda_id'],
                'optional_fields' => ['payment_terms', 'delivery_terms', 'validity_period'],
                'custom_fields' => [
                    'payment_terms' => [
                        'type' => 'text',
                        'label' => 'Termeni de plată',
                        'default' => 'Plata în avans 100%'
                    ],
                    'delivery_terms' => [
                        'type' => 'text',
                        'label' => 'Termeni de livrare',
                        'default' => 'Livrare în 7-14 zile lucrătoare'
                    ],
                    'validity_period' => [
                        'type' => 'number',
                        'label' => 'Valabilitate (zile)',
                        'default' => 30
                    ],
                    'notes' => [
                        'type' => 'textarea',
                        'label' => 'Observații',
                        'default' => ''
                    ]
                ],
                'fiscal_requirements' => [
                    'requires_vat' => true,
                    'vat_rate' => 19,
                    'requires_cui' => true
                ]
            ],
            [
                'name' => 'Ofertă Standard',
                'type' => 'quotation',
                'blade_template' => 'quotation',
                'is_default' => true,
                'required_fields' => ['client_id', 'comanda_id'],
                'optional_fields' => ['payment_terms', 'delivery_terms', 'validity_period', 'currency'],
                'custom_fields' => [
                    'payment_terms' => [
                        'type' => 'text',
                        'label' => 'Termeni de plată',
                        'default' => 'Plata în avans 100%'
                    ],
                    'delivery_terms' => [
                        'type' => 'text',
                        'label' => 'Termeni de livrare',
                        'default' => 'Livrare în 7-14 zile lucrătoare'
                    ],
                    'validity_period' => [
                        'type' => 'number',
                        'label' => 'Valabilitate (zile)',
                        'default' => 15
                    ],
                    'currency' => [
                        'type' => 'select',
                        'label' => 'Monedă',
                        'options' => ['EUR', 'RON'],
                        'default' => 'EUR'
                    ],
                    'notes' => [
                        'type' => 'textarea',
                        'label' => 'Observații',
                        'default' => ''
                    ]
                ],
                'fiscal_requirements' => [
                    'requires_vat' => true,
                    'vat_rate' => 19,
                    'requires_cui' => true
                ]
            ],
            [
                'name' => 'Factură Standard',
                'type' => 'invoice',
                'blade_template' => 'default',
                'is_default' => true,
                'required_fields' => ['client_id', 'comanda_id'],
                'optional_fields' => ['payment_method', 'due_date'],
                'custom_fields' => [
                    'payment_method' => [
                        'type' => 'select',
                        'label' => 'Modalitate de plată',
                        'options' => ['Transfer bancar', 'Numerar', 'Card'],
                        'default' => 'Transfer bancar'
                    ],
                    'due_date' => [
                        'type' => 'date',
                        'label' => 'Scadența',
                        'default' => null
                    ]
                ],
                'fiscal_requirements' => [
                    'requires_vat' => true,
                    'vat_rate' => 19,
                    'requires_cui' => true,
                    'requires_fiscal_number' => true
                ]
            ],
            [
                'name' => 'Contract Standard',
                'type' => 'contract',
                'blade_template' => 'default',
                'is_default' => true,
                'required_fields' => ['client_id', 'comanda_id'],
                'optional_fields' => ['contract_duration', 'warranty_period'],
                'custom_fields' => [
                    'contract_duration' => [
                        'type' => 'text',
                        'label' => 'Durata contractului',
                        'default' => 'Conform specificațiilor'
                    ],
                    'warranty_period' => [
                        'type' => 'text',
                        'label' => 'Perioada de garanție',
                        'default' => '24 luni'
                    ]
                ],
                'fiscal_requirements' => []
            ]
        ];

        foreach ($templates as $templateData) {
            \App\Models\DocumentTemplate::create(array_merge($templateData, [
                'created_by' => $superadmin->id,
                'updated_by' => $superadmin->id,
            ]));
        }

        $this->command->info('Document templates seeded successfully.');
    }
}
