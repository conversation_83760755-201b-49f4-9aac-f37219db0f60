<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('comenzi', function (Blueprint $table) {
            $table->id();

            // Identification
            $table->uuid('uuid')->unique();
            $table->string('internal_number')->unique(); // C001, C002, etc.
            $table->string('name');
            $table->text('description')->nullable();

            // Relationships
            $table->foreignId('client_id')->constrained()->onDelete('cascade');
            $table->foreignId('owner_id')->constrained('users')->onDelete('cascade'); // PM owner

            // Arrival Information
            $table->timestamp('arrival_time');
            $table->enum('arrival_channel', ['email', 'whatsapp', 'website', 'phone', 'walk_in']);
            $table->string('contact_person')->nullable();

            // Timeline
            $table->timestamp('deadline')->nullable();

            // Workflow
            $table->integer('stage')->default(1); // 1-7 stages

            // Flags
            $table->boolean('fast_track')->default(false);
            $table->boolean('priority')->default(false);

            // Financial
            $table->decimal('total_value', 10, 2)->default(0);

            // Status
            $table->boolean('is_active')->default(true);
            $table->boolean('is_finished')->default(false);

            // File uploads
            $table->json('attachments')->nullable();

            $table->timestamps();

            // Indexes
            $table->index(['stage', 'owner_id']);
            $table->index(['client_id', 'stage']);
            $table->index('arrival_time');
            $table->index('deadline');
            $table->index(['fast_track', 'priority']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('comenzi');
    }
};
