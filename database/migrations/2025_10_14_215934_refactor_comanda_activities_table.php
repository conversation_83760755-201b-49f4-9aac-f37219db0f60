<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        // Drop the old table and recreate it with new structure
        Schema::dropIfExists('comanda_activities');
        
        Schema::create('comanda_activities', function (Blueprint $table) {
            $table->id();
            
            // Core Fields
            $table->foreignId('comanda_id')->constrained('comenzi')->onDelete('cascade');
            $table->uuid('uuid')->unique();
            $table->string('type'); // Activity type based on stage
            $table->text('description');
            
            // Assignment
            $table->foreignId('assigned_to')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('assigned_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('assigned_at')->nullable();
            
            // Completion
            $table->timestamp('completed_at')->nullable();
            $table->boolean('is_done')->default(false);
            
            $table->timestamps();
            
            // Indexes
            $table->index(['comanda_id', 'is_done']);
            $table->index('assigned_to');
            $table->index('type');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('comanda_activities');
        
        // Recreate old structure (reference the original migration)
        // We'll keep this empty since we're moving forward
    }
};
