<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('clients', function (Blueprint $table) {
            $table->id();

            // Company or Individual
            $table->string('company_name')->nullable(); // For companies
            $table->string('name')->nullable(); // For individuals

            // Romanian Commercial Requirements
            $table->string('cui')->nullable(); // Tax identification number
            $table->string('reg_com')->nullable(); // Commercial registry number
            $table->text('address');
            $table->string('email')->nullable();
            $table->string('phone')->nullable();

            // Contact Information
            $table->string('contact_person')->nullable();
            $table->string('contact_email')->nullable();
            $table->string('contact_phone')->nullable();

            // Banking Details
            $table->string('bank_name')->nullable();
            $table->string('bank_account')->nullable();

            // Additional Info
            $table->text('notes')->nullable();
            $table->boolean('is_active')->default(true);

            $table->timestamps();

            // Indexes
            $table->index(['company_name', 'name']);
            $table->index('cui');
            $table->index('email');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('clients');
    }
};
