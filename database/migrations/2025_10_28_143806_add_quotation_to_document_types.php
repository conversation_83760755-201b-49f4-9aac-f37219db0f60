<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add 'quotation' to document_templates enum
        DB::statement("ALTER TABLE document_templates MODIFY COLUMN type ENUM('proforma', 'invoice', 'contract', 'guarantee', 'proces_verbal', 'bill', 'note', 'quotation')");
        
        // Add 'quotation' to generated_documents enum
        DB::statement("ALTER TABLE generated_documents MODIFY COLUMN type ENUM('proforma', 'invoice', 'contract', 'guarantee', 'proces_verbal', 'bill', 'note', 'quotation')");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove 'quotation' from document_templates enum
        DB::statement("ALTER TABLE document_templates MODIFY COLUMN type ENUM('proforma', 'invoice', 'contract', 'guarantee', 'proces_verbal', 'bill', 'note')");
        
        // Remove 'quotation' from generated_documents enum
        DB::statement("ALTER TABLE generated_documents MODIFY COLUMN type ENUM('proforma', 'invoice', 'contract', 'guarantee', 'proces_verbal', 'bill', 'note')");
    }
};
