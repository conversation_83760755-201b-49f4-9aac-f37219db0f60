<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('comanda_items', function (Blueprint $table) {
            $table->id();

            // Relationships
            $table->foreignId('comanda_id')->constrained('comenzi')->onDelete('cascade');

            // Item Details
            $table->string('name');
            $table->text('description')->nullable();
            $table->integer('quantity')->default(1);
            $table->string('unit')->default('pcs'); // pcs, hours, pages, etc.

            // Pricing
            $table->decimal('unit_price', 10, 2)->default(0);
            $table->decimal('total_price', 10, 2)->default(0);

            // Status
            $table->enum('status', ['pending', 'in_progress', 'completed', 'cancelled'])->default('pending');
            $table->integer('completion_percentage')->default(0);

            // Assignment (inherits from comanda owner_id by default)
            $table->foreignId('assigned_to')->nullable()->constrained('users')->onDelete('set null');

            // Metadata
            $table->json('metadata')->nullable(); // For flexible additional data
            $table->integer('sort_order')->default(0);
            $table->boolean('is_default_item')->default(false);

            $table->timestamps();

            // Indexes
            $table->index(['comanda_id', 'status']);
            $table->index(['assigned_to', 'status']);
            $table->index('sort_order');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('comanda_items');
    }
};
