<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('activity_types', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Display name
            $table->string('key')->unique(); // Unique identifier (e.g., 'client_meeting')
            $table->integer('stage'); // Which stage (1-7)
            $table->text('description')->nullable();
            $table->integer('sort_order')->default(0);
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            $table->index(['stage', 'is_active']);
            $table->index('key');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('activity_types');
    }
};
