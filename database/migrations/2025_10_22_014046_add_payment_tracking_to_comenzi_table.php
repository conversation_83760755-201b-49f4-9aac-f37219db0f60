<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('comenzi', function (Blueprint $table) {
            // Track total amount paid so far
            $table->decimal('paid_amount', 10, 2)->default(0)->after('total_value');
            
            // Cached payment status for quick queries
            // Values: unpaid, partial, paid, overpaid
            $table->string('payment_status', 20)->default('unpaid')->after('paid_amount');
            
            // Add index for common queries
            $table->index('payment_status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('comenzi', function (Blueprint $table) {
            $table->dropIndex(['payment_status']);
            $table->dropColumn(['paid_amount', 'payment_status']);
        });
    }
};
