<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('generated_documents', function (Blueprint $table) {
            $table->id();

            // Identification
            $table->uuid('uuid')->unique();
            $table->string('document_number')->unique(); // Auto-generated, type-specific (e.g., PRO-2024-001)

            // Relationships
            $table->foreignId('comanda_id')->constrained('comenzi')->onDelete('cascade');
            $table->foreignId('template_id')->constrained('document_templates')->onDelete('cascade');
            $table->foreignId('client_id')->constrained('clients')->onDelete('cascade');

            // Document Info
            $table->enum('type', ['proforma', 'invoice', 'contract', 'guarantee', 'proces_verbal', 'bill', 'note']);
            $table->string('title');
            $table->text('description')->nullable();

            // Generation Data
            $table->json('generated_data')->nullable(); // Snapshot of data used
            $table->json('custom_field_values')->nullable(); // User-filled custom fields

            // File Management
            $table->longText('html_content')->nullable(); // Rendered HTML
            $table->string('pdf_path')->nullable(); // Google Drive path or local storage path
            $table->integer('pdf_size')->nullable(); // File size in bytes
            $table->timestamp('pdf_generated_at')->nullable();

            // Status & Workflow
            $table->enum('status', ['draft', 'generated', 'sent', 'signed', 'archived'])->default('draft');
            $table->foreignId('generated_by')->constrained('users')->onDelete('cascade');
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('approved_at')->nullable();

            // Romanian Compliance
            $table->json('fiscal_data')->nullable(); // Tax-related information
            $table->boolean('legal_requirements_met')->default(false);

            $table->timestamps();

            // Indexes
            $table->index(['comanda_id', 'type']);
            $table->index(['client_id', 'type']);
            $table->index(['type', 'status']);
            $table->index('generated_by');
            $table->index('approved_by');
            $table->index('pdf_generated_at');
            $table->index('approved_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('generated_documents');
    }
};

