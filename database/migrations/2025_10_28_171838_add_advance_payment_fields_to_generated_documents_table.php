<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('generated_documents', function (Blueprint $table) {
            // Track if this is an advance payment proforma
            $table->boolean('is_advance_payment')->default(false)->after('exchange_rate_used');
            
            // Store the advance payment percentage (e.g., 50 for 50%)
            $table->decimal('advance_payment_percentage', 5, 2)->nullable()->after('is_advance_payment');
            
            // Reference to the source quotation for advance payment proformas
            $table->foreignId('source_quotation_id')->nullable()->after('advance_payment_percentage')
                ->constrained('generated_documents')->nullOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('generated_documents', function (Blueprint $table) {
            $table->dropForeign(['source_quotation_id']);
            $table->dropColumn(['is_advance_payment', 'advance_payment_percentage', 'source_quotation_id']);
        });
    }
};
