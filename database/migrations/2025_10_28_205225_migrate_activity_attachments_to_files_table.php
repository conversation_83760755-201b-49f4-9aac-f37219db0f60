<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use App\Models\ComandaActivity;
use App\Models\File;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Migrate existing attachments from JSON column to files table
        $activities = ComandaActivity::whereNotNull('attachments')->get();
        
        foreach ($activities as $activity) {
            if (empty($activity->attachments) || !is_array($activity->attachments)) {
                continue;
            }

            foreach ($activity->attachments as $attachmentPath) {
                // The path in attachments column is like: "activity-files/filename.ext"
                // The local disk root is storage/app/private, so we use the path as-is
                $fullPath = $attachmentPath;
                
                // Check if file exists (using 'local' disk which points to storage/app/private)
                if (!Storage::disk('local')->exists($fullPath)) {
                    \Log::warning("Migration: File not found: {$fullPath} for activity {$activity->id}");
                    continue;
                }

                // Check if File record already exists for this path and activity
                $existingFile = File::where('fileable_type', ComandaActivity::class)
                    ->where('fileable_id', $activity->id)
                    ->where('path', $fullPath)
                    ->first();

                if ($existingFile) {
                    \Log::info("Migration: File record already exists for {$fullPath}");
                    continue;
                }

                // Get file info
                $originalName = basename($attachmentPath);
                $size = Storage::disk('local')->size($fullPath);
                $mimeType = Storage::disk('local')->mimeType($fullPath);
                $extension = pathinfo($originalName, PATHINFO_EXTENSION);

                // Create File record
                try {
                    File::create([
                        'uuid' => \Illuminate\Support\Str::uuid(),
                        'original_name' => $originalName,
                        'stored_name' => basename($fullPath),
                        'path' => $fullPath,
                        'disk' => 'local', // Using default disk
                        'size' => $size,
                        'mime_type' => $mimeType,
                        'extension' => $extension,
                        'fileable_type' => ComandaActivity::class,
                        'fileable_id' => $activity->id,
                        'category' => File::CATEGORY_ACTIVITY_INPUT,
                        'uploaded_by' => $activity->assigned_by ?? $activity->comanda->owner_id ?? null,
                        'is_public' => false,
                    ]);

                    \Log::info("Migration: Created File record for {$fullPath} (activity {$activity->id})");
                } catch (\Exception $e) {
                    \Log::error("Migration: Failed to create File record for {$fullPath}: " . $e->getMessage());
                }
            }
        }

        // Optional: Clear the attachments column after successful migration
        // Uncomment if you want to remove the old data after confirming everything works
        // DB::table('comanda_activities')->update(['attachments' => null]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove File records that were created by this migration
        // We identify them by fileable_type = ComandaActivity and path starting with 'activity-files/'
        File::where('fileable_type', ComandaActivity::class)
            ->where('path', 'like', 'activity-files/%')
            ->delete();
    }
};
