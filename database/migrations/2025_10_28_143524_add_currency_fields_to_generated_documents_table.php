<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('generated_documents', function (Blueprint $table) {
            $table->string('currency', 3)->default('EUR')->after('custom_field_values'); // EUR or RON
            $table->decimal('exchange_rate_used', 10, 4)->nullable()->after('currency'); // Rate used if converted
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('generated_documents', function (Blueprint $table) {
            $table->dropColumn(['currency', 'exchange_rate_used']);
        });
    }
};
