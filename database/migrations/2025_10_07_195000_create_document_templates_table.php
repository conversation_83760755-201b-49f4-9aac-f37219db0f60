<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('document_templates', function (Blueprint $table) {
            $table->id();

            // Identification
            $table->uuid('uuid')->unique();
            $table->string('name');

            // Template Configuration
            $table->enum('type', ['proforma', 'invoice', 'contract', 'guarantee', 'proces_verbal', 'bill', 'note']);
            $table->string('blade_template'); // Template file name (without .blade.php)
            $table->string('css_framework')->default('tailwind'); // CSS framework to use
            $table->longText('custom_css')->nullable(); // Custom CSS for this template

            // Status & Settings
            $table->boolean('is_active')->default(true);
            $table->boolean('is_default')->default(false); // Default template for this type
            $table->boolean('requires_approval')->default(false);

            // Field Configuration
            $table->json('required_fields')->nullable(); // Required fields for this template
            $table->json('optional_fields')->nullable(); // Optional fields
            $table->json('custom_fields')->nullable(); // Custom field definitions

            // Romanian Compliance
            $table->json('fiscal_requirements')->nullable(); // Fiscal/tax requirements

            // Localization
            $table->string('language')->default('ro'); // Template language

            // Ownership & Versioning
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->integer('version')->default(1); // Template version number

            $table->timestamps();

            // Indexes
            $table->index(['type', 'is_active']);
            $table->index(['type', 'is_default']);
            $table->index('created_by');
            $table->index('updated_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('document_templates');
    }
};

