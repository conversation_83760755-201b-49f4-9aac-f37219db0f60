<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('comanda_activities', function (Blueprint $table) {
            $table->id();

            // Identification
            $table->uuid('uuid')->unique();

            // Relationships - Flexible scoping
            $table->foreignId('comanda_id')->constrained('comenzi')->onDelete('cascade');
            $table->foreignId('comanda_item_id')->nullable()->constrained('comanda_items')->onDelete('cascade');

            // Stage & Type
            $table->integer('stage')->nullable();
            $table->string('substage_type')->nullable();
            $table->string('type'); // 'comment', 'status_change', 'assignment', 'file_upload', 'stage_change', etc.
            $table->string('title');
            $table->string('name')->nullable();
            $table->text('description')->nullable();

            // Activity Scoping
            $table->string('scope')->nullable(); // 'comanda' or 'item'

            // Assignment & Ownership
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('assigned_to')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('assigned_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('assigned_at')->nullable();

            // Approval System
            $table->boolean('requires_approval')->default(false);
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('approved_at')->nullable();
            $table->text('approval_notes')->nullable();

            // Status & Progress
            $table->string('status')->nullable(); // 'pending', 'in_progress', 'completed', 'approved'
            $table->integer('progress_percentage')->default(0);
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();

            // Time Tracking
            $table->integer('estimated_duration')->nullable(); // in minutes
            $table->integer('actual_duration')->nullable(); // in minutes

            // User & Timing (legacy)
            $table->timestamp('activity_time')->default(now());

            // Change Tracking
            $table->json('old_values')->nullable(); // For tracking what changed
            $table->json('new_values')->nullable(); // For tracking what it changed to

            // Notes & Communication
            $table->text('notes')->nullable();
            $table->text('internal_notes')->nullable();

            // Metadata
            $table->json('metadata')->nullable(); // For additional flexible data
            $table->boolean('is_internal')->default(false); // Internal notes vs client-visible

            // File Attachments (optional)
            $table->string('file_path')->nullable();
            $table->string('file_name')->nullable();
            $table->integer('file_size')->nullable();

            // Workflow status fields
            $table->boolean('is_done')->default(false);
            $table->boolean('is_closed')->default(false);
            $table->boolean('needs_rework')->default(false);

            // File uploads
            $table->json('activity_attachments')->nullable();

            $table->timestamps();

            // Indexes
            $table->index(['comanda_id', 'activity_time']);
            $table->index(['comanda_item_id', 'activity_time']);
            $table->index(['user_id', 'activity_time']);
            $table->index(['type', 'activity_time']);
            $table->index('is_internal');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('comanda_activities');
    }
};
