<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('files', function (Blueprint $table) {
            $table->id();
            
            // Identification
            $table->uuid('uuid')->unique();
            
            // File Information
            $table->string('original_name');
            $table->string('stored_name');
            $table->string('path');
            $table->string('disk')->default('local');
            $table->bigInteger('size'); // in bytes
            $table->string('mime_type');
            $table->string('extension')->nullable();
            
            // Polymorphic relationship
            $table->morphs('fileable'); // Creates fileable_type and fileable_id
            
            // Categorization
            $table->string('category')->default('general');
            
            // Ownership
            $table->foreignId('uploaded_by')->nullable()->constrained('users')->onDelete('set null');
            
            // Additional Info
            $table->text('description')->nullable();
            $table->json('metadata')->nullable();
            $table->boolean('is_public')->default(false);
            
            $table->timestamps();

            // Indexes (morphs() already creates index for fileable_type and fileable_id)
            $table->index('category');
            $table->index('uploaded_by');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('files');
    }
};

