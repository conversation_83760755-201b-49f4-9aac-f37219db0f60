# Laravel CRM Application Dockerfile
# Based on PHP 8.4 FPM with all required extensions and supervisor

FROM php:8.4-fpm

# Set working directory
WORKDIR /var/www/crmlxii

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    curl \
    libpng-dev \
    libonig-dev \
    libxml2-dev \
    libicu-dev \
    libzip-dev \
    zip \
    unzip \
    supervisor \
    nginx \
    redis-tools \
    && rm -rf /var/lib/apt/lists/*

# Install PHP extensions
RUN docker-php-ext-configure intl \
    && docker-php-ext-install \
        pdo_mysql \
        mysqli \
        mbstring \
        exif \
        pcntl \
        bcmath \
        gd \
        intl \
        zip \
        opcache \
        sockets

# Install Redis extension
RUN pecl install redis \
    && docker-php-ext-enable redis

# Install Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Create www-data user directories
RUN mkdir -p /var/www/.composer \
    && chown -R www-data:www-data /var/www/.composer

# Copy PHP configuration
COPY .misc/php.ini /usr/local/etc/php/php.ini

# Copy supervisor configuration
COPY .misc/supervisorcrml12.conf /etc/supervisor/conf.d/supervisorcrml12.conf

# Copy application code
COPY . /var/www/crmlxii

# Set proper permissions
RUN chown -R www-data:www-data /var/www/crmlxii \
    && chmod -R 755 /var/www/crmlxii \
    && chmod -R 775 /var/www/crmlxii/storage \
    && chmod -R 775 /var/www/crmlxii/bootstrap/cache

# Expose port 9000 for PHP-FPM
EXPOSE 9000

# Start supervisor
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/supervisord.conf"]
