version: '3.8'

services:
  # Laravel CRM Application
  crmlxii:
    build:
      context: ../..
      dockerfile: .misc/Dockerfile
    container_name: crmlxii-app
    restart: unless-stopped
    volumes:
      - ../../:/var/www/crmlxii
      - ./.misc/php.ini:/usr/local/etc/php/php.ini
      - ./.misc/supervisorcrml12.conf:/etc/supervisor/conf.d/supervisorcrml12.conf:ro
    networks:
      - crm-network
    depends_on:
      - mariadb
      - redis
    environment:
      - APP_ENV=production
      - APP_DEBUG=false
      - DB_CONNECTION=mariadb
      - DB_HOST=mariadb
      - DB_PORT=3306
      - DB_DATABASE=crmlxii_prod
      - DB_USERNAME=crmlxii_user
      - DB_PASSWORD=secure_password_here
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - CACHE_DRIVER=redis
      - SESSION_DRIVER=redis
      - QUEUE_CONNECTION=redis
      - BROADCAST_DRIVER=reverb
      - REVERB_APP_ID=your_app_id
      - REVERB_APP_KEY=your_app_key
      - REVERB_APP_SECRET=your_app_secret
      - REVERB_HOST=localhost
      - REVERB_PORT=8080
      - REVERB_SCHEME=http

  # MariaDB Database
  mariadb:
    image: mariadb:11.7
    container_name: crmlxii-mariadb
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: root_password_here
      MYSQL_DATABASE: crmlxii_prod
      MYSQL_USER: crmlxii_user
      MYSQL_PASSWORD: secure_password_here
    volumes:
      - mariadb_data:/var/lib/mysql
    networks:
      - crm-network
    ports:
      - "3306:3306"

  # Redis Cache & Session Store
  redis:
    image: redis:7-alpine
    container_name: crmlxii-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass redis_password_here
    volumes:
      - redis_data:/data
    networks:
      - crm-network
    ports:
      - "6379:6379"

  # Nginx Reverse Proxy (optional)
  nginx:
    image: nginx:alpine
    container_name: crmlxii-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ../../public:/var/www/crmlxii/public:ro
      - ./nginx.conf:/etc/nginx/conf.d/default.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    networks:
      - crm-network
    depends_on:
      - crmlxii

volumes:
  mariadb_data:
    driver: local
  redis_data:
    driver: local

networks:
  crm-network:
    driver: bridge
