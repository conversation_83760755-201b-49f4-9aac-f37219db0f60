Display company information based on the CUI

get
https://infocui.ro/system/api/data
Parameters
Field	Type	Description
key	string	Your API KEY
cui	string	Company CUI


Format raspuns
Field	Type	Description
status	int	
Status code handler
200 = Success
500 = Fail
message	string	Status response message
data	array	
Additional array of data


Example of successful call response
{
        "cod_fiscal": 14399840, //Cod fiscal
        "nume": DANTE INTERNATIONAL SA, //Denumire platitor
        "cod_inmatriculare": J40/372/2002, //Numar inmatriculare ONRC
        "stare" => 1048 - 'functiune' //stare firma
        "adresa": Bucureşti Sectorul 6, Şos. VIRTUŢII, Nr. 148, spatiul E47, Cod poștal 60787, //Adresa firma
        "loc": Bucureşti, //Localitate platitor  componenta din adresa sediului social
        "str": Şos. VIRTUŢII, //Strada platitor  componenta din adresa sediului social
        "nr": 148, //Numar platitor componenta din adresa sediului social
        "di": 30 Iunie 2017, //Data inregistrarii ultimei declaratii
        "dp": 02 Ianuarie 2018, //Data ultimei prelucrari
        "fax": , //Fax platitor
        "sect": 6, //Sector platitor componenta din adresa sediului social
        "tel": 0212038100, //Numar telefon platitor
        "jud_com": J40, //Componenta judet din numar de inmatriculare la Registrul Comertului
        "nr_com": 372, //Componenta numar din numar de inmatriculare la Registrul Comertului
        "an_com": 2002, //componenta an din numar de inmatriculare la Registrul Comertului
        "act_aut": , //Act autorizare
        "tva": "01-02-2002", //Taxa pe valoarea adaugata (data luarii in evidenta)
        "sfarsit": null, //Sfarsit
        "cp": 60787, //Codul postal
        "data_stare": "29 August 2006", //Data calendaristica stare societate
        "judet": "MUNICIPIUL BUCUREŞTI", //judet platitor  componenta din adresa sediului social
        "imp_100": "01-01-2004", //Impozit pe profit (data luarii in evidenta)
        "imp_120": NU, //Impozit pe veniturile microintreprinderilor (data luarii in evidenta)
        "imp_200": NU, //Accize (data luarii in evidenta)
        "imp_410": NU, //Contributia de asigurari sociale (data luarii in evidenta)
        "imp_416": NU, //Contributia de asigurare pentru accidente de munca si boli profesionale datorate de angajator (data luarii in evidenta)
        "imp_420": NU, //Contributia de asigurari pentru somaj (data luarii in evidenta)
        "imp_423": NU, //Contributia angajatorilor pentru Fondul de garantare pentru plata creantelor sociale (data luarii in evidenta)
        "imp_430": NU, //Contributia pentru asigurari de sanatate (data luarii in evidenta)
        "imp_439": NU, //Contributii pentru concedii si indemnizatii de la persoane juridice sau fizice (data luarii in evidenta)
        "imp_500": NU, //Taxa jocuri de noroc (data luarii in evidenta)
        "imp_602": "01-05-2003", //Impozit pe veniturile din salarii si asimilate salariilor (data luarii in evidenta)
        "imp_701": NU, //Impozit pe constructii(data luarii in evidenta)
        "imp_710": NU, //Impozit la titeiul si la gazele naturale din productia interna (data luarii in evidenta)
        "imp_755": NU, //Redevente miniere/Venituri din concesiuni si inchirieri (data luarii in evidenta)
        "imp_756": NU, //Redevente petroliere (data luarii in evidenta)
        "bilanturi": "WEB_AN2014,WEB_AN2015,WEB_AN2016,WEB_AN2017,WEB_AN2018", //Bilanturi
        "imp_412": "01-01-2018", //Contributiile de asigurari sociale (data luarii in evidenta)
        "imp_480": "01-01-2018", //Contributia asiguratorie pentru munca (data luarii in evidenta)
        "imp_432": "01-01-2018", //Contributia de asigurari sociale de sanatate(data luarii in evidenta)
        "detalii_adresa": "spatiul E47", //Alte informatii despre adresa
        "euid": "ROONRC.J40/372/2002", //EUID
        "stare_firma": "1048,2080", //Stare firma
        "timestamp": 1615491768 //Timestamp executie API call
             }

Example of error call response
{
    "status": 400,
    "message": "Something went wrong!",
    "data": false
}

INFOCUI_API_KEY=827078d07e6ed6cf8219851b641f300563c51cc3