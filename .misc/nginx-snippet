server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name crm.concept-42.com;
    root /var/www/crmlxii/public;
    index index.php index.html index.htm;

    # Add this line for file uploads
    client_max_body_size 100M;

    # SSL configuration (certificates should be mounted from host)
    ssl_certificate /etc/letsencrypt/live/crm.concept-42.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/crm.concept-42.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # Minimal nginx interference - let Laravel/Filament handle security headers
    # Only essential HTTPS enforcement
    add_header Strict-Transport-Security "max-age=63072000" always;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss;

    # Handle Laravel routes
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # Handle PHP files - optimized for Laravel
    location ~ \.php$ {
        fastcgi_pass **********:9000;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;

        # Laravel/Filament optimizations
        fastcgi_buffer_size 128k;
        fastcgi_buffers 4 256k;
        fastcgi_busy_buffers_size 256k;
        fastcgi_read_timeout 300;

        # Don't hide headers that Laravel might need
        # fastcgi_hide_header X-Powered-By; # Commented out
    }

location /reverb/ {
    proxy_http_version 1.1;
    resolver 127.0.0.11;
    proxy_set_header Host $http_host;
    proxy_set_header Scheme $scheme;
    proxy_set_header SERVER_PORT $server_port;
    proxy_set_header REMOTE_ADDR $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "Upgrade";

    # Important: rewrite to remove /reverb prefix before passing to Reverb
    rewrite ^/reverb/(.*) /$1 break;
    proxy_pass http://**********:8081;
}


    # Basic static file serving - let Laravel handle caching headers
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|woff|woff2|ttf|svg)$ {
        try_files $uri =404;
    }

    # Deny access to sensitive files
    location ~ /\.(?!well-known).* {
        deny all;
    }

    location ~ /\.ht {
        deny all;
    }

    # Deny access to storage and bootstrap/cache
    location ~ ^/(storage|bootstrap/cache)/ {
        deny all;
    }
}
