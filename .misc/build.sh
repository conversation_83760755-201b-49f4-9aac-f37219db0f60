#!/bin/bash

# Laravel CRM Docker Build Script
# This script builds and optionally deploys the CRM container

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
IMAGE_NAME="crmlxii"
IMAGE_TAG="latest"
DOCKERFILE_PATH=".misc/Dockerfile"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
}

# Build the Docker image
build_image() {
    log_info "Building Docker image: ${IMAGE_NAME}:${IMAGE_TAG}"
    
    if docker build -f "${DOCKERFILE_PATH}" -t "${IMAGE_NAME}:${IMAGE_TAG}" .; then
        log_success "Docker image built successfully!"
    else
        log_error "Failed to build Docker image"
        exit 1
    fi
}

# Show image information
show_image_info() {
    log_info "Image information:"
    docker images "${IMAGE_NAME}:${IMAGE_TAG}" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
}

# Test the image
test_image() {
    log_info "Testing the Docker image..."
    
    # Test PHP version and extensions
    if docker run --rm "${IMAGE_NAME}:${IMAGE_TAG}" php --version; then
        log_success "PHP is working correctly"
    else
        log_error "PHP test failed"
        return 1
    fi
    
    # Test required extensions
    log_info "Checking required PHP extensions..."
    REQUIRED_EXTENSIONS=("pdo_mysql" "mysqli" "intl" "redis" "bcmath" "gd" "zip" "opcache")
    
    for ext in "${REQUIRED_EXTENSIONS[@]}"; do
        if docker run --rm "${IMAGE_NAME}:${IMAGE_TAG}" php -m | grep -q "^${ext}$"; then
            log_success "Extension ${ext} is loaded"
        else
            log_error "Extension ${ext} is NOT loaded"
            return 1
        fi
    done
    
    log_success "All required extensions are loaded!"
}

# Clean up old images
cleanup() {
    log_info "Cleaning up old Docker images..."
    docker image prune -f
    log_success "Cleanup completed"
}

# Main execution
main() {
    echo "======================================"
    echo "  Laravel CRM Docker Build Script"
    echo "======================================"
    echo
    
    check_docker
    
    case "${1:-build}" in
        "build")
            build_image
            show_image_info
            ;;
        "test")
            test_image
            ;;
        "build-and-test")
            build_image
            show_image_info
            test_image
            ;;
        "cleanup")
            cleanup
            ;;
        "all")
            build_image
            show_image_info
            test_image
            cleanup
            ;;
        *)
            echo "Usage: $0 [build|test|build-and-test|cleanup|all]"
            echo
            echo "Commands:"
            echo "  build           - Build the Docker image (default)"
            echo "  test            - Test the existing image"
            echo "  build-and-test  - Build and test the image"
            echo "  cleanup         - Clean up old Docker images"
            echo "  all             - Build, test, and cleanup"
            exit 1
            ;;
    esac
    
    log_success "Script completed successfully!"
}

# Run main function with all arguments
main "$@"
