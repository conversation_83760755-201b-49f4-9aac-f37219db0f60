# Git Setup Instructions for Caramel CRM

## 1. Configure Git User (Required)

Set up your Git identity:
```bash
git config user.name "Your Name"
git config user.email "<EMAIL>"
```

Or set globally:
```bash
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"
```

## 2. Create Initial Commit

```bash
git commit -m "Initial commit: Caramel CRM foundation

- Laravel 11 application setup with authentication
- Docker containerization with PHP 8.4-FPM
- Complete development environment with MariaDB and Redis
- Supervisor configuration for process management
- Laravel Reverb for real-time features
- Queue system setup
- Comprehensive Docker documentation and build scripts
- Project README with Romanian market focus"
```

## 3. Create and Push to Remote Repository

### Option A: GitHub
```bash
# Create repository on GitHub first, then:
git remote add origin https://github.com/yourusername/caramel.git
git push -u origin main
```

### Option B: GitLab
```bash
# Create repository on GitLab first, then:
git remote add origin https://gitlab.com/yourusername/caramel.git
git push -u origin main
```

### Option C: Other Git Provider
```bash
git remote add origin <your-repository-url>
git push -u origin main
```

## 4. Create Development Branch

```bash
# Create and switch to dev branch
git checkout -b dev

# Push dev branch to remote
git push -u origin dev
```

## 5. Branch Strategy

- **`main`**: Production-ready code only
- **`dev`**: Active development branch
- **`feature/feature-name`**: Individual features
- **`hotfix/issue-name`**: Critical fixes

## 6. Development Workflow

```bash
# Start working on a new feature
git checkout dev
git pull origin dev
git checkout -b feature/contact-management

# Work on your feature...
git add .
git commit -m "Add contact management feature"

# Push feature branch
git push -u origin feature/contact-management

# Create pull request to merge into dev
# After review and merge, delete feature branch
git checkout dev
git pull origin dev
git branch -d feature/contact-management
```

## 7. Useful Git Commands

```bash
# Check current status
git status

# View commit history
git log --oneline

# Switch branches
git checkout branch-name

# Create new branch
git checkout -b new-branch-name

# Merge branch
git merge branch-name

# Push changes
git push origin branch-name

# Pull latest changes
git pull origin branch-name
```

## 8. Repository Settings

### Recommended GitHub/GitLab Settings:
- **Visibility**: Private
- **Default branch**: `main`
- **Branch protection**: Enable for `main` branch
- **Require pull request reviews**: Enable
- **Require status checks**: Enable (if CI/CD is set up)
- **Delete head branches**: Enable (auto-delete merged branches)

## 9. Next Steps

After setting up the repository:
1. Set up your development environment
2. Switch to `dev` branch for active development
3. Start building CRM features
4. Create feature branches for individual components
5. Use pull requests for code review and merging
