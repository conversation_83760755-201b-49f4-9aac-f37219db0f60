# Nginx Configuration for Laravel/Filament Development

## Key Principles for Minimal Interference

### 1. **Let <PERSON><PERSON> Handle Security Headers**
- Remove CSP headers from nginx
- Remove X-Frame-Options from nginx  
- Remove X-XSS-Protection (deprecated anyway)
- Let <PERSON><PERSON> middleware control these headers

### 2. **Optimize for JavaScript Frameworks**
- Don't block `'unsafe-eval'` (needed for Alpine.js)
- Don't block `'unsafe-inline'` (needed for dynamic styles)
- Allow WebSocket connections for Reverb

### 3. **Increase Buffer Sizes**
- Filament generates large HTML responses
- Increase fastcgi buffers to prevent 502 errors
- Increase timeouts for complex operations

### 4. **Files to Use**

#### Production (Secure)
Use: `.misc/nginx-snippet` (updated with minimal headers)

#### Development (Permissive)  
Use: `.misc/nginx-minimal.conf` (very permissive)

### 5. **<PERSON>vel Configuration**

#### Enable Filament CSP Middleware
The `FilamentContentSecurityPolicy` middleware only applies to `/admin*` routes and is very permissive during development.

#### Environment Variables
```env
# Development settings
APP_DEBUG=true
LOG_LEVEL=debug

# Session settings for better compatibility
SESSION_DRIVER=redis
SESSION_LIFETIME=120
SESSION_SECURE_COOKIE=true
SESSION_SAME_SITE=lax

# Disable strict CSP in development
FILAMENT_STRICT_CSP=false
```

### 6. **Testing the Configuration**

1. **Check CSP Headers**:
   ```bash
   curl -I https://crm.concept-42.com/admin/login
   ```

2. **Test JavaScript Console**:
   - No Alpine.js errors
   - No CSP violations
   - Filament components work properly

3. **Test Login Flow**:
   - No "?" appended to URL
   - Proper form submission
   - Session persistence

### 7. **Common Issues & Solutions**

| Issue | Cause | Solution |
|-------|-------|----------|
| Alpine.js errors | CSP blocks `unsafe-eval` | Use minimal nginx config |
| Form submission fails | CSRF/Session issues | Check session driver |
| 502 Bad Gateway | Small fastcgi buffers | Increase buffer sizes |
| WebSocket fails | Proxy config | Check Reverb proxy settings |

### 8. **Recommended Nginx Config Switch**

For development, replace your current nginx config with:
```bash
# Use the minimal config
cp .misc/nginx-minimal.conf /path/to/nginx/sites-available/crm.conf
nginx -t && systemctl reload nginx
```

This removes all potential conflicts and lets Laravel handle everything.
