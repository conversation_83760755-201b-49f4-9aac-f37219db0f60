# Laravel CRM Docker Setup

This directory contains all the necessary files to containerize your Laravel CRM application with full functionality.

## Files Overview

- **`Dockerfile`** - Complete PHP 8.4-FPM container with all required extensions
- **`supervisorcrml12.conf`** - Supervisor configuration for managing processes
- **`php.ini`** - Optimized PHP configuration for Laravel
- **`docker-compose.example.yml`** - Complete stack example with MariaDB and Redis
- **`nginx-snippet`** - Nginx configuration for reverse proxy

## Features Included

### PHP Extensions
- ✅ **pdo_mysql** & **mysqli** - Database connectivity
- ✅ **intl** - Internationalization support
- ✅ **redis** - Redis cache/session support
- ✅ **bcmath** - Arbitrary precision mathematics
- ✅ **gd** - Image processing
- ✅ **zip** - Archive handling
- ✅ **opcache** - Performance optimization
- ✅ **sockets** - Network communication
- ✅ All standard Laravel requirements

### Managed Processes (via Supervisor)
- ✅ **PHP-FPM** - Web server process
- ✅ **Laravel Queue Worker** - Background job processing (2 workers)
- ✅ **Laravel Scheduler** - Cron job management
- ✅ **<PERSON><PERSON> Reverb** - WebSocket server for real-time features
- ✅ **Redis Server** - Optional in-container Redis

## Quick Start

### 1. Build the Container
```bash
docker build -f .misc/Dockerfile -t crmlxii:latest .
```

### 2. Run with Docker Compose
```bash
# Copy the example compose file
cp .misc/docker-compose.example.yml docker-compose.yml

# Edit environment variables
nano docker-compose.yml

# Start the stack
docker-compose up -d
```

### 3. Initialize Laravel
```bash
# Run migrations
docker-compose exec crmlxii php artisan migrate

# Seed database
docker-compose exec crmlxii php artisan db:seed

# Generate app key
docker-compose exec crmlxii php artisan key:generate
```

## Environment Variables

### Required Laravel Variables
```env
APP_ENV=production
APP_DEBUG=false
APP_KEY=base64:your_app_key_here
APP_URL=https://your-domain.com

DB_CONNECTION=mariadb
DB_HOST=mariadb
DB_PORT=3306
DB_DATABASE=crmlxii_prod
DB_USERNAME=crmlxii_user
DB_PASSWORD=secure_password_here

REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=redis_password_here

CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis
```

### Reverb Configuration
```env
BROADCAST_DRIVER=reverb
REVERB_APP_ID=your_app_id
REVERB_APP_KEY=your_app_key
REVERB_APP_SECRET=your_app_secret
REVERB_HOST=localhost
REVERB_PORT=8080
REVERB_SCHEME=http
```

## Process Management

### Supervisor Commands
```bash
# Check status
docker-compose exec crmlxii supervisorctl status

# Restart a process
docker-compose exec crmlxii supervisorctl restart laravel-queue

# View logs
docker-compose exec crmlxii supervisorctl tail -f laravel-queue
```

### Enable/Disable Redis Server
The Redis server process is disabled by default. To enable:
```bash
docker-compose exec crmlxii supervisorctl start redis-server
```

## Performance Optimization

### OPcache Settings
- Memory: 256MB
- Max files: 20,000
- Revalidation: 2 seconds

### Queue Workers
- 2 parallel workers
- 512MB memory limit
- 3600 seconds max execution time
- Auto-restart on failure

## Security Considerations

1. **Change default passwords** in docker-compose.yml
2. **Use environment files** for sensitive data
3. **Enable SSL/TLS** in production
4. **Restrict network access** as needed
5. **Regular security updates** for base images

## Troubleshooting

### Common Issues
1. **Permission errors**: Ensure www-data owns Laravel directories
2. **Database connection**: Check DB_HOST matches service name
3. **Redis connection**: Verify REDIS_HOST and credentials
4. **Queue not processing**: Check supervisor logs

### Useful Commands
```bash
# View all logs
docker-compose logs -f

# Access container shell
docker-compose exec crmlxii bash

# Check PHP extensions
docker-compose exec crmlxii php -m

# Test database connection
docker-compose exec crmlxii php artisan db:show
```
