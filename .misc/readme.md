# Caramel CRM

A streamlined and configurable Customer Relationship Management system designed specifically for the Romanian market, starting with niche business sectors.

## 🎯 Vision

Caramel CRM aims to provide Romanian businesses with a modern, intuitive, and culturally-adapted CRM solution that understands local business practices, regulations, and market needs.

## 🚀 Project Status

**Current Phase**: Foundation & Infrastructure Setup
- ✅ Laravel 12 application framework
- ✅ Docker containerization with PHP 8.4
- ✅ Database setup (MariaDB)
- ✅ Redis caching and session management
- ✅ Laravel Reverb for real-time features
- ✅ Queue system for background processing
- ✅ Supervisor process management

## 🎯 Target Market

**Primary Focus**: Romanian niche businesses requiring:
- Customer relationship management
- Order production and manufacturing and pipeline tracking
- Romanian-specific compliance features
- Local language support
- Integration with Romanian business tools

## 🛠️ Technology Stack

- **Backend**: Laravel 11 (PHP 8.4)
- **Database**: MariaDB 11.7
- **Cache/Sessions**: Redis 7
- **Real-time**: <PERSON><PERSON> Reverb (WebSockets)
- **Queue System**: Redis-based queues
- **Containerization**: Docker with Supervisor
- **Frontend**: TBD (likely Vue.js or Livewire)

## 📋 Planned Features

### Core CRM Features
- [ ] Contact management
- [ ] Company/organization tracking
- [ ] Sales pipeline management
- [ ] Activity logging and history
- [ ] Task and appointment scheduling
- [ ] Document management
- [ ] Reporting and analytics

### Romanian Market Specific
- [ ] Romanian language interface
- [ ] CUI (Cod Unic de Înregistrare) validation
- [ ] Romanian address formatting
- [ ] Integration with ANAF (Romanian tax authority)
- [ ] Romanian business document templates
- [ ] Local currency and tax calculations
- [ ] Romanian holiday calendar integration

### Technical Features
- [ ] Role-based access control
- [ ] API for integrations
- [ ] Mobile-responsive design
- [ ] Data export/import capabilities
- [ ] Backup and restore functionality

## 🏗️ Development Setup

### Prerequisites
- Docker and Docker Compose
- Git

### Quick Start
```bash
# Clone the repository
git clone <repository-url>
cd caramel

# Copy environment configuration
cp .misc/.env.docker.example .env

# Build and start the application
.misc/build.sh build-and-test
docker-compose up -d

# Initialize the database
docker-compose exec crmlxii php artisan migrate
docker-compose exec crmlxii php artisan db:seed
```

### Development Workflow
- **Main branch**: Production-ready code
- **Dev branch**: Active development
- **Feature branches**: Individual features (feature/feature-name)

## 📁 Project Structure

```
caramel/
├── .misc/                  # Docker and deployment configurations
│   ├── Dockerfile         # Application container
│   ├── supervisorcrml12.conf  # Process management
│   ├── php.ini           # PHP configuration
│   └── build.sh          # Build automation
├── app/                   # Laravel application code
├── database/              # Migrations, seeders, factories
├── resources/             # Views, assets, language files
├── public/                # Web-accessible files
└── docker-compose.yml     # Development stack
```

## 🤝 Contributing

This is currently a private project in early development. Contribution guidelines will be established as the project matures.

## 📄 License

Private project - All rights reserved.

## 📞 Contact

For inquiries about this project, please contact the development team.

---

**Note**: This project is in active development. Features and documentation will be updated regularly as development progresses.
