# Laravel CRM Docker Environment Configuration
# Copy this file to .env and customize the values

# Application
APP_NAME="Laravel CRM"
APP_ENV=production
APP_KEY=base64:CHANGE_THIS_TO_YOUR_GENERATED_KEY
APP_DEBUG=false
APP_TIMEZONE=UTC
APP_URL=https://your-domain.com

# Database Configuration
DB_CONNECTION=mariadb
DB_HOST=mariadb
DB_PORT=3306
DB_DATABASE=crmlxii_prod
DB_USERNAME=crmlxii_user
DB_PASSWORD=CHANGE_THIS_SECURE_PASSWORD

# Redis Configuration
REDIS_HOST=redis
REDIS_PASSWORD=CHANGE_THIS_REDIS_PASSWORD
REDIS_PORT=6379
REDIS_DB=0

# Cache Configuration
CACHE_STORE=redis
CACHE_PREFIX=crmlxii_cache

# Session Configuration
SESSION_DRIVER=redis
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

# Queue Configuration
QUEUE_CONNECTION=redis
QUEUE_FAILED_DRIVER=database-uuids

# Broadcasting / WebSockets (Laravel Reverb)
BROADCAST_CONNECTION=reverb
REVERB_APP_ID=your_app_id_here
REVERB_APP_KEY=your_app_key_here
REVERB_APP_SECRET=your_app_secret_here
REVERB_HOST=localhost
REVERB_PORT=8080
REVERB_SCHEME=http

# Mail Configuration
MAIL_MAILER=smtp
MAIL_HOST=smtp.mailtrap.io
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# Logging
LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=error

# Security
BCRYPT_ROUNDS=12

# File Storage
FILESYSTEM_DISK=local

# Additional CRM-specific configurations
CRM_TIMEZONE=UTC
CRM_CURRENCY=USD
CRM_DATE_FORMAT="Y-m-d"
CRM_TIME_FORMAT="H:i:s"

# Performance Settings
OCTANE_SERVER=swoole
SCOUT_DRIVER=meilisearch

# Development/Debug (set to false in production)
TELESCOPE_ENABLED=false
DEBUGBAR_ENABLED=false
