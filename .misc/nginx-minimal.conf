##### CRM L12 - Minimal Nginx Configuration
# This config minimizes nginx interference with Laravel/Filament

server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name crm.concept-42.com;
    root /var/www/crmlxii/public;
    index index.php;

    # SSL configuration
    ssl_certificate /etc/letsencrypt/live/crm.concept-42.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/crm.concept-42.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers off;

    # MINIMAL security headers - let <PERSON><PERSON> handle the rest
    add_header Strict-Transport-Security "max-age=********" always;
    
    # NO CSP, X-Frame-Options, etc. - <PERSON>vel middleware handles these

    # Basic compression
    gzip on;
    gzip_vary on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    # Laravel routing - let <PERSON><PERSON> handle everything
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # PHP processing - minimal interference
    location ~ \.php$ {
        fastcgi_pass **********:9000;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
        
        # Increased buffers for Filament's large responses
        fastcgi_buffer_size 128k;
        fastcgi_buffers 8 256k;
        fastcgi_busy_buffers_size 256k;
        fastcgi_read_timeout 300;
        fastcgi_send_timeout 300;
        
        # Let Laravel handle all headers
    }

    # Laravel Reverb WebSocket proxy
    location /reverb {
        proxy_http_version 1.1;
        proxy_set_header Host $http_host;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "Upgrade";
        proxy_pass http://**********:8080;
    }

    # Static files - basic caching only
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri =404;
    }

    # Basic security - deny hidden files
    location ~ /\. {
        deny all;
    }
}

# HTTP to HTTPS redirect
server {
    listen 80;
    listen [::]:80;
    server_name crm.concept-42.com;
    return 301 https://$server_name$request_uri;
}
