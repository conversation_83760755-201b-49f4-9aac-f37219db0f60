[supervisord]
nodaemon=true
user=root
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid
childlogdir=/var/log/supervisor

[unix_http_server]
file=/var/run/supervisor.sock
chmod=0700

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[supervisorctl]
serverurl=unix:///var/run/supervisor.sock

# PHP-FPM Process
[program:php-fpm]
command=php-fpm --nodaemonize --force-stderr
directory=/var/www/crmlxii
user=root
autostart=true
autorestart=true
priority=5
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
startsecs=0
stopwaitsecs=3600
killasgroup=true
stopasgroup=true

# Laravel Reverb WebSocket Server
[program:laravel-reverb]
command=php artisan reverb:start --host=0.0.0.0 --port=8080 --debug
directory=/var/www/crmlxii
user=www-data
autostart=true
autorestart=true
priority=10
startsecs=3
stdout_logfile=/var/log/supervisor/laravel-reverb.log
stderr_logfile=/var/log/supervisor/laravel-reverb-error.log
environment=LARAVEL_REVERB_SERVER_HOST="0.0.0.0",LARAVEL_REVERB_SERVER_PORT="8080"

# Laravel Queue Worker
[program:laravel-queue]
command=php artisan queue:work --sleep=3 --tries=3 --max-time=3600 --memory=512
directory=/var/www/crmlxii
user=www-data
autostart=true
autorestart=true
priority=20
numprocs=2
process_name=%(program_name)s_%(process_num)02d
stdout_logfile=/var/log/supervisor/laravel-queue.log
stderr_logfile=/var/log/supervisor/laravel-queue-error.log
stopwaitsecs=3600
killasgroup=true
stopasgroup=true

# Laravel Scheduler
[program:laravel-schedule]
command=php artisan schedule:work
directory=/var/www/crmlxii
user=www-data
autostart=true
autorestart=true
priority=25
stdout_logfile=/var/log/supervisor/laravel-schedule.log
stderr_logfile=/var/log/supervisor/laravel-schedule-error.log
stopwaitsecs=60

# Redis Server (optional - if you want Redis in the same container)
[program:redis-server]
command=redis-server --daemonize no --bind 127.0.0.1 --port 6379
user=redis
autostart=false
autorestart=true
priority=1
stdout_logfile=/var/log/supervisor/redis.log
stderr_logfile=/var/log/supervisor/redis-error.log
