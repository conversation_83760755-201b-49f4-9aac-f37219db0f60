@php

$userData = $getState();
$userName = $userData['name'];
$userId = $userData['id'];

    

    $initials = '';
    $nameParts = explode(' ', $userName);
    foreach ($nameParts as $part) {
        if (strlen($initials) < 2) {
            $initials .= strtoupper(substr($part, 0, 1));
        } else {
            break;
        }
    }

    if ($userId !== null) {
        $index = $userId % 9; // Adjust based on the number of colors
        $assignedClass = 'user-color-' . $index .' avatar-size';

    } else {
        $assignedClass = 'user-color-default';
    }

@endphp

<div class="rounded-full {{ $assignedClass }} w-12 h-12 flex items-center justify-center text-white font-bold">
   <span class='text-xs'> {{ $initials }} </span>
</div>