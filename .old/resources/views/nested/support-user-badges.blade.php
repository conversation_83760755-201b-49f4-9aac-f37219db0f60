@php
    $supportUsers = $getRecord()->supportUsers;
@endphp

@if($supportUsers->isNotEmpty())
    <div class="w-full flex flex-col justify-center align-middle items-center space-x-1 mx-4">
        @foreach($supportUsers as $user)
            @php
                // Generate initials from user name
                $initials = collect(explode(' ', $user->name))
                    ->map(fn($namePart) => strtoupper(substr($namePart, 0, 1)))
                    ->take(2)
                    ->join('');
    

                    // Assign a color based on user ID
                    $index = $user->id % 5; // Adjust based on the number of colors
        $assignedClass = 'user-color-' . $index .' support-avatar-size';
        @endphp
        <div class="relative flex justify-center align-middle items-center">
                <div class="
                    rounded-full
                    {{ $assignedClass }}
                    w-6 h-6
                    flex items-center justify-center
                    text-white font-bold
                ">
                    <span class="text-xs">{{ $initials }}</span>
                </div>
            </div>
        @endforeach
        </div>
@else
    <span class="text-gray-500 w-100 mx-4 px-4 text-xs text-center">N/A</span>
@endif