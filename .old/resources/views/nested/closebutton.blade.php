@props(['fieldPath']) <!-- Receive the fieldPath from viewData -->
@php 

$currentPath = $getStatePath();
$excludePath = preg_replace('/\.exclude_button$/', '.excluded', $currentPath);
// dd($fieldPath); @endphp
<button
    type="btn btn-circle"
   wire:click.prevent="$set('{{ $excludePath }}', true)"
title="Exclude Substage">
  <svg
    xmlns="http://www.w3.org/2000/svg"
    class="h-3 w-3"
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor">
    <path
      stroke-linecap="round"
      stroke-linejoin="round"
      stroke-width="2"
      d="M6 18L18 6M6 6l12 12" />
  </svg>
  {{ $excludePath }}
</button>
