<div>

@php

$task = $getRecord();
    $currentStep = $task->stage_id;

    // Retrieve all active substages for this stage
    $substagesForThisStage = $task->activeSubstages->filter(fn($substage) => $substage->stage_id == $stepNumber);

    // Determine completion status
    $allComplete = $substagesForThisStage->every(fn($s) => $s->pivot->is_complete);
    $anyIncomplete = $substagesForThisStage->contains(fn($s) => !$s->pivot->is_complete);

    // Decide which icon to show
    $icon = null;
    $iconClasses = '';
    
    if ($substagesForThisStage->isEmpty()) {
        // No active_substages for this stage
        // You could leave $icon as null or show a neutral icon
        $icon = null; 
    } else {
        if ($allComplete) {
            // All substages complete
            $icon = 'heroicon-o-check-circle';
            $iconClasses .= 'alldone';
        } else {
            // Some are incomplete
            if ($currentStep > $stepNumber) {
                // We've passed this stage, but it's incomplete
                $icon = 'heroicon-o-exclamation-triangle';
                $iconClasses .= 'incomplete';
            } else {
                // Not yet reached or currently at this stage, but incomplete
                // Choose a neutral "in-progress" or "not done" icon
                $icon = null;
            }
        }
    }




@endphp
</div>
<div class="flex relative">
        <div class="relative flex flex-row justify-center items-center">
                <!-- Circle -->
                <div class="
        flex items-center justify-center 
        w-10 h-10 rounded-full 
        {{ $currentStep >= $stepNumber ? 'step-active' : 'step-inactive' }}
        text-white text-xs">
 
            @if($icon)
                <x-dynamic-component :component="$icon" class="w-4 h-4 {{$iconClasses}}"/>
            @endif
        
       
               </div>
         
            <!-- Line -->
            @unless($stepNumber == 7)
                <div class="
                    transform 
                    h-3 w-20 
                    {{ $currentStep > $stepNumber ? 'line-active' : 'line-inactive' }}
                "></div>
            @endunless
        </div>

</div>
<!-- 
<div>
<ul aria-label="Steps" role="group" class="steps overflow-auto">
    <li aria-label="Step" class="step step-success">Ofertare</li>
    <li aria-label="Step" class="step step-primary">Contractare</li>
    <li aria-label="Step" class="step">Pregătire</li>
    <li aria-label="Step" class="step">Execuție</li>
    <li aria-label="Step" class="step">Montaj</li>
    <li aria-label="Step" class="step">Finalizare</li>
</ul>
</div> -->