
@php
    $id = $getState();

    $icon = '';

    switch ($id->channel) {
        case 'Channel1':
            $icon = 'heroicon-o-chat-bubble-oval-left-ellipsis';
            break;
        case 'Channel2':
            $icon = 'heroicon-o-envelope';
            break;
        case 'Channel3':
            $icon = 'heroicon-o-phone';
            break;
    }

    $formattedDate = \Carbon\Carbon::parse($id->arrivedAt)->format('d.m.Y, H:i');
@endphp



 
<div class="flex row text-xs gap-2">
    @if(blank($id))
        <x-filament-tables::columns.placeholder>
            Not Rated
        </x-filament-tables::columns.placeholder>
    @else
       
        <x-dynamic-component :component="$icon" class="w-4 h-4 mr-2" /> {{ $id->clientName }},  {{  $formattedDate }}
       
    @endif
  
</div>
