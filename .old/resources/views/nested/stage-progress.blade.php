@php
    $record = $record ?? null;
@endphp

<div class="mb-4">
    <h1 class="text-2xl font-bold mb-2">{{ $record->name }}</h1>

    {{-- This partial should display a horizontal step tracker --}}
    {{-- Example: --}}
    <div class="flex space-x-4 items-center">
        @php
            $currentStep = $record->stage_id;
            $stages = \App\Models\Stage::all(); // or pass this via viewData if needed
        @endphp

        @foreach($stages as $index => $stage)
            {{-- Replace the logic below with your previously discussed icon and styling logic --}}
            @php
                $stepNumber = $stage->id;
                $isActive = $currentStep >= $stepNumber;
            @endphp
            <div class="flex items-center">
                <div class="w-10 h-10 rounded-full flex items-center justify-center {{ $isActive ? 'bg-green-500' : 'bg-gray-300' }}">
                    {{ $stepNumber }}
                </div>
                @if(!$loop->last)
                    <div class="h-1 w-10 {{ $currentStep > $stepNumber ? 'bg-green-500' : 'bg-gray-300' }}"></div>
                @endif
            </div>
        @endforeach
    </div>
</div>