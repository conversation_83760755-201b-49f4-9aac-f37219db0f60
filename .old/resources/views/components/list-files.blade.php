
@php
use Illuminate\Support\Facades\Storage;
    use <PERSON><PERSON>\LaravelGoogleDriveStorage\Gdrive;
    use Illuminate\Support\Facades\Log;

    $files = [];
    $directory = "CRMcrm/tasks/{$recordId}";

    try {
        $files = Gdrive::all($directory, false);
    } catch (\Exception $e) {
        Log::error('Error listing Google Drive files: ' . $e->getMessage());
    }
@endphp


@if (empty($files))
    <p>Nu sunt fișiere atașate.</p>
@else
<h3 class="text-sm font-medium text-gray-700 dark:text-white">Fișiere existente:</h3>
    <ul id="lista-fisiere-editor" class="list-inside text-xs mt-6">
        @foreach ($files as $file)
          @php  Log::info('File: ' . print_r($file, true)); @endphp
            <li class='py-2 border-b-2 border-solid border-gray-600 dark:border-gray-800'>
                <a href="https://drive.google.com/file/d/{{ $file['extraMetadata']['id'] }}/view" target="_blank">
                    {{ $file['extraMetadata']['name']  }}
                </a>
            </li>
        @endforeach
    </ul>
@endif