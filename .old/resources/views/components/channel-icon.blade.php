@php
    switch ($channel) {
        case 'Channel1':
            $icon = 'heroicon-o-envelope';
            break;
        case 'Channel2':
            $icon = 'heroicon-o-phone';
            break;
        case 'Channel3':
            $icon = 'heroicon-o-chat-bubble-oval-left-ellipsis';
            break;
        default:
            $icon = null;  
        }

      //  dd($channel);
@endphp
@if($icon)
    
    <x-dynamic-component :component="$icon" class="w-6 h-6 text-gray-700" />
@endif