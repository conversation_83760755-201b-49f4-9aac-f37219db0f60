@import '/vendor/filament/filament/resources/css/theme.css';


@config 'tailwind.config.js';


.step-active {
  @apply bg-amber-500 z-[3] h-8 w-8 text-[10px];
}

.step-inactive {
  @apply bg-gray-300 z-[3] h-8 w-8 text-[10px];
}

.line-active {
  @apply bg-amber-500 z-0 w-20 h-2 -m-3;
}

.line-inactive {
  @apply bg-gray-300 z-0 w-20 h-2 -m-3;
}

.step-label {
  width: 8rem;
  /* Adjust as needed */
  text-align: center;
}

[class*="fi-table-header-cell-stage"] {
  @apply overflow-hidden max-w-20 pl-0 pr-5 text-left hover:overflow-visible;
}

.fi-ta-header-cell-label {
  @apply text-xs;
}

.fi-table-cell-identifier-record {
  @apply overflow-hidden pr-5 text-left hover:overflow-visible;
}

.fi-table-cell-name {
  @apply overflow-clip max-w-96 mr-4 text-left hover:overflow-visible pr-10;
  @apply pr-4;
  /* Add padding-right to create space */
}

.fi-table-cell-name .fi-ta-text-item .fi-ta-text-item-label {
  @apply text-xs overflow-clip max-w-80
}

.fi-table-cell-deadline .fi-ta-text-item-label {
  @apply text-xs pl-0 pr-5 text-left;
}

.fi-table-cell-id .fi-ta-text-item-label {
  @apply text-xs text-left;
}

.fi-ac-action:disabled {
  @apply text-gray-700;
}

.fi-section-content {
  @apply p-4;
}

.fi-section,
.fi-ta-ctn,
.fi-pagination-items,
.fi-input-wrp,
.fi-dropdown-panel,
.fi-modal-window,
.fi-fo-repeater-item,
.fi-fo-wizard {
  @apply rounded-sm;
}

.fi-fo-wizard-header-step-icon-ctn {
  @apply hidden;
}

.alldone {
  @apply text-yellow-100 opacity-25;
}

.incomplete {
  @apply text-red-900;
}

.toggle-xs {
  --handleoffset: .5rem;
  height: 1rem;
  width: 1.5rem;
}


.fi-fo-wizard .fi-fo-toggle {}

.fi-btn {
  @apply rounded-md;
}

.fi-fo-field-wrp-label span,
.fi-fo-placeholder,
.fi-input,
.fi-select-input,
.fi-fo-select option,
.fi-input-wrp,
{
@apply text-xs;
}

.fi-fo-field-wrp-label span {
  @apply opacity-50;
}

.fi-ac-action:disabled svg {

  /* Tailwind's gray-700 color */
  stroke: #6a717a55;
  /* Tailwind's gray-700 color */
}



.fi-ac-link-action.pointer-events-none svg {

  /* Tailwind's gray-700 color */
  stroke: #6a717a55;
  /* Tailwind's gray-700 color */
}


.user-color-0 {
  @apply bg-red-500;
}

.user-color-1 {
  @apply bg-green-500;
}

.user-color-2 {
  @apply bg-blue-500;
}

.user-color-3 {
  @apply bg-yellow-500;
}

.user-color-4 {
  @apply bg-purple-500;
}

.user-color-5 {
  @apply bg-red-800;
}

.user-color-6 {
  @apply bg-green-800;
}

.user-color-7 {
  @apply bg-blue-800;
}

.user-color-8 {
  @apply bg-yellow-800;
}

.user-color-9 {
  @apply bg-purple-800;
}

.user-color-default {
  @apply bg-slate-800;
}

.avatar-size {
  @apply w-10 h-10;
}

label[for="data.name"] {
  @apply hidden;
}

#lista-fisiere-editor .li {
  @apply py-2 border-2 border-solid border-gray-200 dark:border-gray-800;
}