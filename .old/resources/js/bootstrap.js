import axios from 'axios';
window.axios = axios;

window.axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';

/**
 * Echo exposes an expressive API for subscribing to channels and listening
 * for events that are broadcast by Laravel. Echo and event broadcasting
 * allow your team to quickly build robust real-time web applications.
 */

import './echo';






// window.Echo.private(`user.${userId}`)
//     .listen('.simple.broadcast', (e) => {
//         console.log('Task event received:', e);
//         Filament.notification({
//             title: e.message,
//             body: `Task ID: ${e.taskId}`,
//             icon: 'heroicon-o-bell',
//             timeout: 5000,
//         });
//     });
document.addEventListener('DOMContentLoaded', function () {
    // const userID = window.userID;
    console.log('test-channel');
    window.Echo.channel('test-channel')
        .listen('.simple.broadcast', (e) => {
            console.log(e);
            // showNotification(response);
        });
});
