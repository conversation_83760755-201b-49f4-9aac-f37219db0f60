<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up()
    {
        Schema::create('identifiers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('project_id')->constrained('projects')->onDelete('cascade');
            $table->enum('channel', ['Channel1', 'Channel2', 'Channel3']); // Replace with actual enum values
            $table->string('clientName');
            $table->dateTime('arrivedAt');
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('identifiers');
    }
};