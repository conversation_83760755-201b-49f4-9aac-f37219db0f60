<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::table('tasks', function (Blueprint $table) {
            $table->boolean('is_paid')->default(false);
            $table->boolean('fast_track')->default(false);
            $table->softDeletes();
        });
    }

    public function down()
    {
        Schema::table('tasks', function (Blueprint $table) {
            $table->dropColumn('is_paid');
            $table->dropColumn('fast_track');
            $table->dropSoftDeletes();
        });
    }
};
