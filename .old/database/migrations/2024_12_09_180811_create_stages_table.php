<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up()
    {
        Schema::create('stages', function (Blueprint $table) {
            $table->id();
            $table->enum('name', [
                'Ofertare',
                'Contractare',
                'Pregatire',
                'Aprovizionare',
                'Executie',
                'Montaj',
                'Finalizare',
            ]);
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('stages');
    }
};
