<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        // Update projects table
        Schema::table('projects', function (Blueprint $table) {
            // Remove identifier, assigned_to, and deadline from projects
            // assuming they were there in the original schema
            // Drop foreign key constraints first
    $table->dropForeign(['assigned_to']);
            $table->dropColumn(['identifier', 'assigned_to', 'deadline']);
            
            // Add the boolean field for project-level billing
            $table->boolean('project_level_billing')->default(true)->nullable();
        });

        // Update tasks table
        Schema::table('tasks', function (Blueprint $table) {
            // Make project_id nullable, so tasks can stand alone
            $table->unsignedBigInteger('project_id')->nullable()->change();

            // Add identifier and deadline fields to tasks
            $table->string('identifier')->nullable()->after('added_by');
            $table->dateTime('deadline')->nullable()->after('identifier');
        });

        // Update identifiers table (if keeping the Identifier model)
        Schema::table('identifiers', function (Blueprint $table) {
            // Drop the project_id foreign key and column
            $table->dropForeign(['project_id']);
            $table->dropColumn('project_id');

            // Add task_id as the new foreign key
            $table->foreignId('task_id')->nullable()->constrained('tasks')->onDelete('cascade');
        });
    }

    public function down()
    {
        // Rollback steps if needed
        Schema::table('identifiers', function (Blueprint $table) {
            $table->dropForeign(['task_id']);
            $table->dropColumn('task_id');
            $table->foreignId('project_id')->constrained('projects')->onDelete('cascade');
        });

        Schema::table('tasks', function (Blueprint $table) {
            $table->unsignedBigInteger('project_id')->nullable(false)->change();
            $table->dropColumn(['identifier', 'deadline']);
        });

        Schema::table('projects', function (Blueprint $table) {
            $table->dropColumn('project_level_billing');
            // Re-add the original columns if needed
            $table->string('identifier');
            $table->foreignId('assigned_to')->constrained('users')->onDelete('cascade');
            $table->dateTime('deadline')->nullable();
        });
    }
};
