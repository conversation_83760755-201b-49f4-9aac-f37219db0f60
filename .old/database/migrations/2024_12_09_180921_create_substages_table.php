<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up()
    {
        Schema::create('substages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('stage_id')->constrained('stages')->onDelete('cascade');
            $table->string('name');
            $table->boolean('isComplete')->default(false);
            $table->unsignedBigInteger('value')->nullable(); // Adjust type based on usage
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('substages');
    }
};
