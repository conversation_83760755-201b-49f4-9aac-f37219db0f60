<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up()
    {
        Schema::create('active_substages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('task_id')->constrained('tasks')->onDelete('cascade');
            $table->foreignId('substage_id')->constrained('substages')->onDelete('cascade');
            $table->timestamps();

          //  $table->unique(['task_id', 'substage_id']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('active_substages');
    }
};