<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::create('clients', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('tax_id')->nullable()->default(null);
            $table->string('country')->nullable()->default(null);
            $table->string('county')->nullable()->default(null);
            $table->string('city')->nullable()->default(null);
            $table->string('address')->nullable()->default(null);
            $table->string('iban')->nullable()->default(null);
            $table->string('bank')->nullable()->default(null);
            $table->string('phone_legal')->nullable()->default(null);
            $table->string('phone_work01')->nullable()->default(null);
            $table->string('email')->nullable()->default(null);
            $table->string('roc_id')->nullable()->default(null);
            $table->string('contact_legal')->nullable()->default(null);
            $table->string('contact_work01')->nullable()->default(null);
            $table->string('id_series')->nullable()->default(null);
            $table->string('id_number')->nullable()->default(null);
            $table->string('id_issuer')->nullable()->default(null);
            $table->string('vehicle_plate')->nullable()->default(null);
            $table->string('postal_code')->nullable()->default(null);
            $table->decimal('discount_rate', 5, 2)->nullable()->default(null);
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('clients');
    }
};
