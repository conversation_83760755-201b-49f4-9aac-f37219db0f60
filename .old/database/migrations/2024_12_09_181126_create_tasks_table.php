<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;


return new class extends Migration {
    public function up()
    {
        Schema::create('tasks', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->foreignId('project_id')->constrained('projects')->onDelete('cascade');
            $table->foreignId('assigned_to')->constrained('users')->onDelete('cascade');
            $table->foreignId('added_by')->constrained('users')->onDelete('cascade');
            $table->string('name')->default('Editează asta!');
            $table->text('description')->nullable();
            $table->foreignId('stage_id')->nullable()->constrained('stages')->onDelete('set null');
            $table->longText('content')->nullable();
            $table->decimal('price', 15, 2)->nullable();

            $table->foreignId('support01')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('support02')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('support03')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('support04')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('support05')->nullable()->constrained('users')->onDelete('set null');

            $table->text('reject_reason')->nullable();
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('tasks');
    }
};