<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Client;
use League\Csv\Reader;
use Illuminate\Support\Facades\Log;

class ClientSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Path to the CSV file
        $csvFile = storage_path('app/import.csv');

        // Read the CSV file
        $csv = Reader::createFromPath($csvFile, 'r');
        $csv->setHeaderOffset(0); // Set the header offset

        // Get the records
        $records = $csv->getRecords();

        // Loop through the records and create clients
        foreach ($records as $record) {
            try {
                Client::create([
                    'name' => $record['denumire'],
                    'tax_id' => $record['cod_fiscal'],
                    'country' => $record['tara'],
                    'county' => $record['judet'],
                    'city' => $record['localitate'],
                    'address' => $record['adresa'],
                    'iban' => $record['cont_banca'],
                    'bank' => $record['banca'],
                    'phone_legal' => $record['tel'],
                    'phone_work01' => null, // No correspondent in CSV
                    'email' => $record['email'],
                    'roc_id' => $record['reg_com'],
                    'contact_legal' => $record['delegat'],
                    'contact_work01' => null, // No correspondent in CSV
                    'id_series' => $record['bi_serie'],
                    'id_number' => $record['bi_numar'],
                    'id_issuer' => $record['bi_pol'],
                    'vehicle_plate' => $record['masina'],
                    'postal_code' => $record['cod_post'],
                    'discount_rate' => $record['discount'],
                ]);
            } catch (\Exception $e) {
                Log::error('Error importing client: ' . $e->getMessage());
            }
        }
    }
}