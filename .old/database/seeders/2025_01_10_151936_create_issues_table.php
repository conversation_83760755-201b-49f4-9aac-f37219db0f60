<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::disableForeignKeyConstraints();

        Schema::create('issues', function (Blueprint $table) {
            $table->id();
            $table->foreignId('task_id')->constrained();
            $table->text('description');
            $table->text('details')->nullable();
            $table->enum('stage', ["Ofertare","Contractare","Pregatire","Aprovizionare","Executie","Montaj","Finalizare"])->nullable();
            $table->enum('type', ["Viciu material","Eroare utilaj","Eroare operator","Eroare client"]);
            $table->enum('severity', ["redusă","medie","ridicată","critică"]);
            $table->set('losses', ["material","timp","transport", "montaj","accidentări"])->nullable();
            $table->enum('solution', ["De stabilit","Rambursare","Refacere parțială","Refacere totală"])->nullable();
            $table->decimal('approximate_value', 10, 2)->nullable();
            $table->timestamps();
            
        });

        Schema::table('tasks', function (Blueprint $table) {
            //$table->foreignId('client_id')->nullable()->constrained()->onDelete('set null');
            $table->boolean('low_priority')->nullable();
            $table->boolean('is_finished')->nullable();
            $table->softDeletes();
        });

        Schema::enableForeignKeyConstraints();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('issues');
    }
};
