<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use App\Models\Issue;
use App\Models\Task;

class IssueFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Issue::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'task_id' => Task::factory(),
            'description' => $this->faker->text(),
            'details' => $this->faker->text(),
            'stage' => $this->faker->randomElement(["Ofertare","Contractare","Pregatire","Aprovizionare","Executie","Montaj","Finalizare"]),
            'type' => $this->faker->randomElement(["Material_fault","Machine_fault","User_error","Client_error"]),
            'severity' => $this->faker->randomElement(["low","medium","high","critical"]),
            'losses' => $this->faker->randomElement(["material","time","shipping","injuries"]),
            'solution' => $this->faker->randomElement(["TBD","Refund","Partial_rework","Total_rework"]),
        ];
    }
}
