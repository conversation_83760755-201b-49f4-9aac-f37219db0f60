<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use App\Models\MaterialsOrder;
use App\Models\Task;

class MaterialsOrderFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = MaterialsOrder::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'task_id' => Task::factory(),
            'supplier' => $this->faker->word(),
            'material_description' => $this->faker->word(),
            'value' => $this->faker->randomFloat(2, 0, 99999999.99),
        ];
    }
}
