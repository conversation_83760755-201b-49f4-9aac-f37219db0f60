<?php

namespace Database\Factories;

use App\Models\Identifier;
use Illuminate\Database\Eloquent\Factories\Factory;

class IdentifierFactory extends Factory
{
    protected $model = Identifier::class;

    public function definition()
    {
        return [
            'task_id' => null,
            'channel' => $this->faker->randomElement(['Channel1', 'Channel2', 'Channel3']),
            'clientName' => $this->faker->name,
            'arrivedAt' => $this->faker->dateTimeBetween('-1 year', 'now'),
        ];
    }
}