<?php

namespace App\Filament\App\Widgets;

use App\Models\Meeting;
use App\Models\Installation;
use App\Models\FieldWork;
use App\Models\Task;
use App\Models\User;
use Filament\Actions\Action;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Infolist;
use Guava\Calendar\Widgets\CalendarWidget;
use Guava\Calendar\ValueObjects\Event;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class SchedulerCalendar extends CalendarWidget
{
    protected string $calendarView = 'dayGridMonth';
    protected bool $eventClickEnabled = true;    
    protected bool $dateClickEnabled = true;
    protected bool $dateSelectEnabled = true;   
    


    protected function getCalendarOptions(): array
    {
/*         return [
            'timeZone' => 'Europe/Bucharest',
            'slotMinTime' => '07:00:00',
            'slotMaxTime' => '22:00:00',
            'editable' => true,
            'selectable' => true,
            'initialView' => $this->calendarView,
            'eventTimeFormat' => [
                'hour' => '2-digit',
                'minute' => '2-digit',
                'hour12' => false
            ],
            'displayEventEnd' => true,
            'displayEventTime' => true,
            'firstDay' => 1, // Start week on Monday
            'defaultTimedEventDuration' => '01:00:00', // Default duration of 1 hour

        ]; */

        return [
            'timeZone' => 'Europe/Bucharest',
            'slotMinTime' => '07:00:00',
            'slotMaxTime' => '22:00:00',
            'editable' => true,
            'selectable' => true,
            'initialView' => $this->calendarView,
            'headerToolbar' => [
                'left' => 'prev,next today',
                'center' => 'title',
                'right' => 'dayGridMonth,timeGridWeek,timeGridDay,listWeek'
            ],
            'eventTimeFormat' => [
                'hour' => '2-digit',
                'minute' => '2-digit',
                'hour12' => false
            ],
            'displayEventEnd' => true,
            'displayEventTime' => true,
            'firstDay' => 1, // Start week on Monday
            'defaultTimedEventDuration' => '01:00:00', // Default duration of 1 hour
        ];
    }
    
    protected function getToolbarActions(): array
    {
        return [
            FormAction::make('viewMonth')
                ->label('Month')
                ->icon('heroicon-m-calendar')
                ->action(fn () => $this->setCalendarView('dayGridMonth')),
            
            FormAction::make('viewWeek')
                ->label('Week')
                ->icon('heroicon-m-calendar-days')
                ->action(fn () => $this->setCalendarView('timeGridWeek')),
            
            FormAction::make('viewDay')
                ->label('Day')
                ->icon('heroicon-m-calendar-days')
                ->action(fn () => $this->setCalendarView('timeGridDay')),
            
            FormAction::make('viewList')
                ->label('List')
                ->icon('heroicon-m-list-bullet')
                ->action(fn () => $this->setCalendarView('listWeek')),
        ];
    }

    public function setCalendarView(string $view): void
    {
        $this->calendarView = $view;
        $this->dispatch('calendar-view-changed', view: $view);
    }

    public function getEvents(array $fetchInfo = []): Collection|array
    {
        // Eager load relationships to avoid N+1 query problems
        $meetings = Meeting::with(['assignees', 'task'])->get();
        $installations = Installation::with(['assignees', 'task'])->get();
        $fieldWorks = FieldWork::with(['assignees', 'task'])->get();
        $tasks = Task::with('client')
        ->whereNotNull('deadline')
        ->where('is_finished', false)
        ->get();
        return collect()
            ->merge($meetings->map(fn($meeting) => $meeting->toEvent()))
            ->merge($installations->map(fn($installation) => $installation->toEvent()))
            ->merge($fieldWorks->map(fn($fieldWork) => $fieldWork->toEvent()))
            ->merge($tasks->map(fn($task) => $task->toEvent()));
    }
    
    public function getSchema(?string $model = null): array
    {
        // Get all users for the select options
        $users = User::pluck('name', 'id')->toArray();
        
        return match($model) {
            Meeting::class => [
                TextInput::make('title')->required(),
                DateTimePicker::make('start_time')
                ->required()
                ->seconds(false)
                ->displayFormat('Y-m-d H:i'),
            DateTimePicker::make('end_time')
                ->required()
                ->seconds(false)
                ->displayFormat('Y-m-d H:i'),
                Select::make('task_id')
                    ->relationship('task', 'name'),
                // Direct selection without relationships to debug
                Select::make('assignees')
                    ->multiple()
                    ->options($users)
                    ->required(),
                Textarea::make('details'),
            ],
            Installation::class => [
                TextInput::make('title')->required(),
                DateTimePicker::make('scheduled_date')->required(),
                Select::make('task_id')
                    ->relationship('task', 'name'),
                Select::make('assignees')
                    ->multiple()
                    ->options($users)
                    ->required(),
               Textarea::make('details'),
            ],
            FieldWork::class => [
                TextInput::make('title')->required(),
                \Filament\Forms\Components\DatePicker::make('start_date')->required(),
            // Use DatePicker instead of DateTimePicker to hide hours
                \Filament\Forms\Components\DatePicker::make('end_date')
                ->required(),
                Select::make('task_id')
                    ->relationship('task', 'name'),
                Select::make('assignees')
                    ->multiple()
                    ->options($users)
                    ->required(),
                Textarea::make('details'),
            ],
            default => []
        };
    }
    

    public function getInfoListSchema(?string $model = null): array
    {
        return match($model) {
            Task::class => [
                Section::make('Task Deadline')
                    ->schema([
                        TextEntry::make('name')
                            ->label('Task')
                            ->size('xl')
                            ->weight('bold'),
                        TextEntry::make('client.name')
                            ->label('Client'),
                        TextEntry::make('deadline')
                            ->label('Deadline')
                            ->date(),
                        TextEntry::make('owner.name')
                            ->label('Assigned to'),
                    ]),
            ],
            Meeting::class => [
                Section::make('Meeting Details')
                    ->schema([
                        TextEntry::make('title')
                            ->label('')
                            ->size('xl')
                            ->weight('bold'),
                        TextEntry::make('start_time')
                            ->label('Oră începere')
                            ->date('Y-m-d H:i'),
                        TextEntry::make('end_time')
                            ->label('Oră încheiere')
                            ->date('Y-m-d H:i'),
                        TextEntry::make('task.name')
                            ->label('Se referă la lucrarea:'),
                        TextEntry::make('assignees')
                            ->label('Participanti')
                           ->formatStateUsing(function ($state, $record) {
                                if ($record->relationLoaded('assignees')) {
                                    return $record->assignees->pluck('name')->join(', ');
                                }
                                return 'No assignees';
                            }),
                        TextEntry::make('details')
                            ->label('Detalii')
                            ->markdown(),
                        TextEntry::make('creator.name')
                            ->label('Adăugată de'),
                    ]),
            ],
            Installation::class => [
                Section::make('Installation Details')
                    ->schema([
                        TextEntry::make('title')
                            ->label('Title')
                            ->size('lg')
                            ->weight('bold'),
                        TextEntry::make('scheduled_date')
                            ->label('Date')
                            ->date('Y-m-d'),
                        TextEntry::make('task.name')
                            ->label('Related Task'),
                        TextEntry::make('assignees')
                            ->label('Participanți')
                            ->formatStateUsing(function ($state, $record) {
                                if ($record->relationLoaded('assignees')) {
                                    return $record->assignees->pluck('name')->join(', ');
                                }
                                return 'Fără participanți, se montează singur';
                            }),
                        TextEntry::make('details')
                            ->label('Details')
                            ->markdown(),
                        TextEntry::make('creator.name')
                            ->label('Created By'),
                    ]),
            ],
            FieldWork::class => [
                Section::make('Field Work Details')
                    ->schema([
                        TextEntry::make('title')
                            ->label('Title')
                            ->size('lg')
                            ->weight('bold'),
                        TextEntry::make('start_date')
                            ->label('Start Date')
                            ->date('Y-m-d'),
                        TextEntry::make('end_date')
                            ->label('End Date')
                            ->date('Y-m-d'),
                        TextEntry::make('task.name')
                            ->label('Related Task'),
                        TextEntry::make('assignees')
                            ->label('Participanți')
                            ->formatStateUsing(function ($state, $record) {
                            if ($record->relationLoaded('assignees')) {
                                return $record->assignees->pluck('name')->join(', ');
                            }
                            return 'No assignees';
                        }),
                        TextEntry::make('details')
                            ->label('Details')
                            ->markdown(),
                        TextEntry::make('creator.name')
                            ->label('Created By'),
                    ]),
            ],
            default => []
        };
    }

    public function getEventContent(): null|string|array
    {
        return [
            Task::class => view('calendar.task-event'),
            // ... other model views if needed
        ];
    }
    
    public function getEventClickContextMenuActions(): array
    {
        return [
            Action::make('view')
                ->icon('heroicon-m-eye')
                ->label('View')
                ->model(fn () => get_class($this->getEventRecord()))
                ->mountUsing(function ($arguments, $form) {
                    $record = $this->getEventRecord();
                    if (!$record) return;
                    
                     // Explicitly load the relationships needed for display
                    $record->load('assignees', 'task', 'creator');
                    
                    // Don't allow editing for tasks
                    if ($record instanceof Task) {
                        $this->dispatch('open-modal', id: 'view-task-details');
                        return;
                    }

                    // Log for debugging
                    Log::info("View - Record type: " . get_class($record));
                    Log::info("View - Assignees count: " . $record->assignees->count());
                    Log::info("View - Assignee names: " . $record->assignees->pluck('name')->join(', '));
                })
                ->infolist(function (Infolist $infolist): Infolist {
                    $record = $this->getEventRecord();
                    $modelType = get_class($record);
                    
                    // Force reload the record with its relationships
                    $record = $modelType::with(['assignees', 'task', 'creator'])->find($record->id);
                    
                    return $infolist
                        ->record($record)
                        ->schema($this->getInfoListSchema($modelType));
                }),
              //  ->disabled(fn () => !$this->getEventRecord()),
                
            Action::make('edit')
                ->icon('heroicon-m-pencil')
                ->label('Edit')
                ->model(fn () => get_class($this->getEventRecord()))
                ->mountUsing(function ($arguments, $form) {
                    $record = $this->getEventRecord();
                    if (!$record) return;
                    
                    // Get the correct model type
                    $modelType = get_class($record);
                    
                    // Force reload the record to ensure we have fresh data
                    $record = $modelType::with(['assignees', 'task'])->find($record->id);
                    
                    if (!$record) {
                        Log::error("Edit - Could not reload record: {$modelType} #{$record->id}");
                        return;
                    }
                    
                    Log::info("Edit - Record type: {$modelType}, ID: {$record->id}");
                    Log::info("Edit - Assignees loaded: " . json_encode($record->assignees->pluck('id')));
                    
                    // Extract data for form
                    $data = $record->toArray();
                    $data['assignees'] = $record->assignees->pluck('id')->toArray();
                    
                    Log::info("Edit - Form data to fill: " . json_encode($data));
                    
                    $form->fill($data);
                })
                ->form(fn () => $this->getSchema(get_class($this->getEventRecord())))
                ->action(function (array $data): void {
                    $record = $this->getEventRecord();
                    if (!$record) return;
                    
                    $modelType = get_class($record);
                    
                    Log::info("Edit Action - Record type: {$modelType}, ID: {$record->id}");
                    Log::info("Edit Action - Received data: " . json_encode($data));
                    
                    // Handle assignees separately
                    $assigneeIds = [];
                    if (isset($data['assignees'])) {
                        // Ensure we have clean IDs
                        foreach ((array)$data['assignees'] as $id) {
                            if (is_numeric($id)) {
                                $assigneeIds[] = (int)$id;
                            }
                        }
                    }
                    
                    Log::info("Edit Action - Processed assignee IDs: " . json_encode($assigneeIds));
                    
                    // Update basic data
                    $updateData = collect($data)->except(['assignees'])->toArray();
                    $record->update($updateData);
                    
                    // Sync the assignees - use DB transaction for safety
                    DB::beginTransaction();
                    try {
                        if ($modelType === Meeting::class) {
                            // Delete existing relationships first to be safe
                            DB::table('meeting_user')->where('meeting_id', $record->id)->delete();
                            
                            // Insert new relationships
                            foreach ($assigneeIds as $userId) {
                                DB::table('meeting_user')->insert([
                                    'meeting_id' => $record->id,
                                    'user_id' => $userId,
                                ]);
                            }
                            
                            Log::info("Edit Action - Manually inserted meeting_user records");
                        } else {
                            // For other models, use regular sync
                            $record->assignees()->sync($assigneeIds);
                            Log::info("Edit Action - Used model sync for assignees");
                        }
                        
                        DB::commit();
                    } catch (\Exception $e) {
                        DB::rollBack();
                        Log::error("Edit Action - Error syncing assignees: " . $e->getMessage());
                    }
                    
                    $this->refreshRecords();
                }),
                
            Action::make('delete')
                ->icon('heroicon-m-trash')
                ->label('Delete')
                ->requiresConfirmation()
                ->action(function () {
                    $record = $this->getEventRecord();
                    if ($record) {
                        $record->delete();
                        $this->refreshRecords();
                    }
                }),
        ];
    }
    
    public function getDateClickContextMenuActions(): array
    {
        return [
            Action::make('createMeeting')
                ->label('Adaugă Întâlnire')
                ->icon('heroicon-m-user-group')
                ->model(Meeting::class)
                ->mountUsing(fn ($arguments, $form) => $form->fill([
                    'start_time' => data_get($arguments, 'dateStr'),
                    'end_time' => data_get($arguments, 'dateStr'),
                ]))
                ->form(fn () => $this->getSchema(Meeting::class))
                ->action(function (array $data): void {
                    Log::info("Create Meeting - Received data: " . json_encode($data));
                    
                    // Handle assignees
                    $assigneeIds = [];
                    if (isset($data['assignees'])) {
                        foreach ((array)$data['assignees'] as $id) {
                            if (is_numeric($id)) {
                                $assigneeIds[] = (int)$id;
                            }
                        }
                    }
                    
                    Log::info("Create Meeting - Processed assignee IDs: " . json_encode($assigneeIds));
                    
                    // Create meeting first
                    $createData = collect($data)->except(['assignees'])->toArray();
                    $meeting = Meeting::create(array_merge($createData, [
                        'created_by' => Auth::id(),
                    ]));
                    
                    Log::info("Create Meeting - Created meeting with ID: {$meeting->id}");
                    
                    // Manually insert assignees
                    DB::beginTransaction();
                    try {
                        foreach ($assigneeIds as $userId) {
                            DB::table('meeting_user')->insert([
                                'meeting_id' => $meeting->id,
                                'user_id' => $userId,
                            ]);
                        }
                        DB::commit();
                        Log::info("Create Meeting - Inserted assignees manually");
                    } catch (\Exception $e) {
                        DB::rollBack();
                        Log::error("Create Meeting - Error inserting assignees: " . $e->getMessage());
                    }
                    
                    $this->refreshRecords();
                }),    

                Action::make('createInstallation')
                ->label('Adaugă Montaj')
                ->icon('heroicon-m-wrench')
                ->model(Installation::class)
                ->mountUsing(fn ($arguments, $form) => $form->fill([
                    'scheduled_date' => data_get($arguments, 'dateStr'),
                   // 'end_time' => data_get($arguments, 'dateStr'),
                ]))
                ->form(fn () => $this->getSchema(Installation::class))
                ->action(function (array $data): void {
                    Log::info("Create Installation - Received data: " . json_encode($data));
                    
                    // Handle assignees
                    $assigneeIds = [];
                    if (isset($data['assignees'])) {
                        foreach ((array)$data['assignees'] as $id) {
                            if (is_numeric($id)) {
                                $assigneeIds[] = (int)$id;
                            }
                        }
                    }
                    
                    Log::info("Create Installation - Processed assignee IDs: " . json_encode($assigneeIds));
                    
                    // Create meeting first
                    $createData = collect($data)->except(['assignees'])->toArray();
                    $installation = Installation::create(array_merge($createData, [
                        'created_by' => Auth::id(),
                    ]));
                    
                    Log::info("Create Installation - Created installation with ID: {$installation->id}");
                    
                    // Manually insert assignees
                    DB::beginTransaction();
                    try {
                        foreach ($assigneeIds as $userId) {
                            DB::table('installation_user')->insert([
                                'installation_id' => $installation->id,
                                'user_id' => $userId,
                            ]);
                        }
                        DB::commit();
                        Log::info("Create Installation - Inserted assignees manually");
                    } catch (\Exception $e) {
                        DB::rollBack();
                        Log::error("Create Installation - Error inserting assignees: " . $e->getMessage());
                    }
                    
                    $this->refreshRecords();
                }),
                Action::make('createFieldwork')
                ->label('Adaugă Deplasare')
                ->icon('heroicon-m-truck')
                ->model(FieldWork::class)
                ->mountUsing(fn ($arguments, $form) => $form->fill([
                    'scheduled_date' => data_get($arguments, 'dateStr'),
                    'end_date' => data_get($arguments, 'dateStr'),
                ]))
                ->form(fn () => $this->getSchema(FieldWork::class))
                ->action(function (array $data): void {
                    Log::info("Create FieldWork - Received data: " . json_encode($data));
                    
                    // Handle assignees
                    $assigneeIds = [];
                    if (isset($data['assignees'])) {
                        foreach ((array)$data['assignees'] as $id) {
                            if (is_numeric($id)) {
                                $assigneeIds[] = (int)$id;
                            }
                        }
                    }
                    
                    Log::info("Create FieldWork - Processed assignee IDs: " . json_encode($assigneeIds));
                    
                    // Create meeting first
                    $createData = collect($data)->except(['assignees'])->toArray();
                    $fieldWork = FieldWork::create(array_merge($createData, [
                        'created_by' => Auth::id(),
                    ]));
                    
                    Log::info("Create FieldWork - Created installation with ID: {$fieldWork->id}");
                    
                    // Manually insert assignees
                    DB::beginTransaction();
                    try {
                        foreach ($assigneeIds as $userId) {
                            DB::table('field_work_user')->insert([
                                'field_work_id' => $installation->id,
                                'user_id' => $userId,
                            ]);
                        }
                        DB::commit();
                        Log::info("Create FieldWork - Inserted assignees manually");
                    } catch (\Exception $e) {
                        DB::rollBack();
                        Log::error("Create FieldWork - Error inserting assignees: " . $e->getMessage());
                    }
                    
                    $this->refreshRecords();
                }),
            // Similar changes for createInstallation and createFieldWork...
        ];
    }
}
