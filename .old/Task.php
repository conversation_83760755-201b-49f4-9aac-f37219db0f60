<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\Pivot;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Support\Str;
use Parallax\FilamentComments\Models\Traits\HasFilamentComments;
use Illuminate\Support\Facades\Auth;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Guava\Calendar\Contracts\Eventable;
use Guava\Calendar\ValueObjects\Event;

use App\Observers\TaskObserver;

#[ObservedBy([TaskObserver::class])]
class Task extends Model implements Eventable
{
    use HasFactory, SoftDeletes, HasFilamentComments, LogsActivity;    

    protected $fillable = [
        'uuid',
        'project_id',
        'assigned_to',
        'added_by',
        'name',
        'description',
        'stage_id',
        'content',
        'price',
 
        'reject_reason',

        // Newly added fields
        'identifier',
        'deadline',
        'is_paid',       // New field
        'fast_track',    // New field
        'client_id', 
        'low_priority',
        'is_finished'
    ];
    protected static function boot()
    {
        parent::boot();

     

        static::creating(function ($model) {
            if (empty($model->uuid)) {
                $model->uuid = Str::uuid()->toString();
            }
        });

        static::updating(function ($model) {
            $model->updated_by = Auth::id();
        });
    }

    
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
        ->logOnly(['name', 'assigned_to', 'added_by', 'project_id', 'identifier', 'deadline', 'is_paid', 'fast_track', 'client_id', 'is_finished', 'price', 'reject_reason', 'substages'])
        //'substages.is_complete', 'substages.is_pending', 'substages.assigned_to', 'substages.added_by'])
        ->logUnguarded();
       // ->logOnly(['name', 'text']);
        // Chain fluent methods for configuration options
    }
    // Relationships
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'low_priority' => 'boolean',
        'is_finished' => 'boolean',
    ];

    public function project()
    {
        return $this->belongsTo(Project::class);
    }

    public function owner()
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    public function addedBy()
    {
        return $this->belongsTo(User::class, 'added_by');
    }

     public function identifierRecord()
 {
     return $this->hasOne(Identifier::class, 'id', 'identifier');
 }
public function identifier()
{
    return $this->belongsTo(Identifier::class, 'identifier');
}

    public function stage()
    {
        return $this->belongsTo(Stage::class);
    }

    public function substages()
    {
        return $this->hasMany(Substage::class);
    }

     /**
     * @return HasMany
     */
    public function substagesOfertare(): HasMany
    {
        return $this->hasMany(Substage::class)
        ->where('stage_id', 1);
    }

     /**
     * @return HasMany
     */
    public function substagesContractare(): HasMany
    {
        return $this->hasMany(Substage::class)
        ->where('stage_id', 2);
    }

     /**
     * @return HasMany
     */

    public function substagesPregatire(): HasMany
    {
        return $this->hasMany(Substage::class)
        ->where('stage_id', 3);
    }

     /**
     * @return HasMany
     */

    public function substagesAprovizionare(): HasMany
    {
        return $this->hasMany(Substage::class)
        ->where('stage_id', 4);
    }

     /**
     * @return HasMany
     */

    public function substagesProductie(): HasMany
    {
        return $this->hasMany(Substage::class)
        ->where('stage_id', 5);
    }

     /**
     * @return HasMany
     */

    public function substagesMontaj(): HasMany
    {
        return $this->hasMany(Substage::class)
        ->where('stage_id', 6);
    }

     /**
     * @return HasMany
     */

    public function substagesReceptie(): HasMany
    {
        return $this->hasMany(Substage::class)
        ->where('stage_id', 7);
    }
   // Support User Relationships
  
   // Accessor to Get All Support Users
   public function supportUsers()
   {
       return $this->belongsToMany(User::class, 'task_support_user', 'task_id', 'user_id')
                   ->withTimestamps();
   }

public function files(): HasMany
{
    return $this->hasMany(File::class);
}

    public function getInvolvedUsers()
    {
        // Ensure the owner relationship is loaded to prevent N+1 queries
        $this->loadMissing('owner', 'supportUsers');

        // Merge the owner and supportUsers into a single collection
        return collect([$this->owner])->merge($this->supportUsers);
    }


    public function getIsUserInvolvedAttribute()
    {
        $userId = Auth::id();
        return $this->assigned_to == $userId || $this->supportUsers->contains($userId);
    }

    
    public function getRouteKeyName()
    {
        return 'uuid';
    }
    public function client()
    {
        return $this->belongsTo(Client::class);
    }

    public function issues(): HasMany
    {
        return $this->hasMany(Issue::class);
    }

    public function matOrders(): HasMany
    {
        return $this->hasMany(MaterialOrder::class);
    }

    public function invoices(): HasMany
    {
        return $this->hasMany(Invoice::class);
    }

    public function proformas(): HasMany
    {
        return $this->hasMany(Proforma::class);
    }

    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }
    

    public function toEvent(): Event|array
    {

        $startFormatted = $this->deadline;//->addHours(2)->setTimezone('Europe/Bucharest')->format('Y-m-d\TH:i:s');
        $endFormatted = $this->deadline;//->addHours(2)->setTimezone('Europe/Bucharest')->format('Y-m-d\TH:i:s');

        


        return Event::make($this)
            ->title($this->name)
             ->start($startFormatted)
             ->end($endFormatted)
            ->backgroundColor('#aa0101')
            ->extendedProps([
                'type' => 'deadline',
              //  'assignees' => $this->assignees->pluck('name'),
              //  'details' => $this->details,
            ]);
    }
}