<?php

namespace App\Tables\Columns;

use Filament\Tables\Columns\ViewColumn;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Filament\Tables\Columns\TextColumn;



class StepColumn extends ViewColumn
{
    protected string $view = 'nested.step';

    protected int $stepNumber;
    protected bool $isLast = false;
   // protected $recordUsing;
  //  protected $record;
    public $currentStep;  
  // public $stepNumber;
   // public $isLast;

   public function stepNumber(int $number): static
   {
       return $this->viewData([
           'stepNumber' => $number,
           
           'isLast' => false, // Set to true for the last step
       ]);
   }
/**
     * Apply custom header classes.
     *
     * @param string $classes
     * @return static
     */
    public function headerClasses(string $classes): static
    {
        $this->headerClass = $classes;

        return $this;
    }

        /**
     * Get header attributes.
     *
     * @return array
     */
    public function getHeaderAttributes(): array
    {
        return $this->headerClass
            ? ['class' => $this->headerClass]
            : [];
    }

    public function lastStep(bool $isLast = true): static
   {
       $this->isLast = $isLast;
        return $this;
   }

   public function getHeader(): string
{
    return view($this->view, [
        'isHeader'    => true,
        'headerClass' => $this->headerClass,
        // Add other necessary data if needed
    ])->render();
}




}