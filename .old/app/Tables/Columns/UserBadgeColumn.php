<?php

namespace App\Tables\Columns;

use Filament\Tables\Columns\ViewColumn;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Filament\Tables\Columns\TextColumn;



class UserBadgeColumn extends ViewColumn
{


   
    protected string $view = 'nested.user-badge';
    protected function setUp(): void
    {
        parent::setUp();

        $this->getStateUsing(function ($record, $column) {
            $userId = data_get($record, $column->getName());
            $user = \App\Models\User::find($userId);
            return [
                'name' => $user ? $user->name : 'Unknown User',
                'id'   => $user ? $user->id : null,
            ];
        });
    }



}