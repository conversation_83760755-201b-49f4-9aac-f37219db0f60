<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Symfony\Component\Process\Process;

class RunDevServer extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'run:devserver';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Run npm dev, reverb server, and Laravel server';

    /**
     * Execute the console command.
     *
     * @return int
     */
public function handle()
    {
        $this->info('Starting npm run dev...');
        $npmProcess = new Process(['npm', 'run', 'dev']);
        $npmProcess->setTimeout(null);
        $npmProcess->start();
      

        $this->info('Starting Laravel server...');
        $serveProcess = new Process(['php', 'artisan',  'serve', '--host=*************', '--port=8000']);
        $serveProcess->setTimeout(null);
        $serveProcess->start();

        $this->info('Starting reverb server...');
        $reverbProcess = new Process(['php', 'artisan', 'reverb:start', '--host=*************', '--debug']);
        $reverbProcess->setTimeout(null);
        $reverbProcess->start();

        $this->info('Starting worker...');
        $workerProcess = new Process(['php', 'artisan',  'queue:listen']);
        $workerProcess->setTimeout(null);
        $workerProcess->start();

        $this->info('Starting pulse check');
        $workerProcess = new Process(['php', 'artisan',  'pulse:check']);
        $workerProcess->setTimeout(null);
        $workerProcess->start();

        // Continuously check the output of each process
         while ($npmProcess->isRunning() || $reverbProcess->isRunning() || $serveProcess->isRunning()) {
           // while ($npmProcess->isRunning() || $serveProcess->isRunning()) {
            if ($npmProcess->isRunning()) {
                echo $npmProcess->getIncrementalOutput();
                echo $npmProcess->getIncrementalErrorOutput();
            }

            if ($reverbProcess->isRunning()) {
                echo $reverbProcess->getIncrementalOutput();
                echo $reverbProcess->getIncrementalErrorOutput();
            }

            if ($serveProcess->isRunning()) {
                echo $serveProcess->getIncrementalOutput();
                echo $serveProcess->getIncrementalErrorOutput();
            }

            // Sleep for a short period to avoid high CPU usage
            usleep(100000); // 100ms
        }

        return 0;
    }
}