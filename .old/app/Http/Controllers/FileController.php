<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\File;
use App\Models\Task;
use Illuminate\Support\Facades\Storage;
use Yaza\LaravelGoogleDriveStorage\Gdrive;
use illuminate\Support\Facades\Log;

class FileController extends Controller
{
    // Placeholder methods for future implementation
    public function index()
    {
        $task = Task::findOrFail($taskId);
        $files = $task->files;
    }

    public function store(Request $request)
    {
        $request->validate([
            'file' => 'required|file',
            'task_id' => 'required|exists:tasks,id',
        ]);

        $file = $request->file('file');
        $pathprefix = 'CRMcrm/tasks/' . $request->task_id;
        $path = Storage::disk('google')->putFile($pathprefix, $file);
Log::info('path: '.$path);
        $fileModel = new File();

        $fileModel->task_id = $request->task_id;
        $fileModel->filename = $file->getClientOriginalName();
        $fileModel->filepath = $path;
Log::info('Filemodel: '.$fileModel);
        $fileModel->save();

       // return response()->json(['message' => 'File uploaded successfully', 'file' => $fileModel], 201);
    }

    public function show(File $file)
    {
       // For a quick check, you might redirect to the Google Drive link:
    return redirect()->away("https://drive.google.com/file/d/{$file->file_id}/view");
    }

    public function destroy(File $file)
    {
        $file = File::findOrFail($id);
        Storage::disk('google')->delete($file->filepath);
        $file->delete();
    }
}
