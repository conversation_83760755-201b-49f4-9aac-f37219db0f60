<?php

namespace App\Http\Controllers;

use App\Models\Client;
use Illuminate\Http\Request;

class ClientController extends Controller
{
    /**
     * Search for clients by name.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function search(Request $request)
    {
        $search = $request->input('search', '');

        $clients = Client::where('name', 'like', "%{$search}%")
                        ->limit(10)
                        ->get(['id', 'name']);

        return response()->json($clients);
    }
}