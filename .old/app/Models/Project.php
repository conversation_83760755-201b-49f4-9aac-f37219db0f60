<?php


namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Project extends Model
{
    use HasFactory;

    protected $fillable = [
        'uuid',
        'added_by',
        'description',
        'project_level_billing', // newly added field
    ];

    // Relationships

    public function tasks()
    {
        return $this->hasMany(Task::class);
    }

    public function identifier()
    {
        return $this->hasOne(Identifier::class);
    }

    public function owner()
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    public function addedBy()
    {
        return $this->belongsTo(User::class, 'added_by');
    }
}