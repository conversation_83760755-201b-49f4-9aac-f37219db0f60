<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Substage extends Model
{
    use HasFactory;

    protected $fillable = [
        'stage_id',
        'name',
        'isComplete',
        'value',
    ];

    // Relationships

    public function stage()
    {
        return $this->belongsTo(Stage::class);
    }

    public function tasks()
    {
        return $this->belongsToMany(Task::class, 'active_substages')
                    ->withPivot('created_at', 'updated_at')
                    ->withTimestamps();
    }
}