<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
class Client extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'tax_id',
        'country',
        'county',
        'city',
        'address',
        'iban',
        'bank',
        'phone_legal',
        'phone_work01',
        'email',
        'roc_id',
        'contact_legal',
        'contact_work01',
        'id_series',
        'id_number',
        'id_issuer',
        'vehicle_plate',
        'postal_code',
        'discount_rate',
    ];

    /**
     * Get the tasks for the client.
     */
    public function tasks()
    {
        return $this->hasMany(Task::class);
    }
}