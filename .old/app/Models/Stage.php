<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Stage extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
    ];

    // Relationships

    public function tasks()
    {
        return $this->hasMany(Task::class);
    }

    public function substages()
    {
        return $this->hasMany(Substage::class);
    }
}
