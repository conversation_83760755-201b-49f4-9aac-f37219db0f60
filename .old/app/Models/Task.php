<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Task extends Model
{
    use HasFactory, SoftDeletes;    

    protected $fillable = [
        'uuid',
        'project_id',
        'assigned_to',
        'added_by',
        'name',
        'description',
        'stage_id',
        'content',
        'price',
 
        'reject_reason',

        // Newly added fields
        'identifier',
        'deadline',
        'is_paid',       // New field
        'fast_track',    // New field
        'client_id', 
        'low_priority',
        'is_finished'
    ];
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->uuid)) {
                $model->uuid = Str::uuid()->toString();
            }
        });
    }
    // Relationships
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'low_priority' => 'boolean',
        'is_finished' => 'boolean',
    ];

    public function project()
    {
        return $this->belongsTo(Project::class);
    }

    public function owner()
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    public function addedBy()
    {
        return $this->belongsTo(User::class, 'added_by');
    }

     public function identifierRecord()
 {
     return $this->hasOne(Identifier::class, 'id', 'identifier');
 }
public function identifier()
{
    return $this->belongsTo(Identifier::class, 'identifier');
}

    public function stage()
    {
        return $this->belongsTo(Stage::class);
    }

    public function substages()
    {
        return $this->belongsToMany(Substage::class, 'active_substages')
                    ->withPivot('created_at', 'updated_at')
                    ->withTimestamps();
    }

    public function activeSubstages()
{
    return $this->belongsToMany(Substage::class, 'active_substages')
                ->withPivot('is_complete')
                ->withTimestamps();
}
   // Support User Relationships
  
   // Accessor to Get All Support Users
   public function supportUsers()
   {
       return $this->belongsToMany(User::class, 'task_support_user', 'task_id', 'user_id')
                   ->withTimestamps();
   }

public function files(): HasMany
{
    return $this->hasMany(File::class);
}

public function getInvolvedUsers()
    {
        // Ensure the owner relationship is loaded to prevent N+1 queries
        $this->loadMissing('owner', 'supportUsers');

        // Merge the owner and supportUsers into a single collection
        return collect([$this->owner])->merge($this->supportUsers);
    }

    public function getRouteKeyName()
    {
        return 'uuid';
    }
    public function client()
    {
        return $this->belongsTo(Client::class);
    }

    public function issues(): HasMany
    {
        return $this->hasMany(Issue::class);
    }

    public function matOrders(): HasMany
    {
        return $this->hasMany(MaterialOrder::class);
    }
    
}
