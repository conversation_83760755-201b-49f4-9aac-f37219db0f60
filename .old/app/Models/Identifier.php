<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Identifier extends Model
{
    use HasFactory;

    protected $fillable = [
        'task_id',
        'channel',
        'clientName',
        'arrivedAt',
    ];

    // Relationships

    public function task()
    {
        return $this->belongsTo(Task::class);
    }
}