<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Task;
use App\Models\Identifier;

class IdentifiersDisplay extends Component
{
    public $task ;
    public $identifier;
   // $state = $getState();
    public function mount($task)
    {

       // $state = $getState();
        
       //dd($task);
        $this->task = $getState($task);
        $this->taskString = print_r($this->task, true);
      //  $this->identifier = Identifier::find($this->task->identifier);
        
    }
    public function render()
    {
        return view('livewire.identifiers-display', [
        //    'identifierRecord' => $this->identifier,
        ]);
    }
}
