<?php

namespace App\Filament\App\Resources;

use App\Filament\App\Resources\TaskResource\Pages;
use App\Filament\App\Resources\TaskResource\RelationManagers;
use App\Models\Task;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Database\Eloquent\Model;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Pages\CreateRecord;
use Filament\Resources\Pages\EditRecord;
use Filament\Resources\Pages\ViewRecord;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ViewColumn;
use Filament\Tables\Columns\IconColumn;
use App\Tables\Columns\StepColumn;
use App\Tables\Columns\UserBadgeColumn;
use App\Tables\Columns\SupportUserBadgeColumn;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\Auth;
use Filament\Tables\Actions\ActionGroup;
use Illuminate\Support\Facades\Gate;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\ToggleFilter;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Card;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Button;





use Filament\Forms\Components\DateTimePicker;



use Filament\Forms\Components\Toggle;
class TaskResource extends Resource
{
    protected static ?string $model = Task::class;

    // Navigation & Labeling
    protected static ?string $navigationIcon = 'heroicon-o-clipboard-document-check';
    protected static ?string $navigationLabel = 'Work';
    protected static ?string $pluralModelLabel = 'Work'; 
    protected static ?string $slug = 'work'; // The URL in the sidebar




   // public static function getTableQuery()
   // {
    //    return static::$model::query(); // returns Eloquent\Builder
   // }

   public static function getEloquentQuery(): Builder
{
    return parent::getEloquentQuery()->with('supportUsers');
}
    public static function table(Table $table): Table
    {
        return $table
        
            ->columns([
                // [task id]
                TextColumn::make('id')
                    ->label('ID')
                    ->sortable()
                    ->grow(false)->wrap()->width('24px'),

                // [some kind of icon indicating if has project]
                // We assume a task "has a project" if project_id is not null
              //  IconColumn::make('project_id')
              //      ->label('')
              //      ->boolean()
              //      ->trueIcon('heroicon-o-briefcase')
              //      ->falseIcon('heroicon-o-academic-cap')
              //      ->tooltip(fn ($record) => $record->project_id ? 'Has Project' : 'No Project')
              //      ->grow(false)->wrap()->width('24px'),

                // [a custom livewire component for identifiers]
                // We'll use a ViewColumn and pass the task as a parameter
                ViewColumn::make('identifierRecord')
                    ->label('Detalii')
                    ->view('nested.identifiers-display')
                    ->grow(false)
                    ->width('200px'),
                UserBadgeColumn::make('assigned_to')
                ->label(new HtmlString(Blade::render('<x-heroicon-o-user class="w-4 h-4" />')))
                    
                    ->tooltip(fn ($record) => $record->owner ? $record->owner->name : 'No Owner')
                    ->grow(false)
                    ->width('24px'),
                SupportUserBadgeColumn::make('supportUsers')
                    ->label(new HtmlString(Blade::render('<x-heroicon-o-user-group class="w-4 h-4" />')))
                    
                    ->grow(false)
                    ->width('24px'),

             // ViewColumn::make('debug_column')
             // ->label('Debug')
             // ->view('nested.debug-view'),
                // [the deadline]
                TextColumn::make('deadline')
                    ->label('Deadline')
                    ->date()
                    ->sortable()->width('100px'),

                // [a livewire component displaying the users initials]
              //  ViewColumn::make('user_badge')
             //       ->label('Assigned To')
             //       ->view('user-badge'),   

                // [task name]
                TextColumn::make('name')
                    ->label('Lucrare')
                    
                    ->searchable()
                    ->sortable()->grow(false)
                    ->width('384px'),

                StepColumn::make('stage_1')
                ->label('Ofertare')
                ->width('80px')
                ->grow(false)
                    ->stepNumber('1'),

                // Step 2
                StepColumn::make('stage_2')
                ->label('Contractare')
                ->width('80px')->grow(false)
                    ->stepNumber('2'),
                    
                // Step 3
                StepColumn::make('stage_3')
                ->label('Pregătire')
                ->width('80px')->grow(false)
                    ->stepNumber('3'),
                    
                // Step 4
                StepColumn::make('stage_4')
                ->label('Aprovizionare')
                ->width('80px')->grow(false)
                    ->stepNumber('4'),
                    
                // Step 5
                StepColumn::make('stage_5')
                ->label('Execuție')
                
                ->width('80px')->grow(false)
                ->stepNumber('5'),
                

                // Step 6 (Last Step)
                StepColumn::make('stage_6')
                ->label('Montaj')
                ->width('80px')->grow(false)
                    ->stepNumber('6'),
                    
                      // Step 6 (Last Step)
                StepColumn::make('stage_7')
                ->label('Finalizare')
                ->width('80px')->grow(false)
                      ->stepNumber('7')
                      ->lastStep(),
            ])
            ->actions([
            
                
                Tables\Actions\EditAction::make()
                    ->label('')
                    ->icon('heroicon-o-pencil')
                    ->authorize(fn () => true) 
                ->disabled(fn ($record) => !Gate::allows('update', $record)),
                Tables\Actions\DeleteAction::make()
                ->label('')
                ->icon('heroicon-o-trash')
                ->authorize(fn () => true)
                ->requiresConfirmation()
                ->form([
                    TextInput::make('reject_reason')
                        ->label('Motivul pentru care stergeți această lucrare')
                        ->required(false)
                        ->placeholder('(optional)'),
                ])
                ->action(function ($record, $data) {
                    $record->update(['reject_reason' => $data['reject_reason']]);
                    $record->delete();
                })
                ->disabled(fn ($record) => !Gate::allows('delete', $record)),
                
                    ])
            ->filters([
                 // **1. Owned Tasks Filter**
            Filter::make('owned')
            ->label('Proprii') // "Owned"
           
            ->query(function (Builder $query, $state): Builder {
                if ($state) {
                    $query->where('assigned_to', Auth::id());
                }

                return $query;
            }),

        // **2. Supporting Tasks Filter**
        Filter::make('supporting')
            ->label('În suport') // "In Support"
          
            ->query(function (Builder $query, $state): Builder {
                if ($state) {
                    $query->whereHas('supportUsers', function ($q) {
                        $q->where('user_id', Auth::id());
                    });
                }

                return $query;
            }),

        // **3. Involved Tasks Filter**
        Filter::make('involved')
            ->label('Ambele') // "Involved"
      
            ->query(function (Builder $query, $state): Builder {
                if ($state) {
                    $query->where(function ($q) {
                        $q->where('assigned_to', Auth::id())
                          ->orWhereHas('supportUsers', function ($q2) {
                              $q2->where('user_id', Auth::id());
                          });
                    });
                }

                return $query;
            }),
            ])
            
            ->defaultSort('id', 'desc')
            ->recordUrl(fn (Model $record): string => TaskResource::getUrl('view', ['record' => $record]))
            ;
    }

    




    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTasks::route('/'),
            'create' => Pages\CreateTask::route('/create'),
            'edit' => Pages\EditTask::route('/{record:uuid}/edit'), // Use UUID
            'view' => Pages\ViewTask::route('/{record:uuid}'), // Ensure this is registered
        ];
    }
}