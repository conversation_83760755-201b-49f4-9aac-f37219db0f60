<?php

namespace App\Filament\App\Resources\TaskResource\Pages;

use App\Filament\App\Resources\TaskResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use App\Events\TaskUpdated;
use App\Events\TestEvent;

class ListTasks extends ListRecords
{
    protected static string $resource = TaskResource::class;

    /**
     * Get the URL for a table row when clicked.
     *
     * @param  mixed  $record
     * @return string|null
     */
    protected function getHeaderActions(): array
    {
        event(new TestEvent());
        broadcast(new TestEvent());
        return [
            Actions\CreateAction::make()->label('Adaugă lucrare') // **Custom Label**
            ->icon('heroicon-o-plus') // **Custom Icon**
            ->color('danger') // **Custom Color (Tailwind CSS color)**
            ->size('md') // **Optional: Customize size**
            ->tooltip('Adaugă o lucrare nouă'), // **Optional: Add a tooltip**
            
        ];
        
    }

    protected function getTableRowUrl($record): ?string
    {
        return TaskResource::getUrl('view', ['record' => $record]);
    }
}
