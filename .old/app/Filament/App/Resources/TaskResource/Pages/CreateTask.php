<?php

namespace App\Filament\App\Resources\TaskResource\Pages;

use App\Filament\App\Resources\TaskResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Components\DateTimePicker;
use Filament\Resources\Pages\ListRecords;

use Filament\Resources\Pages\EditRecord;
use Filament\Resources\Pages\ViewRecord;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ViewColumn;
use Filament\Tables\Columns\IconColumn;
use App\Tables\Columns\StepColumn;
use App\Tables\Columns\UserBadgeColumn;
use App\Tables\Columns\SupportUserBadgeColumn;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\Auth;
use Filament\Tables\Actions\ActionGroup;
use Illuminate\Support\Facades\Gate;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\ToggleFilter;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Card;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Button;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\Wizard;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Wizard\Step;
use Filament\Forms\Components\Hidden;
use App\Models\Substage;
use App\Models\Stage;
use App\Models\Identifier;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\ViewField;
use Filament\Forms\Concerns\InteractsWithForms;
use App\Models\Client;
use Illuminate\Support\Facades\Log;
use Filament\Notifications\Notification;
use App\Models\User;
use App\Events\TaskUpdated;

class CreateTask extends CreateRecord
{
    
    
    public ?int $currentStageId = null;
    protected static string $resource = TaskResource::class;

 

    
protected function associateSubstages(Stage $stage)
{
    $substages = $stage->substages;

    foreach ($substages as $substage) {
        if (!$this->record->activeSubstages()->where('substage_id', $substage->id)->exists()) {
            $this->record->activeSubstages()->attach($substage->id, ['is_complete' => false]);
        }
    }
}

protected function mutateFormDataBeforeCreate(array $data): array
{


    if (!empty($data['client_id'])) {
        $client = Client::find($data['client_id']);
        if ($client) {
            $data['identifier']['clientName'] = $client->name;
        }
    }




    // Handle Identifier Data
    if (isset($data['identifier'])) {
        // Extract Identifier data
        $identifierData = $data['identifier'];
        unset($data['identifier']);

        // Create Identifier
        $identifier = Identifier::create($identifierData);

        // Assign Identifier's ID to Task's 'identifier' field
        $data['identifier'] = $identifier->id;
    }


// Determine current stage ID from 'substageData' keys
if (isset($data['substageData']) && !empty($data['substageData'])) {
    // Get all stage IDs from 'substageData' keys
    $stageIds = array_keys($data['substageData']);

    // Assuming the last stage is the current one
    $this->currentStageId = end($stageIds);

   // $data['stage_id'] = $this->currentStageId;
} else {
    // Fallback to a default stage ID if needed
  //  $data['stage_id'] = $this->currentStageId ?? Stage::first()->id;
}



    $this->substagesToAssociate = [];

    if (isset($data['substageData'])) {
        foreach ($data['substageData'] as $stageId => $substages) {
            foreach ($substages as $substageData) {
                $this->substagesToAssociate[$substageData['substage_id']] = [
                    'is_complete' => $substageData['is_complete'] ?? false,
                ];
            }
        }
        unset($data['substageData']);
    }

    return $data;
}

protected function afterCreate()
{
    

   // $assignedUserId = $this->record->assigned_to; // For example
    event(new TaskUpdated('You have been assigned a new task!'));
    

}

public function confirmExclude()
{
    $this->excluded = true;
}
public function form(Form $form): Form
    {


    $stages = Stage::with('substages')->get();
        // Initialize currentStageId to the first stage's ID
        $this->currentStageId = $stages->first()->id ?? null;
    $wizardSteps = [];

        foreach ($stages as $stage) {

            $substageCards = [];


            $wizardSteps[] = Step::make($stage->name)
                ->schema([
                
                    
                    Forms\Components\Repeater::make('substageData.' . $stage->id)
                        // ->label($stage->name . ' Substages')
                            ->label('') // Hide the label
                            ->schema([

                                
                                
                                Checkbox::make('is_complete')
                                    ->label('Finalizat')
                                    ->default(false),
                                
                        
                                
                                // Exclude Button

                            ])->grid(4)
                            ->itemLabel(function ($state) {
                                return isset($state['substage_id']) 
                                    ? (Substage::find($state['substage_id'])->name ?? 'Substage') 
                                    : 'Substage';
                            })
                            ->createItemButtonLabel('') // Hide the "Add" button
                            ->disableItemCreation()
                            //->disableItemDeletion()
                            ->default($stage->substages->map(function ($substage) {
                                return [
                                    'substage_id' => $substage->id,
                                    
                                    'is_complete' => false,
                                ];
                            })->toArray()),
                ])->beforeValidation(function () use ($stage) {
                    // Set the currentStageId to the ID of this stage
                    $this->currentStageId = $stage->id;
                    Log::info('Current Stage ID: ' . $this->currentStageId);
                });
        }

        return $form->schema([
                Forms\Components\Grid::make()
                    ->columns(12)
                    ->schema([
                        // First column (span 5)
                        Grid::make()
                            ->columns(2)
                            ->columnSpan(5)
                            ->schema([
                                                 
  
                                Forms\Components\Section::make()
                                            ->columns(3)
                                            ->columnSpan(2)
                                            ->schema([
                                                // **Added By (Hidden Field)**
                                                    Hidden::make('added_by')
                                                        ->default(Auth::id()), // Automatically set to current user's ID
                                                    TextInput::make('name')
                                                    ->label('')->placeholder('Nume lucrare')
                                                    ->columnSpan(2)->required(),
                                                    Forms\Components\Section::make()
                                                
                                                    ->columns(2)
                                                    ->columnSpan(1)
                                                    ->schema([Checkbox::make('fast_track')
                                                    ->label('FastTrack'),
                                                    
                                                    Checkbox::make('is_paid')
                                                    ->label('Achitat')
                                                    ])
                                                    
                                                    ,
                                                RichEditor::make('content')
                                                    ->label('')
                                                    ->columnSpan(3),
                                                TextInput::make('price')
                                                    ->label('Price')
                                                    ->numeric()->columnSpan(1),
   
                                       
                                            ]),
                                        
                                       
                                     
                                   
                            ]),
                        // Third column (span 3)
                        Grid::make()
                        ->extraAttributes(['class' => 'flex items-stretch']) // Ensure equal height
                            ->columns(7)
                            ->columnSpan(7)
                            ->schema([
                                Card::make()
                                ->columns(1)
                                ->columnSpan(2)
                                ->schema([
                                    // **Channel (Select)**
                                    Select::make('identifier.channel')
                                        ->label('Canal intrare')
                                        ->options([
                                            'Channel1' => 'Email',
                                            'Channel2' => 'Telefonic',
                                            'Channel3' => 'Messaging',
                                        ])
                                        ->required()
                                        ->reactive(), // Makes the field reactive if needed

                                    // **Client Name (Text Input)**
                                   // Client Selection
                    Select::make('client_id')
                    ->label('Nume Client') ->required()
                    ->searchable()
                    ->preload() // Optionally preload options
                    ->getSearchResultsUsing(function (string $search) {
                        return Client::where('name', 'like', "%{$search}%")
                                     ->limit(10)
                                     ->pluck('name', 'id')
                                     ->toArray();
                    })
                    ->createOptionForm([
                        TextInput::make('name')
                            ->label('Nume Client Nou')
                            ->required()
                            ->maxLength(255),
                    ])
                    ->createOptionUsing(function (array $data) {
                        Log::info('createOptionUsing data:', ['data' => $data]);
                    
                        if (empty($data['name'] ?? '')) {
                            Log::error('Client name not provided in createOptionUsing.');
                            return '';
                        }
                    
                        $clientName = trim($data['name']);
                        $existingClient = Client::where('name', $clientName)->first();
                        if ($existingClient) {
                            Log::info("Client exists: ID {$existingClient->id}, Name {$existingClient->name}");
                            return $existingClient->id;
                        }
                    
                        try {
                            $client = Client::create(['name' => $clientName]);
                            Log::info("Created new client: ID {$client->id}, Name {$client->name}");
                            return $client->id;
                        } catch (\Exception $e) {
                            Log::error('Error creating new client: ' . $e->getMessage());
                            return '';
                        }
                    }) ->getOptionLabelUsing(function ($value) {
                        return Client::find($value)?->name;
                    })
                    ->reactive()
                    ->placeholder('Cauta sau adaugă')
                    ,

                // Identifier Client Name
                TextInput::make('identifier.clientName')
                    ->label('Client Name')
                    ->required()
                    ->maxLength(255)
                    ->reactive()
                    ->hidden(),

                                    // **Arrived At (DateTime Picker)**
                                    DateTimePicker::make('identifier.arrivedAt')
                                        ->label('Dată intrare')
                                        ->required(),
                                ]), 
                                Card::make()
                                    ->schema([
                                        DatePicker::make('deadline')
                                            ->label('Deadline')
                                            ->columnSpan(1),
                                            Select::make('assigned_to')
                                            ->label('Atribuit lui')
                                            ->relationship('owner', 'name')->required(),
                                            Select::make('support_users')
                                            ->label('Colaboratori suport')
                                            ->multiple()
                                            ->relationship('supportUsers', 'name'),

                                    ])->columnSpan(2), 
                                    Card::make()
                                    ->schema([
                                        
                                        Placeholder::make('files')
                                            ->label('Fișiere')
                                            ->content('File links will be displayed here'),
                                        Placeholder::make('upload_files')
                                            ->label('Upload Files')
                                            ->content('File upload input here'),
                                    ])->columnSpan(3),
                                    Hidden::make('stage_id')
                        ->default(null)
                        ->disabled(),
                        Wizard::make($wizardSteps)
                                    ->skippable()
                                    ->columns(1)
                                    ->columnSpan(7),
                            ]),
                   
                        
                  
                        ]),
                    ]);


   }

   
}

