<?php

namespace App\Filament\App\Resources\TaskResource\Pages;

use App\Filament\App\Resources\TaskResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
//use App\Filament\App\Resources\TaskResource;
//use Filament\Resources\Pages\ViewRecord;
use Filament\Forms\Components;
use Filament\Forms;
use Filament\Tables;
use Illuminate\Support\HtmlString;
use App\Models\Task;
use App\Models\Substage;
use App\Models\Identifier;
use App\Models\Stage;
use App\Models\User;
use App\Models\File;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Facades\Storage;
use Yaza\LaravelGoogleDriveStorage\Gdrive;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\RepeatableEntry;


class ViewTask extends ViewRecord
{
    protected static string $resource = TaskResource::class;




  

   public function form(Forms\Form $form): Forms\Form
    {
        return $form
            ->schema([
                // Top section: Show task name and stage progress
                Components\Section::make('Overview')
                    ->schema([
                        Components\View::make('nested.stage-progress')
                            ->label(false)
                            ->viewData(['record' => $this->record]),
                        
                        
                    ])
                    ->collapsible(false)
                    ->columns(3),

                // Main Content in a 3-column layout
                Components\Grid::make(3)
                    ->schema([
                        // Left Column
                        Components\Section::make('Client / Identifier Info')
                            ->schema([
                                // Identifier Record Information
                                
                            
                                // Ownership & Assignment
                                Components\Card::make()
                                    ->schema([
                                        Components\Placeholder::make('owner')
                                            ->label('Assigned To')
                                            ->content(fn($record) => $record->owner?->name ?? 'Not Assigned'),
                                        Components\Placeholder::make('support_users')
                                            ->label('Support Users')
                                            ->content(function($record){
                                                $users = $record->supportUsers;
                                                return $users->isEmpty() 
                                                    ? 'No support users' 
                                                    : $users->pluck('name')->join(', ');
                                            }),
                                    ]),
                            ])
                            ->columnSpan(1),

                        // Center Column
                        Components\Section::make('Task Details')
                            ->schema([
                                Components\Card::make()
                                    ->schema([
                                        Components\Placeholder::make('description')
                                            ->label('Description')
                                            ->content(fn($record) => $record->description ?? 'No description'),
                                        Components\View::make('nested.rich-text-display')
                                            ->label('Content')
                                            ->viewData(['content' => $this->record->content]),

                                        Components\Placeholder::make('price')
                                            ->label('Price')
                                            ->content(fn($record) => $record->price ? '$' . number_format($record->price, 2) : 'N/A')
                                            ->visible(fn ($record) => auth()->user()->isAdmin() || auth()->user()->isOwner($record)),

                                        Components\Placeholder::make('flags')
                                            ->label('Status')
                                            ->content(function($record) {
                                                $isPaid = $record->is_paid ? 'Paid' : 'Unpaid';
                                                $fastTrack = $record->fast_track ? 'Fast Track' : 'Regular';
                                                return "$isPaid / $fastTrack";
                                            }),
                                    ]),

                                // Substage Completion Status
                                Components\Card::make()
                                    ->schema([
                                        Components\Placeholder::make('active_substages')
                                            ->label('Substages')
                                            ->content(function($record) {
                                                $currentStageId = $record->stage_id;
                                                $substages = $record->activeSubstages;
                                                if ($substages->isEmpty()) {
                                                    return 'No substages';
                                                }

                                                // Display a list with checkmarks
                                                return new HtmlString(
                                                    $substages->map(function($sub) use ($currentStageId) {
                                                        $check = $sub->pivot->is_complete 
                                                            ? '<x-heroicon-o-check-circle class="w-4 h-4 text-green-500 inline" />'
                                                            : '<x-heroicon-o-minus-circle class="w-4 h-4 text-gray-400 inline" />';
                                                        return "<div class='flex items-center space-x-2'>
                                                                    $check
                                                                    <span>{$sub->name}</span>
                                                                </div>";
                                                    })->join('')
                                                );
                                            }),
                                    ]),
                            ])
                            ->columnSpan(1),

                        // Right Column
                        Components\Section::make('Proiect și fișiere')
                            ->schema([
                                // Project Info
                                Components\Card::make()
                                    ->schema([
                                        Components\Placeholder::make('project')
                                            ->label('Proiect')
                                            ->content(fn($record) => $record->project_id 
                                                ? "Project #{$record->project_id}" // Or fetch project details if available
                                                : 'Nu face parte dintr-un proiect'),
                                        Components\Placeholder::make('deadline')
                                            ->label('Task Deadline')
                                            ->content(fn($record) => $record->deadline ?? 'N/A'),
                                    ]),

                                // Files & Attachments
                                Components\Card::make()
                                    ->schema([
                                        Components\Placeholder::make('files')
                                        ->label('Fișiere')
                                        ->content(function ($record) {
                                            $folderPath = "CRMcrm/tasks/{$record->id}";
                                            if (!Storage::disk('google')->exists($folderPath)) {
                                                return 'No files found';
                                            }
                
                                            $files = Storage::disk('google')->files($folderPath);
                                            if (empty($files)) {
                                                return 'No files found';
                                            }
                
                                            $fileItems = [];
                                            foreach ($files as $file) {
                                                $fileName = basename($file);
                                                $fileUrl = Storage::disk('google')->url($file);
                                                $fileItems[] = [
                                                    'name' => $fileName,
                                                    'url' => $fileUrl,
                                                ];
                                            }
                
                                            return Infolist::make()
                                            
                                                ->state(['files' => $fileItems])
                                                ->schema([
                                                    RepeatableEntry::make('files')
                                                    ->label('')
                                                        ->schema([
                                                            TextEntry::make('url')
                                                            ->html()
                                                                ->label($fileName)
                                                                ->url($fileUrl)->openUrlInNewTab()
                                                                ,
                                                        ]),
                                                ]);
                                        }),
                                            
                                    ]),

                                // Reject Reason (if any)
                                Components\Card::make()
                                    ->schema([
                                        Components\Placeholder::make('reject_reason')
                                            ->label('Reject Reason')
                                            ->content(fn($record) => $record->reject_reason ?? 'No reject reason')
                                            ->visible(fn($record) => !empty($record->reject_reason)),
                                    ]),
                            ])
                            ->columnSpan(1),
                    ]),
            ]);
    }
}
