<?php

namespace App\Filament\App\Resources\TaskResource\Pages;

namespace App\Filament\App\Resources\TaskResource\Pages;

use App\Filament\App\Resources\TaskResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Components\DateTimePicker;
use Filament\Resources\Pages\ListRecords;

use Filament\Resources\Pages\EditRecord;
use Filament\Resources\Pages\ViewRecord;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ViewColumn;
use Filament\Tables\Columns\IconColumn;
use App\Tables\Columns\StepColumn;
use App\Tables\Columns\UserBadgeColumn;
use App\Tables\Columns\SupportUserBadgeColumn;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\Auth;
use Filament\Tables\Actions\ActionGroup;
use Illuminate\Support\Facades\Gate;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\ToggleFilter;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Card;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Button;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\Wizard;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Wizard\Step;
use Filament\Forms\Components\Hidden;
use App\Models\Substage;
use App\Models\Stage;
use App\Models\Identifier;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\ViewField;
use Filament\Forms\Concerns\InteractsWithForms;
use Illuminate\Database\Eloquent\Builder;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Repeater;
use App\Models\File;
use Illuminate\Support\Facades\Storage;
use Yaza\LaravelGoogleDriveStorage\Gdrive;
use Illuminate\Support\Facades\Log;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\RepeatableEntry;
use Filament\Infolists\Components\IconEntry;
use Filament\Infolists\Components\Fieldset;
use Filament\Forms\Components\View;


class EditTask extends EditRecord
{
    
    protected static string $resource = TaskResource::class;

    protected array $substagesToAssociate = [];
    protected array $supportUsersToAttach = [];
    
    protected function getRecordQuery(): Builder
    {
        return parent::getRecordQuery()->with([
            'identifierRecord',
            'activeSubstages',
            'supportUsers',
            // Include other relationships if needed
        ]);
    }

    public ?int $currentStageId = null;


    public function form(Form $form): Form
    {
      //  $groupedActiveSubstages = $this->getGroupedActiveSubstages();
        $stages = Stage::with('substages')->get();
        $wizardSteps = [];

        $activeSubstagesGrouped = $this->record->activeSubstages->groupBy(function ($substage) {
            return $substage->stage->id;
        });

        foreach ($stages as $stage) {
            $wizardSteps[] = Wizard\Step::make($stage->name)
                ->schema([
                    Repeater::make('activeSubstagesByStage.' . $stage->id)
                    ->label('') // Remove the label
                        ->schema([
                            Hidden::make('substage_id'),
                            Checkbox::make('is_complete')
                                ->label('Finalizat')
                                ->default(false),
                        ])
                        ->itemLabel(function ($state) {
                            if (! isset($state['substage_id'])) {
                                return 'Substage';
                            }
                            $substage = Substage::find($state['substage_id']);
                            return $substage ? $substage->name : 'Substage';
                        })->grid(4)
                        ->addable(false)
                        ->default([]),
                ]);
        }

        return $form->schema([
            Grid::make()
                ->columns(12)
                ->schema([
                    // Left Column
                    Grid::make()
                        ->columns(2)
                        ->columnSpan(5)
                        ->schema([
                            Forms\Components\Section::make()
                                ->columns(3)
                                ->columnSpan(2)
                                ->schema([
                                    // Hidden field for 'added_by' (keep if necessary)
                                    Hidden::make('added_by')
                                        ->default(Auth::id()),

                                    TextInput::make('name')
                                        ->label('')
                                        ->columnSpan(2)
                                        ,

                                    Forms\Components\Section::make()
                                        ->columns(2)
                                        ->columnSpan(1)
                                        ->schema([
                                            Checkbox::make('fast_track')
                                                ->label('FastTrack'),
                                            Checkbox::make('is_paid')
                                                ->label('Achitat'),
                                        ]),
                                    Card::make()
                                    ->columnSpan(3)
                                    ->columns(4)
                                    ->schema([
                                        DatePicker::make('deadline')
                                        ->label('Deadline')
                                        ->columnSpan(1),
                                    Select::make('assigned_to')
                                        ->label('Responsabil')
                                        ->relationship('owner', 'name')->disabled(),
                                    Select::make('support_users')
                                        ->label('Suport')
                                        ->multiple()
                                        ->relationship('supportUsers', 'name')
                                        ->disabled(fn ($record) => !auth()->user()->isAdmin() && !auth()->user()->isOwner($record))
                                        ->columnSpan(2),
                                    ]),
                                    RichEditor::make('content')
                                        ->label('Content')
                                        ->columnSpan(3),

                                    TextInput::make('price')
                                        ->label('Pret')
                                        ->numeric()
                                        ->columnSpan(1)
                                        ->visible(fn ($record) => auth()->user()->isAdmin() || auth()->user()->isOwner($record)),
                                        
                                ]),
                        ]),
                    // Right Column
                    Grid::make()
                        ->columns(7)
                        ->columnSpan(7)
                        ->schema([
                            Card::make('')
                                ->columns(8)
                                ->label('')
                                ->columnSpan(4)
                                ->schema([

                                    ViewField::make('channelIcon')
                                       // ->label('Existing Files')
                                        ->view('components.channel-icon')
                                       // ->visible(fn (callable $get) => !empty($get('channel')))
                                        ->viewData([
                                        'channel' => $this->record->identifierRecord->channel
                                        ])->columnSpan(1)
                                        ,
                                    

                                    Placeholder::make('clientName')
                                        ->label('')
                                        ->content(fn ($record) => $record->identifierRecord?->clientName ?? '')
                                        ->columnSpan(4),

                                    Placeholder::make('arrivedAt')
                                        ->label('')
                                        ->content(fn ($record) => $record->identifierRecord?->arrivedAt ?? '')
                                        ->columnSpan(3),
                                ]),
                                Card::make()
                                ->schema([
                                    
                                        ViewField::make('listFiles')
                                        ->label('Existing Files')
                                        ->view('components.list-files')
                                        ->viewData([
                                            'recordId' => $this->record->id,
                                        ]),
                                
                                        FileUpload::make('uploadedFiles')
                                        ->label('Încarcă fișiere')
                                        ->disk('google')
                                        ->directory(fn($get, $state, $record) => "CRMcrm/tasks/{$record->id}")
                                        ->multiple()
                                        ->preserveFilenames(),
                                ])
                                ->columnSpan(4),
                          
                            
                            Hidden::make('stage_id')
                                ->default(null)
                                ->disabled(),
                            Wizard::make($wizardSteps)
                                ->skippable()
                                ->columns(1)
                                ->columnSpan(7), // store current step index
                                // Use a hidden field to set stage_id based on the wizard step index
                                Hidden::make('stage_id')
                                    ->dehydrated()
                                    ->default(function (callable $get) use ($stages) {
                                        $currentStepIndex = $get('wizard_current_step') ?? 0; 
                                        return $stages[$currentStepIndex]->id ?? null;
                                    }),
                        ]),
                ]),
        ]);
    }



    protected function mutateFormDataBeforeFill(array $data): array
    {
        $data['identifier.title'] = $this->record->identifier->title ?? '';
        $data['identifier.description'] = $this->record->identifier->description ?? '';




                // Load all stages (with substages if needed)
                $stages = Stage::with('substages')->get();

                // Initialize an array keyed by stage_id for our form data
                $activeSubstagesGrouped = [];
                foreach ($stages as $stage) {
                    $activeSubstagesGrouped[$stage->id] = [];
                }
        
                // Populate the arrays with existing pivot data from activeSubstages
                foreach ($this->record->activeSubstages as $activeSubstage) {
                    $stageId = $activeSubstage->stage->id;
                    $activeSubstagesGrouped[$stageId][] = [
                        'substage_id' => $activeSubstage->id,
                        'is_complete' => $activeSubstage->pivot->is_complete,
                    ];
                }
        
                // Add this data to the form data array
                $data['activeSubstagesByStage'] = $activeSubstagesGrouped;

        return $data;


    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        if (isset($data['identifier'])) {
            $identifierData = $data['identifier'];
            unset($data['identifier']);

            $identifier = $this->record->identifier;
            if ($identifier) {
                $identifier->update($identifierData);
            } else {
                $identifier = Identifier::create($identifierData);
                $this->record->identifier()->associate($identifier);
            }
            $data['identifier'] = $identifier->id;
        }

        $allActiveSubstages = [];
        if (isset($data['activeSubstagesByStage'])) {
            foreach ($data['activeSubstagesByStage'] as $stageId => $substagesData) {
                foreach ($substagesData as $substageData) {
                    if (isset($substageData['substage_id'])) {
                        $allActiveSubstages[$substageData['substage_id']] = [
                            'is_complete' => $substageData['is_complete'] ?? false,
                        ];
                    }
                }
            }
        }

        // Remove the custom data so it won't attempt to save directly to the model
        unset($data['activeSubstagesByStage']);

        // Store this array temporarily so we can use it in afterSave()
        $this->allActiveSubstagesData = $allActiveSubstages;

        $this->supportUsersToAttach = $data['support_users'] ?? [];
        unset($data['support_users']);

         // Determine the maximum stage_id of completed substages
    $maxStageId = null;
    if (!empty($allActiveSubstages)) {
        // Load all substage IDs at once to minimize queries
        $completedSubstages = array_filter($allActiveSubstages, fn($pivot) => $pivot['is_complete'] === true);
        if (!empty($completedSubstages)) {
            $substageIds = array_keys($completedSubstages);
            // Fetch all completed Substages with their stages
            $completedSubstageModels = \App\Models\Substage::whereIn('id', $substageIds)->with('stage')->get();
            
            foreach ($completedSubstageModels as $sub) {
                $stageId = $sub->stage_id;
                if (is_null($maxStageId) || $stageId > $maxStageId) {
                    $maxStageId = $stageId;
                }
            }
        }
    }

    // If we found a max stage_id, set it on the task data
    if (!is_null($maxStageId)) {
        $data['stage_id'] = $maxStageId;
    }


        return $data;
    }


    protected function getExistingFiles()
{
    if (!$this->record) {
        return [];
    }
    
    $directory = "CRMcrm/tasks/{$this->record->id}";
    try {
        $files = Gdrive::all($directory, false);
        dd($files);
        return $files;
    } catch (\Exception $e) {
        Log::error('Error listing Google Drive files: ' . $e->getMessage());
        return [];
    }
}


//     protected function afterSave()
//     {
//         dd($this->form->getState()['uploadedFiles']);
//       //  parent::afterSave();

//         $uploadedFiles = $this->form->getState()['uploadedFiles'] ?? [];
//         $googleFileIds = $this->form->getState()['uploadedFileIds'] ?? [];
    
//         foreach ($uploadedFiles as $path) {
//             File::create([
//                 'task_id' => $this->record->id,
//                 'filename' => basename($path),
//                 'filepath' => $path,  // logical path
//                 'file_id' => $googleFileIds[$path] ?? null, // real Google Drive file ID
//             ]);
//         }
    
//         // Clear them out if you like 
//         $this->form->fill([
//             'uploadedFiles' => [],
//             'uploadedFileIds' => [],
//         ]);
// }
}