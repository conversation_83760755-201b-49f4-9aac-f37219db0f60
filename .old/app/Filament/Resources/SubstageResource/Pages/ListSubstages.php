<?php

namespace App\Filament\Resources\SubstageResource\Pages;

use App\Filament\Resources\SubstageResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListSubstages extends ListRecords
{
    protected static string $resource = SubstageResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
