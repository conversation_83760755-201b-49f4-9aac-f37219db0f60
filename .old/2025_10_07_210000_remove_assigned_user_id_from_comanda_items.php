<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('comanda_items', function (Blueprint $table) {
            // Drop the index first
            $table->dropIndex(['assigned_user_id', 'status']);
            
            // Drop the foreign key constraint
            $table->dropForeign(['assigned_user_id']);
            
            // Drop the column
            $table->dropColumn('assigned_user_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('comanda_items', function (Blueprint $table) {
            // Re-add the column
            $table->foreignId('assigned_user_id')->nullable()->after('completion_percentage')->constrained('users')->onDelete('set null');
            
            // Re-add the index
            $table->index(['assigned_user_id', 'status']);
        });
    }
};

