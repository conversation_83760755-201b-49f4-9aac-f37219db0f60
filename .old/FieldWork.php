<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Guava\Calendar\Contracts\Eventable;
use Guava\Calendar\ValueObjects\Event;

class FieldWork extends Model implements Eventable
{
    use SoftDeletes;

    protected $fillable = [
        'title',
        'start_date',
        'end_date',
        'created_by',
        'task_id',
        'details',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
    ];

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function task(): BelongsTo
    {
        return $this->belongsTo(Task::class);
    }

    public function assignees(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'field_work_user', 'field_work_id', 'user_id');
    }

    public function toEvent(): Event|array
    {   
        $startFormatted = $this->start_date->addHours(24)->setTimezone('Europe/Bucharest')->format('Y-m-d\T00:00:00');
        $endFormatted = $this->end_date->addHours(2)->setTimezone('Europe/Bucharest')->format('Y-m-d\T23:59:59');
        
        return Event::make($this)
            ->title($this->title)
            ->start($startFormatted)
            ->end($endFormatted)
            ->allDay(true)
            ->backgroundColor('#F59E0B')
            ->extendedProps([
                'type' => 'fieldwork',
                'assignees' => $this->assignees->pluck('name'),
                'details' => $this->details,
            ]);
    }
}
