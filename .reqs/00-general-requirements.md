# Caramel CRM - General Requirements

## Overview
Caramel is a streamlined CRM application specifically designed for the Romanian market, targeting a niche public. Built with Laravel 12 and Filament 4, it focuses on order management ("comenzi") with a structured workflow system.

## Technology Stack
- **Backend**: Lara<PERSON> 12
- **Admin Interface**: Filament 4
- **Database**: MySQL/PostgreSQL
- **File Storage**: Google Drive integration
- **Document Generation**: PDF generation capabilities

## Core Architecture

### Filament Panels Structure
1. **App Panel** - Main operational interface for managing comenzi
2. **Definitions Panel** - Master data management (clients, document templates)
3. **PM Dashboard Panel** - Project Manager specific views and controls
4. **Admin Panel** - System administration and configuration

### Core Models Hierarchy

#### Primary Model: Comandă (Order)
- **Identification**: Internal number, UUID, name
- **Client Relationship**: Belongs to single client (from partners/companies)
- **Content**: Description, item list with prices, total value
- **Timing**: Arrival time, arrival channel, deadline
- **Workflow**: 7-stage progression with substages/activities
- **Assignment**: Owner (PM), contributors (specialists)
- **Flags**: Fast-track boolean, priority boolean
- **Files**: Google Drive integration, image uploads
- **Contact**: Client-side contact person

#### Supporting Models
- **Clients/Partners**: Romanian commercial data requirements
- **Users**: Role-based access (Superadmin, Managers, PMs, Specialists)
- **Documents**: Proformas, invoices, contracts, bills, notes
- **Document Templates**: HTML/CSS visual templates
- **Activities/Substages**: Workflow components with approval requirements

## User Roles & Permissions

### Superadmin (Developer)
- Full system access
- Admin panel access
- System maintenance capabilities

### Managers
- Assign comenzi to PMs
- Access all panels except admin
- Contribute to any comandă
- View all pricing information

### Project Managers (PMs)
- Own and manage assigned comenzi
- Assign contributors to substages/activities
- Approve/sign off on completed activities
- Contribute to others' comenzi
- **Restriction**: Cannot see pricing of others' comenzi

### Specialists
- Assigned to specific substages/activities
- Mark assigned work as complete
- Kanban-style workload view in app panel
- **Restriction**: Cannot see pricing information

## Key Features

### Workflow Management
- 7-stage comandă progression
- Configurable substages/activities per stage
- Assignment system for contributors
- Approval/sign-off requirements
- Progress tracking and reporting

### Document Management
- Template-based document generation
- PDF export capabilities
- Database storage for editability
- Integration with comandă data
- Client information cross-referencing

### File Management
- Google Drive integration
- Organized by comandă UUID
- Image upload support
- Secure access controls

### Client Management
- Romanian commercial requirements compliance
- Centralized in definitions panel
- Cross-referenced throughout system

## Technical Considerations

### Laravel 12 Features to Leverage
- Enhanced performance optimizations
- Improved queue system
- Advanced validation features
- Better testing capabilities

### Filament 4 Features to Utilize
- **Nested Resources**: For comandă substages/activities
- Enhanced table builders
- Improved form components
- Advanced notification system
- Better role/permission integration
- Kanban board components for specialist view

### Integration Requirements
- Google Drive API for file storage
- PDF generation library
- Romanian localization support
- Responsive design for mobile access

## Development Phases

### Phase 1: Core Foundation
- User authentication and roles
- Basic comandă CRUD operations
- Client management
- File upload infrastructure

### Phase 2: Workflow System
- 7-stage workflow implementation
- Substage/activity management
- Assignment and approval system
- Progress tracking

### Phase 3: Document System
- Template management
- Document generation
- PDF export functionality
- Integration with comandă data

### Phase 4: Advanced Features
- Kanban specialist view
- Advanced reporting
- Google Drive integration
- Performance optimizations

## Success Criteria
- Streamlined order management process
- Clear role-based access control
- Efficient workflow progression
- Reliable document generation
- Intuitive user experience across all panels
