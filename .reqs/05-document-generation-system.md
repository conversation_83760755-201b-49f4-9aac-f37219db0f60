# Document Generation System Specification

## Overview
The document generation system uses custom Blade templates with Page.js styling, processed through an external Gotenberg instance for professional PDF generation. This approach provides maximum flexibility for Romanian commercial document requirements while maintaining high-quality output.

## Architecture Overview

### Generation Flow
```
Blade Template + Data → Rendered HTML → Gotenberg → Professional PDF
     ↓                      ↓              ↓            ↓
Custom Styling         Page.js CSS    External API   Database Storage
                                                           ↓
                                                   Google Drive Backup
```

## Document Types & Templates

### Standard Document Types
```
Document Types:
├── Proformas (Proforma Invoices)
│   ├── Template: proforma.blade.php
│   ├── Data Source: comandă + client + items
│   └── Romanian Requirements: CUI, Reg Com, VAT details
├── Invoices (Facturi)
│   ├── Template: invoice.blade.php
│   ├── Data Source: comandă + client + items + payments
│   └── Romanian Requirements: Full fiscal compliance
├── Contracts (Contracte)
│   ├── Template: contract.blade.php
│   ├── Data Source: comandă + client + custom terms
│   └── Romanian Requirements: Legal compliance, signatures
├── Guarantee Certificates (Certificate de Garantie)
│   ├── Template: guarantee.blade.php
│   ├── Data Source: comandă + client + warranty terms
│   └── Romanian Requirements: Consumer protection law
├── Proces Verbal de Predare/Primire
│   ├── Template: proces_verbal.blade.php
│   ├── Data Source: comandă + client + delivery details
│   └── Romanian Requirements: Legal handover documentation
└── Bills & Notes (Diverse)
    ├── Template: generic_bill.blade.php
    ├── Data Source: configurable
    └── Romanian Requirements: Basic commercial format
```

## Template System Architecture

### DocumentTemplate Model
```php
DocumentTemplate {
    // Identification
    id: bigint (primary key)
    uuid: string (unique)
    name: string
    type: enum ['proforma', 'invoice', 'contract', 'guarantee', 'proces_verbal', 'bill', 'note']
    
    // Template Content
    blade_template: string (filename without .blade.php)
    css_framework: string (default: 'page.js')
    custom_css: text (additional styling)
    
    // Configuration
    is_active: boolean (default: true)
    is_default: boolean (one per type)
    requires_approval: boolean (for generation)
    
    // Data Mapping
    required_fields: json (list of required data fields)
    optional_fields: json (list of optional data fields)
    custom_fields: json (template-specific fields)
    
    // Romanian Compliance
    fiscal_requirements: json (tax/legal requirements)
    language: string (default: 'ro')
    
    // Metadata
    created_by: bigint (user_id)
    updated_by: bigint (user_id)
    version: integer (template versioning)
    
    // Timestamps
    created_at: timestamp
    updated_at: timestamp
}
```

### Generated Document Model
```php
GeneratedDocument {
    // Identification
    id: bigint (primary key)
    uuid: string (unique)
    document_number: string (auto-generated, type-specific)
    
    // Relationships
    comandă_id: bigint (foreign key)
    template_id: bigint (foreign key)
    client_id: bigint (foreign key)
    
    // Document Info
    type: enum (same as template types)
    title: string
    description: text (nullable)
    
    // Generation Data
    generated_data: json (snapshot of data used)
    custom_field_values: json (user-filled custom fields)
    
    // File Management
    html_content: longtext (rendered HTML)
    pdf_path: string (Google Drive path)
    pdf_size: integer (bytes)
    pdf_generated_at: timestamp
    
    // Status & Workflow
    status: enum ['draft', 'generated', 'sent', 'signed', 'archived']
    generated_by: bigint (user_id)
    approved_by: bigint (user_id, nullable)
    approved_at: timestamp (nullable)
    
    // Romanian Compliance
    fiscal_data: json (tax-related information)
    legal_requirements_met: boolean
    
    // Timestamps
    created_at: timestamp
    updated_at: timestamp
}
```

## Blade Template Structure

### Base Template Layout
```blade
{{-- resources/views/documents/layouts/base.blade.php --}}
<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $document_title }}</title>
    
    {{-- Page.js CSS Framework --}}
    <link rel="stylesheet" href="{{ asset('css/page.js') }}">
    
    {{-- Custom Document Styling --}}
    <style>
        @page {
            size: A4;
            margin: 2cm;
        }
        
        .document-header {
            border-bottom: 2px solid #333;
            padding-bottom: 1rem;
            margin-bottom: 2rem;
        }
        
        .company-info {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }
        
        .client-info {
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 0.5rem;
            margin: 1rem 0;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 2rem 0;
        }
        
        .items-table th,
        .items-table td {
            border: 1px solid #dee2e6;
            padding: 0.75rem;
            text-align: left;
        }
        
        .items-table th {
            background-color: #e9ecef;
            font-weight: bold;
        }
        
        .totals-section {
            margin-top: 2rem;
            text-align: right;
        }
        
        .document-footer {
            margin-top: 3rem;
            border-top: 1px solid #dee2e6;
            padding-top: 1rem;
            font-size: 0.9rem;
            color: #6c757d;
        }
        
        {{-- Custom CSS from template --}}
        {!! $template->custom_css ?? '' !!}
    </style>
</head>
<body>
    <div class="document-container">
        @yield('content')
    </div>
</body>
</html>
```

### Proforma Template Example
```blade
{{-- resources/views/documents/proforma.blade.php --}}
@extends('documents.layouts.base')

@section('content')
<div class="document-header">
    <div class="company-info">
        <div class="company-details">
            <h1>{{ config('company.name') }}</h1>
            <p>{{ config('company.address') }}</p>
            <p>CUI: {{ config('company.cui') }}</p>
            <p>Reg. Com: {{ config('company.reg_com') }}</p>
        </div>
        <div class="document-meta">
            <h2>PROFORMA</h2>
            <p><strong>Nr:</strong> {{ $document->document_number }}</p>
            <p><strong>Data:</strong> {{ $document->created_at->format('d.m.Y') }}</p>
        </div>
    </div>
</div>

<div class="client-info">
    <h3>Client:</h3>
    <p><strong>{{ $client->company_name ?? $client->name }}</strong></p>
    <p>{{ $client->address }}</p>
    @if($client->cui)
        <p>CUI: {{ $client->cui }}</p>
    @endif
    @if($client->reg_com)
        <p>Reg. Com: {{ $client->reg_com }}</p>
    @endif
</div>

<div class="comandă-info">
    <h3>Comandă: {{ $comandă->name }}</h3>
    <p>{{ $comandă->description }}</p>
</div>

@if($comandă->items->count() > 0)
<table class="items-table">
    <thead>
        <tr>
            <th>Nr. Crt.</th>
            <th>Descriere</th>
            <th>U.M.</th>
            <th>Cantitate</th>
            <th>Preț Unitar</th>
            <th>Valoare</th>
        </tr>
    </thead>
    <tbody>
        @foreach($comandă->items as $index => $item)
        <tr>
            <td>{{ $index + 1 }}</td>
            <td>{{ $item->description }}</td>
            <td>{{ $item->unit ?? 'buc' }}</td>
            <td>{{ $item->quantity }}</td>
            <td>{{ number_format($item->unit_price, 2) }} RON</td>
            <td>{{ number_format($item->total_price, 2) }} RON</td>
        </tr>
        @endforeach
    </tbody>
</table>

<div class="totals-section">
    <p><strong>Subtotal: {{ number_format($comandă->subtotal, 2) }} RON</strong></p>
    @if($comandă->vat_amount > 0)
        <p>TVA ({{ $comandă->vat_rate }}%): {{ number_format($comandă->vat_amount, 2) }} RON</p>
    @endif
    <h3>Total: {{ number_format($comandă->total_value, 2) }} RON</h3>
</div>
@endif

{{-- Custom fields from template --}}
@if(isset($custom_fields) && count($custom_fields) > 0)
<div class="custom-fields">
    @foreach($custom_fields as $field => $value)
        <p><strong>{{ ucfirst(str_replace('_', ' ', $field)) }}:</strong> {{ $value }}</p>
    @endforeach
</div>
@endif

<div class="document-footer">
    <p>Proforma valabilă 30 de zile de la data emiterii.</p>
    <p>Document generat automat de sistemul CRM Caramel.</p>
</div>
@endsection
```

## Gotenberg Integration

### Service Class Structure
```php
class DocumentGenerationService
{
    protected string $gotenbergUrl;
    
    public function __construct()
    {
        $this->gotenbergUrl = config('services.gotenberg.url');
    }
    
    public function generatePdf(GeneratedDocument $document): string
    {
        // 1. Render Blade template with data
        $html = $this->renderTemplate($document);
        
        // 2. Send to Gotenberg
        $pdfContent = $this->callGotenberg($html);
        
        // 3. Store in Google Drive
        $path = $this->storeInGoogleDrive($pdfContent, $document);
        
        // 4. Update document record
        $document->update([
            'pdf_path' => $path,
            'pdf_size' => strlen($pdfContent),
            'pdf_generated_at' => now(),
            'status' => 'generated'
        ]);
        
        return $path;
    }
    
    protected function renderTemplate(GeneratedDocument $document): string
    {
        $template = $document->template;
        $comandă = $document->comandă;
        $client = $document->client;
        
        return view("documents.{$template->blade_template}", [
            'document' => $document,
            'template' => $template,
            'comandă' => $comandă,
            'client' => $client,
            'custom_fields' => $document->custom_field_values
        ])->render();
    }
    
    protected function callGotenberg(string $html): string
    {
        $response = Http::post("{$this->gotenbergUrl}/forms/chromium/convert/html", [
            'files' => [
                'index.html' => $html
            ],
            'marginTop' => '2cm',
            'marginBottom' => '2cm',
            'marginLeft' => '2cm',
            'marginRight' => '2cm',
            'format' => 'A4',
            'printBackground' => true
        ]);
        
        return $response->body();
    }
}
```

### Gotenberg Configuration
```yaml
# docker-compose.yml addition
gotenberg:
  image: gotenberg/gotenberg:7
  ports:
    - "3000:3000"
  command:
    - "gotenberg"
    - "--chromium-disable-web-security"
    - "--chromium-allow-list=file:///*"
```

## Document Generation Workflow

### Generation Process
```
1. User selects document type and comandă
2. System loads appropriate template
3. User fills custom fields (if any)
4. System renders Blade template with data
5. HTML sent to Gotenberg for PDF conversion
6. PDF stored in Google Drive
7. Document record created/updated in database
8. User receives download link
```

### Integration Points

#### From App Panel
- Document generation buttons in comandă edit view
- Quick generation from comandă table actions
- Bulk document generation for multiple comenzi

#### From Definitions Panel
- Template management interface
- Template preview and testing
- Custom field configuration

#### API Endpoints
```php
// Document generation routes
Route::post('/documents/generate', [DocumentController::class, 'generate']);
Route::get('/documents/{uuid}/download', [DocumentController::class, 'download']);
Route::get('/documents/{uuid}/preview', [DocumentController::class, 'preview']);
```

This system provides professional document generation with Romanian compliance while maintaining flexibility for custom requirements and high-quality PDF output through Gotenberg.
