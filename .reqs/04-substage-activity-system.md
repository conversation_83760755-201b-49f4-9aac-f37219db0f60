# Substage/Activity System Specification

## Overview
The substage/activity system forms the core workflow engine of the CRM. Each of the 7 main stages contains specific substages that can be assigned to specialists. These substages appear as tasks in the Productie panel's kanban board and share common file access with their parent comandă.

## Stage-Specific Substages Configuration

### Stage 1: Ofertare (Quotation)
```
Available Substages:
├── Masuratori (Measurements)

├── Design / mockups

├── Cost Calculation

└── Quote Preparation

```

### Stage 2: Contractare (Contracting)
```
Available Substages:
├── Contract Draft

├── Terms Finalization

├── Legal Review

└── Contract Signing

```

### Stage 3: Pregatire (Preparation)
```
Available Substages:
├── Project Planning

├── Resource Allocation

├── Material Specifications

└── Timeline Establishment

└── Technical Drawings

```

### Stage 4: Aprovizionare (Procurement)
```
Available Substages:
├── Material Ordering

├── Supplier Coordination

└── Quality Verification

```

### Stage 5: Executie (Execution)
```
Available Substages:


├── Processing [machine / tool] 

├── Manufacturing Processes

├── Assembly Operations

├── Quality Control


```

### Stage 6: Livrare (Delivery)
```
Available Substages:
├── Final Inspection

├── Packaging

├── Transportation

└── Installation (if applicable)

└── Post-delivery receipt / papers (if applicable)

```

### Stage 7: Facturare (Invoicing)
```
Available Substages:
├── Invoice Preparation

├── Documentation Completion

├── Payment Processing

└── Project Closure

└── Payment Follow-up
    
```

## Comandă Items Architecture

### Default Item Strategy
**Decision**: Always create a default item for every comandă to maintain consistent data structure.

#### Implementation for Simple Services
```php
// When comandă is created without explicit items, auto-create default item:
ComandaItem::create([
    'comanda_id' => $comanda->id,
    'description' => $comanda->name, // Use comandă name as description
    'quantity' => 1,
    'unit_price' => $comanda->total_value,
    'total_price' => $comanda->total_value,
    'unit' => 'serviciu',
    'is_default_item' => true // Flag to identify auto-generated items
]);
```

#### Benefits
- Consistent data structure across all comandă types
- Simplified document generation (invoices, proformas)
- Easier reporting and financial calculations
- Uniform activity association logic

## Activity Association Architecture

### Flexible Activity Scoping
Activities can be associated with either the entire comandă or specific items within the comandă.

#### Scope Types
- **Comandă Level**: Activities that affect the entire project (e.g., contract negotiation, final invoicing)
- **Item Level**: Activities specific to individual items (e.g., CNC cutting for specific components, measurements for individual pieces)

#### Stage Scope Configuration
```php
const STAGE_SCOPES = [
    1 => [ // Ofertare
        'masuratori' => 'both', // Can be comandă or item level
        'cost_calculation' => 'item', // Always item-specific
        'technical_specifications' => 'item',
        'quote_preparation' => 'comanda', // Always comandă level
    ],
    2 => [ // Contractare
        'contract_negotiation' => 'comanda', // Always comandă level
        'terms_finalization' => 'comanda',
        'legal_review' => 'comanda',
        'contract_signing' => 'comanda',
    ],
    3 => [ // Pregatire
        'project_planning' => 'comanda',
        'resource_allocation' => 'both',
        'material_specifications' => 'item',
        'timeline_establishment' => 'comanda',
    ],
    4 => [ // Aprovizionare
        'material_ordering' => 'item',
        'supplier_coordination' => 'both',
        'delivery_scheduling' => 'comanda',
        'quality_verification' => 'item',
    ],
    5 => [ // Executie
        'debitare_cnc' => 'item', // Always item-specific
        'manufacturing_processes' => 'item',
        'assembly_operations' => 'both',
        'quality_control' => 'item',
        'progress_monitoring' => 'comanda',
    ],
    6 => [ // Livrare
        'final_inspection' => 'both',
        'packaging' => 'item',
        'transportation' => 'comanda',
        'installation' => 'comanda',
    ],
    7 => [ // Facturare
        'invoice_preparation' => 'comanda', // Always comandă level
        'documentation_completion' => 'comanda',
        'payment_processing' => 'comanda',
        'project_closure' => 'comanda',
    ]
];
```

## Activity/Substage Data Model

### Enhanced ComandaActivity Model Structure
```php
ComandaActivity {
    // Identification
    id: bigint (primary key)
    comandă_id: bigint (foreign key)
    uuid: string (unique)

    // Stage & Type
    stage: integer (1-7)
    substage_type: string (from predefined list)
    name: string
    description: text

    // Activity Scoping (NEW)
    scope: enum ['comanda', 'item'] (default: 'comanda')
    comanda_item_id: bigint (foreign key, nullable) // Links to specific item if scope = 'item'

    // Assignment & Ownership
    assigned_to: bigint (user_id, nullable)
    assigned_by: bigint (user_id)
    assigned_at: timestamp (nullable)

    // Approval System
    requires_approval: boolean (default: false)
    approved_by: bigint (user_id, nullable)
    approved_at: timestamp (nullable)
    approval_notes: text (nullable)

    // Status & Progress
    status: enum ['pending', 'assigned', 'in_progress', 'completed', 'approved', 'rejected']
    progress_percentage: integer (0-100)
    started_at: timestamp (nullable)
    completed_at: timestamp (nullable)

    // Time Tracking
    estimated_duration: integer (minutes) - hidden for now, nullable
    actual_duration: integer (minutes, nullable) - hidden for now, nullable

    // Custom Fields (JSON)
    custom_fields: json (stage-specific fields)

    // Notes & Communication
    notes: text (nullable)
    internal_notes: text (nullable, PM/Manager only)

    // Timestamps
    created_at: timestamp
    updated_at: timestamp
}
```

### Relationships
```php
// ComandaActivity relationships
belongsTo(Comandă::class, 'comandă_id')
belongsTo(ComandaItem::class, 'comanda_item_id') // NEW: For item-level activities
belongsTo(User::class, 'assigned_to', 'assignee')
belongsTo(User::class, 'assigned_by', 'assigner')
belongsTo(User::class, 'approved_by', 'approver')
hasMany(ActivityFile::class) // Shared with comandă files
hasMany(ActivityComment::class)
hasMany(ActivityTimeLog::class)

// Helper methods
public function isComandaLevel(): bool
{
    return $this->scope === 'comanda';
}

public function isItemLevel(): bool
{
    return $this->scope === 'item';
}

public function scopeComandaLevel($query)
{
    return $query->where('scope', 'comanda')->whereNull('comanda_item_id');
}

public function scopeItemLevel($query)
{
    return $query->where('scope', 'item')->whereNotNull('comanda_item_id');
}
```

## File Management Integration

### Shared File System
- **Common Storage**: All files uploaded to activities are also available at comandă level
- **Google Drive Structure**: `/CRM_Files/[comandă_uuid]/[activity_type]/filename`
- **Access Control**: Files visible to all team members working on the comandă
- **Version Control**: Automatic versioning for updated files

### File Categories
```
File Types per Activity:
├── Input Files (required to start)
├── Work Files (created during execution)
├── Output Files (deliverables)
└── Reference Files (supporting documentation)
```

## Kanban Integration

### Task Card Data Mapping
```
Kanban Task Card = ComandaActivity {
    Title: activity.name
    Comandă Reference: comandă.internal_number + comandă.name
    Client: comandă.client.name
    Due Date: calculated from comandă.deadline and stage position
    Priority: inherited from comandă (fast_track, priority)
    Files Count: count of associated files
    Status: activity.status
    Estimated Time: activity.estimated_duration
}
```

### Status Flow in Kanban
```
To Do (pending/assigned) → In Progress (in_progress) → 
Review (completed, awaiting approval) → Complete (approved)
```

## Approval Workflow

### Approval Requirements
- **Configurable per substage type**: Some activities require PM approval
- **Automatic for critical stages**: Quality control, final inspections
- **Optional for routine tasks**: Progress monitoring, documentation

### Approval Process
1. Specialist marks activity as completed
2. If approval required → Status: "completed" (awaiting approval)
3. PM receives notification
4. PM reviews work and files
5. PM approves/rejects with notes
6. If approved → Status: "approved"
7. If rejected → Status: "in_progress" with feedback

## Enhanced ComandaItem Model

### Updated ComandaItem Structure
```php
ComandaItem {
    // Existing fields...
    id: bigint (primary key)
    comanda_id: bigint (foreign key)
    description: string
    unit: string (default: 'buc')
    quantity: decimal(8,2)
    unit_price: decimal(10,2)
    total_price: decimal(10,2)
    specifications: text (nullable)
    notes: text (nullable)
    sort_order: integer

    // NEW: Default item handling
    is_default_item: boolean (default: false) // Identifies auto-generated items

    // Timestamps
    created_at: timestamp
    updated_at: timestamp
}

// Relationships
hasMany(ComandaActivity::class) // NEW: Item-specific activities
belongsTo(Comandă::class)
```

### Default Item Logic
```php
// In Comandă model
protected static function booted()
{
    static::created(function ($comandă) {
        // Always create a default item if none provided
        if ($comandă->items()->count() === 0) {
            $comandă->items()->create([
                'description' => $comandă->name,
                'quantity' => 1,
                'unit_price' => $comandă->total_value ?: 0,
                'total_price' => $comandă->total_value ?: 0,
                'unit' => 'serviciu',
                'is_default_item' => true,
            ]);
        }
    });
}
```

This system provides a comprehensive workflow engine that scales from simple tasks to complex multi-stage projects while maintaining clear visibility and control at all levels, with flexible activity scoping for both comandă-level and item-level operations.
