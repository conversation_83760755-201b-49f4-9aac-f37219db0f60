# Caramel CRM - Architecture Overview

## System Architecture

### High-Level Architecture Pattern
Caramel follows a **Multi-Panel Admin Architecture** using Filament 4's panel system, with clear separation of concerns:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   App Panel     │    │ Definitions     │    │ PM Dashboard    │    │  Admin Panel    │
│   (Operations)  │    │   Panel         │    │    Panel        │    │  (System)       │
│                 │    │ (Master Data)   │    │ (Management)    │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │                       │
         └───────────────────────┼───────────────────────┼───────────────────────┘
                                 │                       │
                    ┌─────────────────────────────────────────────────────┐
                    │              Core Laravel Application                │
                    │                                                     │
                    │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐ │
                    │  │   Models    │  │ Controllers │  │  Services   │ │
                    │  └─────────────┘  └─────────────┘  └─────────────┘ │
                    └─────────────────────────────────────────────────────┘
                                         │
                    ┌─────────────────────────────────────────────────────┐
                    │                  Data Layer                         │
                    │                                                     │
                    │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐ │
                    │  │   MySQL     │  │ Google      │  │   Redis     │ │
                    │  │  Database   │  │   Drive     │  │   Cache     │ │
                    │  └─────────────┘  └─────────────┘  └─────────────┘ │
                    └─────────────────────────────────────────────────────┘
```

## Panel Architecture

### 1. App Panel (`/app`) - Operations Hub
**Purpose**: Day-to-day operations, comandă management, workflow execution

**Key Features**:
- Comandă CRUD operations with advanced filtering
- Workflow stage management (7 stages)
- Activity/substage assignment and tracking
- File upload and management
- Specialist kanban view
- Real-time notifications

**Target Users**: All users (with role-based restrictions)

**Resources**:
- `ComandaResource` (main resource with nested activities)
- `ComandaActivityResource` (nested under Comandă)
- `FileResource` (for file management)
- `WorkloadResource` (specialist kanban view)

### 2. Definitions Panel (`/definitions`) - Master Data
**Purpose**: Centralized master data management

**Key Features**:
- Client/Partner management
- Document template management
- System configuration
- Reference data maintenance

**Target Users**: Managers, PMs (read-only for some data)

**Resources**:
- `ClientResource`
- `DocumentTemplateResource`
- `DocumentResource` (with generation capabilities)

### 3. App Panel (`/app`) - Main Operations & PM Dashboard
**Purpose**: Primary interface for all operations and PM management

**Key Features**:
- **Main Page**: Sortable table of all comenzi with:
  - ID number, client name, arrival channel icons
  - Comandă name, visual stage completion (1-7)
  - Action icons: comments, edit, view, delete (role-based)
- **Calendar Page**: Month view with 4 event types:
  - Meetings (hours), Service tours (days)
  - Installations (hours), Deadlines (automatic)
- **Document Generation**: Page group for creating PIs, invoices, contracts
- Comandă CRUD operations with advanced filtering
- Workflow stage management and activity assignment

**Target Users**: All users (with role-based restrictions)

**Resources**:
- `ComandaResource` (main table with stage visualization)
- `CalendarResource` (using Guava Calendar plugin)
- `DocumentGenerationResource` (grouped pages)
- `ComandaActivityResource` (nested under Comandă)

### 4. Productie Panel (`/productie`) - Execution Team Interface
**Purpose**: Simplified interface for execution team specialists

**Key Features**:
- Kanban board view of assigned tasks
- Task-focused interface with minimal distractions
- Relevant information only (no pricing, simplified data)
- Quick status updates and completion marking
- File upload for task-related materials

**Target Users**: Specialists only

**Resources**:
- `TaskKanbanResource` (main kanban board)
- `TaskDetailResource` (focused task view)

### 5. Admin Panel (`/admin`) - System Administration
**Purpose**: System configuration and maintenance

**Key Features**:
- User management and role assignment
- **User Permission Management** (using Filament Shield or similar plugin)
- System settings and configuration
- Template configuration and management
- Audit logs and system monitoring
- Performance monitoring and optimization
- Database maintenance tools

**Target Users**: Superadmin only

**Resources**:
- `UserResource` (enhanced with role management)
- `PermissionResource` (via Filament plugin)
- `RoleResource` (via Filament plugin)
- `SystemSettingsResource`
- `AuditLogResource`
- `DatabaseMaintenanceResource`

## Core Data Model Architecture

### Primary Entity: Comandă
```
Comandă (Orders)
├── Identification Layer
│   ├── id (primary key)
│   ├── uuid (unique identifier)
│   ├── internal_number (auto-generated)
│   └── name
├── Business Layer
│   ├── client_id (foreign key)
│   ├── description
│   ├── total_value (calculated)
│   ├── fast_track (boolean)
│   └── priority (boolean)
├── Workflow Layer
│   ├── stage (1-7)
│   ├── owner_id (PM)
│   ├── deadline
│   └── activities (hasMany)
├── Tracking Layer
│   ├── arrival_time
│   ├── arrival_channel
│   ├── contact_person
│   └── timestamps
└── Relations
    ├── items (hasMany)
    ├── contributors (belongsToMany)
    ├── files (hasMany)
    └── documents (hasMany)
```

### Supporting Entities

#### Client/Partner Model
```
Client
├── Romanian Commercial Data
│   ├── company_name
│   ├── cui (tax identification)
│   ├── reg_com (commercial registry)
│   ├── address
│   ├── bank_details
│   └── legal_representative
├── Contact Information
│   ├── email
│   ├── phone
│   └── contact_persons (hasMany)
└── Relations
    └── comenzi (hasMany)
```

#### User Model (Extended)
```
User
├── Authentication
│   ├── email
│   ├── password
│   └── email_verified_at
├── Profile
│   ├── name
│   ├── role (enum: superadmin, manager, pm, specialist)
│   └── avatar
├── Permissions
│   ├── can_see_prices
│   ├── can_assign_work
│   └── panel_access (array)
└── Relations
    ├── owned_comenzi (hasMany)
    ├── contributed_comenzi (belongsToMany)
    └── activities (hasMany)
```

#### Activity/Substage Model
```
ComandaActivity
├── Identification
│   ├── comandă_id
│   ├── stage (1-7)
│   ├── name
│   └── description
├── Assignment
│   ├── assigned_to (user_id)
│   ├── assigned_by (user_id)
│   └── assigned_at
├── Approval System
│   ├── requires_approval
│   ├── approved_by
│   └── approved_at
├── Status Tracking
│   ├── status (enum: pending, in_progress, completed, approved)
│   ├── started_at
│   └── completed_at
└── Relations
    ├── comandă (belongsTo)
    ├── assignee (belongsTo User)
    └── approver (belongsTo User)
```

## Workflow Architecture

### 7-Stage Workflow System
```
Stage 1: Ofertare (Quotation)
├── Masuratori (Measurements)
├── Cost calculation
├── Technical specifications
└── Quote preparation

Stage 2: Contractare (Contracting)
├── Contract negotiation
├── Terms finalization
├── Legal review
└── Contract signing

Stage 3: Pregatire (Preparation)
├── Project planning
├── Resource allocation
├── Material specifications
└── Timeline establishment

Stage 4: Aprovizionare (Procurement)
├── Material ordering
├── Supplier coordination
├── Delivery scheduling
└── Quality verification

Stage 5: Executie (Execution)
├── Debitare CNC (CNC Cutting)
├── Manufacturing processes
├── Quality control
└── Progress monitoring

Stage 6: Livrare (Delivery)
├── Final inspection
├── Packaging
├── Transportation
└── Installation (if applicable)

Stage 7: Facturare (Invoicing)
├── Invoice preparation
├── Documentation completion
├── Payment processing
└── Project closure
```

### Activity Assignment Flow
```
PM Creates Activity → Assigns to Specialist → Specialist Works → 
Marks Complete → (If approval required) → PM Reviews → 
PM Approves → Activity Closed
```

## Document Generation Architecture

### Template System
```
DocumentTemplate
├── Template Definition
│   ├── type (proforma, invoice, contract, etc.)
│   ├── html_template (with placeholders)
│   ├── css_styles
│   └── required_fields
├── Data Binding
│   ├── comandă_fields (mapped)
│   ├── client_fields (mapped)
│   └── custom_fields
└── Generation Logic
    ├── data_population
    ├── pdf_conversion
    └── storage_handling
```

### Document Types
```
Standard Documents:
├── Proformas (Proforma Invoices)
├── Invoices (Facturi)
├── Contracts (Contracte)
├── Guarantee Certificates (Certificate de garantie)
├── Proces Verbal de Predare/Primire
└── Various bills and notes
```

### Document Generation Flow
```
Select Template → Populate Data → Preview → Generate PDF →
Store in Database → Save to Google Drive → Provide Download Link
```

## Integration Architecture

### Google Drive Integration
```
File Storage Strategy:
/CRM_Files/
├── /[comandă_uuid]/
│   ├── /uploads/
│   ├── /generated_docs/
│   └── /images/
```

### Authentication & Authorization
```
Role-Based Access Control:
├── Panel Access Control
├── Resource-Level Permissions
├── Field-Level Visibility
└── Action-Level Restrictions
```

## Technical Implementation Strategy

### Laravel 12 Features Utilization
- **Enhanced Eloquent**: For complex relationships and queries
- **Improved Queue System**: For document generation and file processing
- **Advanced Validation**: For Romanian commercial data validation
- **Better Testing**: For comprehensive test coverage

### Filament 4 Features Utilization
- **Nested Resources**: ComandaResource with nested activities
- **Advanced Tables**: With custom filters and bulk actions
- **Form Builder**: For complex comandă forms
- **Kanban Boards**: For specialist workload view
- **Notification System**: For workflow updates

### Performance Considerations
- **Database Indexing**: On frequently queried fields
- **Caching Strategy**: Redis for session and query caching
- **Queue Processing**: For heavy operations
- **Lazy Loading**: For related models

## Security Architecture

### Data Protection
- **Role-based field visibility**: Price hiding for specialists
- **Panel access control**: Strict panel-to-role mapping
- **File access control**: Google Drive integration with proper permissions
- **Audit logging**: Track all significant actions

### Romanian Compliance
- **GDPR compliance**: For client data handling
- **Commercial law compliance**: For document generation
- **Tax regulation compliance**: For invoice generation

This architecture provides a solid foundation for implementing the Caramel CRM system with clear separation of concerns, scalable design, and proper utilization of Laravel 12 and Filament 4 features.
