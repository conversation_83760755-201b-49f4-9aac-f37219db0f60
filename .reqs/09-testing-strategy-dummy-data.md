# Testing Strategy & Dummy Data Generation

## Overview
This document outlines a comprehensive testing strategy for the Caramel CRM system and provides specifications for generating realistic dummy data for development and testing purposes.

## Testing Strategy

### Testing Pyramid Structure
```
                    ┌─────────────────┐
                    │   E2E Tests     │ ← Full workflow testing
                    │   (Filament)    │
                    └─────────────────┘
                  ┌───────────────────────┐
                  │  Integration Tests    │ ← API, Database, Services
                  │  (Feature Tests)      │
                  └───────────────────────┘
              ┌─────────────────────────────────┐
              │        Unit Tests               │ ← Models, Services, Helpers
              │     (Pest/PHPUnit)              │
              └─────────────────────────────────┘
```

### Test Categories

#### 1. Unit Tests
**Target**: Individual classes, methods, and functions
**Framework**: Pest PHP
**Coverage**: Models, Services, Helpers, Utilities

```php
// Example: Comandă Model Tests
test('comandă generates UUID on creation', function () {
    $comandă = Comandă::factory()->create();
    expect($comandă->uuid)->toBeString()->toHaveLength(36);
});

test('comand<PERSON> calculates total value from items', function () {
    $comandă = Comandă::factory()->create();
    $comandă->items()->create(['description' => 'Item 1', 'price' => 100]);
    $comandă->items()->create(['description' => 'Item 2', 'price' => 200]);
    
    expect($comandă->fresh()->total_value)->toBe(300.00);
});

test('comandă stage progression validation', function () {
    $comandă = Comandă::factory()->create(['stage' => 1]);
    
    $comandă->stage = 3; // Skip stage 2
    expect(fn() => $comandă->save())->toThrow(ValidationException::class);
});
```

#### 2. Integration Tests
**Target**: Component interactions, database operations, external services
**Framework**: Pest PHP with Laravel testing utilities

```php
// Example: Document Generation Integration
test('document generation creates PDF and stores in Google Drive', function () {
    Storage::fake('google');
    
    $comandă = Comandă::factory()->withItems()->create();
    $template = DocumentTemplate::factory()->proforma()->create();
    
    $document = app(DocumentGenerationService::class)
        ->generateDocument($comandă, $template);
    
    expect($document)->toBeInstanceOf(GeneratedDocument::class);
    expect($document->pdf_path)->toBeString();
    Storage::disk('google')->assertExists($document->pdf_path);
});

test('calendar automatically creates deadline events', function () {
    $deadline = now()->addDays(30);
    $comandă = Comandă::factory()->create(['deadline' => $deadline]);
    
    $this->assertDatabaseHas('calendar_events', [
        'comandă_id' => $comandă->id,
        'type' => CalendarEvent::TYPE_DEADLINE,
        'start_date' => $deadline->startOfDay(),
    ]);
});
```

#### 3. Feature Tests (Filament)
**Target**: Complete user workflows through Filament interface
**Framework**: Pest PHP with Filament testing utilities

```php
// Example: Comandă Management Workflow
test('PM can create and manage comandă', function () {
    $pm = User::factory()->pm()->create();
    
    $this->actingAs($pm)
        ->get('/app/comenzi/create')
        ->assertSuccessful();
    
    $comandăData = [
        'name' => 'Test Kitchen Project',
        'client_id' => Client::factory()->create()->id,
        'description' => 'Complete kitchen renovation',
        'deadline' => now()->addDays(30)->format('Y-m-d'),
    ];
    
    $this->post('/app/comenzi', $comandăData)
        ->assertRedirect();
    
    $this->assertDatabaseHas('comenzi', [
        'name' => 'Test Kitchen Project',
        'owner_id' => $pm->id,
    ]);
});

test('specialist can only see assigned tasks in kanban', function () {
    $specialist = User::factory()->specialist()->create();
    $comandă = Comandă::factory()->create();
    
    // Create assigned activity
    $assignedActivity = ComandaActivity::factory()->create([
        'comandă_id' => $comandă->id,
        'assigned_to' => $specialist->id,
    ]);
    
    // Create unassigned activity
    $unassignedActivity = ComandaActivity::factory()->create([
        'comandă_id' => $comandă->id,
        'assigned_to' => null,
    ]);
    
    $this->actingAs($specialist)
        ->get('/productie/tasks')
        ->assertSee($assignedActivity->name)
        ->assertDontSee($unassignedActivity->name);
});
```

## Dummy Data Generation

### Factory Definitions

#### User Factory
```php
class UserFactory extends Factory
{
    public function definition(): array
    {
        return [
            'name' => $this->faker->name(),
            'email' => $this->faker->unique()->safeEmail(),
            'email_verified_at' => now(),
            'password' => Hash::make('password'),
            'role' => $this->faker->randomElement(['manager', 'pm', 'specialist']),
        ];
    }

    public function superadmin(): static
    {
        return $this->state(['role' => 'superadmin']);
    }

    public function manager(): static
    {
        return $this->state(['role' => 'manager']);
    }

    public function pm(): static
    {
        return $this->state(['role' => 'pm']);
    }

    public function specialist(): static
    {
        return $this->state(['role' => 'specialist']);
    }
}
```

#### Client Factory
```php
class ClientFactory extends Factory
{
    public function definition(): array
    {
        $isCompany = $this->faker->boolean(70); // 70% companies, 30% individuals
        
        return [
            'company_name' => $isCompany ? $this->faker->company() : null,
            'name' => !$isCompany ? $this->faker->name() : null,
            'cui' => $isCompany ? 'RO' . $this->faker->numerify('########') : null,
            'reg_com' => $isCompany ? 'J' . $this->faker->numberBetween(1, 52) . '/' . $this->faker->numerify('####') . '/2024' : null,
            'address' => $this->faker->address(),
            'email' => $this->faker->safeEmail(),
            'phone' => $this->faker->phoneNumber(),
            'contact_person' => $this->faker->name(),
        ];
    }

    public function company(): static
    {
        return $this->state([
            'company_name' => $this->faker->company(),
            'name' => null,
            'cui' => 'RO' . $this->faker->numerify('########'),
            'reg_com' => 'J' . $this->faker->numberBetween(1, 52) . '/' . $this->faker->numerify('####') . '/2024',
        ]);
    }

    public function individual(): static
    {
        return $this->state([
            'company_name' => null,
            'name' => $this->faker->name(),
            'cui' => null,
            'reg_com' => null,
        ]);
    }
}
```

#### Comandă Factory
```php
class ComandăFactory extends Factory
{
    public function definition(): array
    {
        $arrivalChannels = ['email', 'whatsapp', 'website', 'phone', 'walk_in'];
        $stages = [1, 2, 3, 4, 5, 6, 7];
        
        return [
            'uuid' => $this->faker->uuid(),
            'internal_number' => 'C' . str_pad($this->faker->unique()->numberBetween(1, 9999), 4, '0', STR_PAD_LEFT),
            'name' => $this->faker->randomElement([
                'Kitchen Renovation',
                'Bathroom Remodel',
                'Office Furniture',
                'Custom Cabinets',
                'Wardrobe Installation',
                'Living Room Setup',
                'Restaurant Fit-out',
                'Retail Store Design',
            ]),
            'description' => $this->faker->paragraph(),
            'client_id' => Client::factory(),
            'owner_id' => User::factory()->pm(),
            'arrival_time' => $this->faker->dateTimeBetween('-3 months', 'now'),
            'arrival_channel' => $this->faker->randomElement($arrivalChannels),
            'contact_person' => $this->faker->name(),
            'deadline' => $this->faker->optional(0.8)->dateTimeBetween('now', '+6 months'),
            'stage' => $this->faker->randomElement($stages),
            'fast_track' => $this->faker->boolean(20),
            'priority' => $this->faker->boolean(30),
            'total_value' => $this->faker->randomFloat(2, 500, 50000),
        ];
    }

    public function withItems(): static
    {
        return $this->afterCreating(function (Comandă $comandă) {
            $itemCount = $this->faker->numberBetween(1, 8);
            
            for ($i = 0; $i < $itemCount; $i++) {
                ComandaItem::factory()->create([
                    'comandă_id' => $comandă->id,
                ]);
            }
            
            // Recalculate total value
            $comandă->update([
                'total_value' => $comandă->items->sum('total_price')
            ]);
        });
    }

    public function withActivities(): static
    {
        return $this->afterCreating(function (Comandă $comandă) {
            $stageActivities = [
                1 => ['Masuratori', 'Cost Calculation', 'Technical Specifications'],
                2 => ['Contract Negotiation', 'Terms Finalization', 'Legal Review'],
                3 => ['Project Planning', 'Resource Allocation', 'Material Specifications'],
                4 => ['Material Ordering', 'Supplier Coordination', 'Quality Verification'],
                5 => ['Debitare CNC', 'Manufacturing Processes', 'Assembly Operations'],
                6 => ['Final Inspection', 'Packaging', 'Transportation'],
                7 => ['Invoice Preparation', 'Documentation Completion', 'Project Closure'],
            ];
            
            foreach ($stageActivities as $stage => $activities) {
                if ($comandă->stage >= $stage) {
                    foreach ($activities as $activityName) {
                        ComandaActivity::factory()->create([
                            'comandă_id' => $comandă->id,
                            'stage' => $stage,
                            'name' => $activityName,
                            'status' => $comandă->stage > $stage ? 'approved' : 'pending',
                        ]);
                    }
                }
            }
        });
    }
}
```

### Seeder Classes

#### DatabaseSeeder
```php
class DatabaseSeeder extends Seeder
{
    public function run(): void
    {
        $this->call([
            UserSeeder::class,
            ClientSeeder::class,
            ComandăSeeder::class,
            DocumentTemplateSeeder::class,
            CalendarEventSeeder::class,
        ]);
    }
}
```

#### Comprehensive Data Seeder
```php
class ComandăSeeder extends Seeder
{
    public function run(): void
    {
        // Create users first
        $superadmin = User::factory()->superadmin()->create([
            'name' => 'Super Admin',
            'email' => '<EMAIL>',
        ]);

        $managers = User::factory()->manager()->count(2)->create();
        $pms = User::factory()->pm()->count(5)->create();
        $specialists = User::factory()->specialist()->count(8)->create();

        // Create clients
        $companies = Client::factory()->company()->count(15)->create();
        $individuals = Client::factory()->individual()->count(10)->create();

        // Create comenzi with realistic distribution
        $allClients = $companies->merge($individuals);
        
        foreach ($pms as $pm) {
            // Each PM gets 3-8 comenzi
            $comandăCount = fake()->numberBetween(3, 8);
            
            for ($i = 0; $i < $comandăCount; $i++) {
                $comandă = Comandă::factory()
                    ->withItems()
                    ->withActivities()
                    ->create([
                        'owner_id' => $pm->id,
                        'client_id' => $allClients->random()->id,
                    ]);

                // Assign specialists to some activities
                $activities = $comandă->activities()->where('status', '!=', 'approved')->get();
                
                foreach ($activities->random(min(3, $activities->count())) as $activity) {
                    $activity->update([
                        'assigned_to' => $specialists->random()->id,
                        'assigned_by' => $pm->id,
                        'assigned_at' => now(),
                        'status' => fake()->randomElement(['assigned', 'in_progress', 'completed']),
                    ]);
                }
            }
        }

        $this->command->info('Created realistic CRM data structure');
    }
}
```

### Artisan Commands for Data Management

#### Generate Test Data Command
```php
class GenerateTestDataCommand extends Command
{
    protected $signature = 'crm:generate-data {--fresh : Fresh database}';
    protected $description = 'Generate comprehensive test data for CRM';

    public function handle()
    {
        if ($this->option('fresh')) {
            $this->call('migrate:fresh');
        }

        $this->info('Generating test data...');
        
        $this->call('db:seed', ['--class' => 'DatabaseSeeder']);
        
        $this->info('Test data generated successfully!');
        $this->table(
            ['Entity', 'Count'],
            [
                ['Users', User::count()],
                ['Clients', Client::count()],
                ['Comenzi', Comandă::count()],
                ['Activities', ComandaActivity::count()],
                ['Calendar Events', CalendarEvent::count()],
            ]
        );
    }
}
```

This comprehensive testing strategy ensures robust development and provides realistic data for testing all CRM functionality.
