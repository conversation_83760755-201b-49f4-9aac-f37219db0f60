# Calendar Integration Specification

## Overview
The calendar system integrates Guava Calendar with the CRM to provide comprehensive scheduling and deadline management. It supports 4 event types with automatic deadline creation from comandă data and manual event management for meetings, service tours, and installations.

## Calendar Architecture

### Event Type System
```
Calendar Events:
├── Meetings (Manual)
│   ├── Duration: Hours (e.g., 2-4 PM)
│   ├── Color: Blue (#3B82F6)
│   ├── Participants: Internal team + client contacts
│   └── Location: Office/Client site/Online
├── Service Tours (Manual)
│   ├── Duration: Days (00:00 first day to 23:59 last day)
│   ├── Color: Green (#10B981)
│   ├── Team: Assigned specialists
│   └── Route: Multiple client locations
├── Installations (Manual)
│   ├── Duration: Hours (e.g., 9 AM - 5 PM)
│   ├── Color: Orange (#F59E0B)
│   ├── Team: Installation crew
│   └── Equipment: Required tools/materials
└── Deadlines (Automatic)
    ├── Duration: All day event
    ├── Color: Red (#EF4444)
    ├── Source: Comandă deadline field
    └── Auto-sync: Updates when comandă changes
```

## Data Models

### CalendarEvent Model
```php
CalendarEvent extends Model
{
    protected $fillable = [
        'title',
        'description',
        'type',
        'start_date',
        'end_date',
        'all_day',
        'color',
        'comandă_id',
        'client_id',
        'created_by',
        'participants',
        'location',
        'status',
        'metadata'
    ];

    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'all_day' => 'boolean',
        'participants' => 'array',
        'metadata' => 'array',
    ];

    // Event Types
    const TYPE_MEETING = 'meeting';
    const TYPE_SERVICE_TOUR = 'service_tour';
    const TYPE_INSTALLATION = 'installation';
    const TYPE_DEADLINE = 'deadline';

    // Event Status
    const STATUS_SCHEDULED = 'scheduled';
    const STATUS_CONFIRMED = 'confirmed';
    const STATUS_IN_PROGRESS = 'in_progress';
    const STATUS_COMPLETED = 'completed';
    const STATUS_CANCELLED = 'cancelled';

    // Relationships
    public function comandă()
    {
        return $this->belongsTo(Comandă::class);
    }

    public function client()
    {
        return $this->belongsTo(Client::class);
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Scopes
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }

    public function scopeUpcoming($query)
    {
        return $query->where('start_date', '>=', now());
    }

    public function scopeInDateRange($query, $start, $end)
    {
        return $query->whereBetween('start_date', [$start, $end])
                    ->orWhereBetween('end_date', [$start, $end]);
    }
}
```

## Automatic Deadline Integration

### Comandă Model Integration
```php
// Add to Comandă model
class Comandă extends Model
{
    // ... existing code ...

    protected static function booted()
    {
        // Auto-create deadline event when comandă is created with deadline
        static::created(function ($comandă) {
            if ($comandă->deadline) {
                CalendarEvent::create([
                    'title' => "Deadline: {$comandă->name}",
                    'description' => "Comandă deadline for {$comandă->client->name}",
                    'type' => CalendarEvent::TYPE_DEADLINE,
                    'start_date' => $comandă->deadline->startOfDay(),
                    'end_date' => $comandă->deadline->endOfDay(),
                    'all_day' => true,
                    'color' => '#EF4444',
                    'comandă_id' => $comandă->id,
                    'client_id' => $comandă->client_id,
                    'created_by' => auth()->id(),
                    'status' => CalendarEvent::STATUS_SCHEDULED,
                    'metadata' => [
                        'auto_generated' => true,
                        'comandă_internal_number' => $comandă->internal_number,
                    ]
                ]);
            }
        });

        // Update deadline event when comandă deadline changes
        static::updated(function ($comandă) {
            if ($comandă->isDirty('deadline')) {
                // Find existing deadline event
                $deadlineEvent = CalendarEvent::where('comandă_id', $comandă->id)
                    ->where('type', CalendarEvent::TYPE_DEADLINE)
                    ->first();

                if ($comandă->deadline) {
                    if ($deadlineEvent) {
                        // Update existing event
                        $deadlineEvent->update([
                            'start_date' => $comandă->deadline->startOfDay(),
                            'end_date' => $comandă->deadline->endOfDay(),
                            'title' => "Deadline: {$comandă->name}",
                        ]);
                    } else {
                        // Create new deadline event
                        CalendarEvent::create([
                            'title' => "Deadline: {$comandă->name}",
                            'description' => "Comandă deadline for {$comandă->client->name}",
                            'type' => CalendarEvent::TYPE_DEADLINE,
                            'start_date' => $comandă->deadline->startOfDay(),
                            'end_date' => $comandă->deadline->endOfDay(),
                            'all_day' => true,
                            'color' => '#EF4444',
                            'comandă_id' => $comandă->id,
                            'client_id' => $comandă->client_id,
                            'created_by' => auth()->id(),
                            'status' => CalendarEvent::STATUS_SCHEDULED,
                            'metadata' => [
                                'auto_generated' => true,
                                'comandă_internal_number' => $comandă->internal_number,
                            ]
                        ]);
                    }
                } else {
                    // Remove deadline event if deadline is cleared
                    if ($deadlineEvent) {
                        $deadlineEvent->delete();
                    }
                }
            }
        });

        // Clean up deadline event when comandă is deleted
        static::deleted(function ($comandă) {
            CalendarEvent::where('comandă_id', $comandă->id)
                ->where('type', CalendarEvent::TYPE_DEADLINE)
                ->delete();
        });
    }
}
```

## Filament Calendar Resource

### CalendarResource Implementation
```php
class CalendarResource extends Resource
{
    protected static ?string $model = CalendarEvent::class;
    protected static ?string $navigationIcon = 'heroicon-o-calendar';
    protected static ?string $navigationLabel = 'Calendar';
    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\Section::make('Event Details')
                ->schema([
                    Forms\Components\TextInput::make('title')
                        ->required()
                        ->maxLength(255),
                    
                    Forms\Components\Textarea::make('description')
                        ->rows(3),
                    
                    Forms\Components\Select::make('type')
                        ->options([
                            CalendarEvent::TYPE_MEETING => 'Meeting',
                            CalendarEvent::TYPE_SERVICE_TOUR => 'Service Tour',
                            CalendarEvent::TYPE_INSTALLATION => 'Installation',
                        ])
                        ->required()
                        ->reactive()
                        ->afterStateUpdated(fn ($state, callable $set) => 
                            $set('color', match($state) {
                                CalendarEvent::TYPE_MEETING => '#3B82F6',
                                CalendarEvent::TYPE_SERVICE_TOUR => '#10B981',
                                CalendarEvent::TYPE_INSTALLATION => '#F59E0B',
                                default => '#6B7280'
                            })
                        ),
                    
                    Forms\Components\ColorPicker::make('color')
                        ->required(),
                ]),

            Forms\Components\Section::make('Date & Time')
                ->schema([
                    Forms\Components\DateTimePicker::make('start_date')
                        ->required()
                        ->native(false),
                    
                    Forms\Components\DateTimePicker::make('end_date')
                        ->required()
                        ->native(false)
                        ->after('start_date'),
                    
                    Forms\Components\Toggle::make('all_day')
                        ->reactive()
                        ->afterStateUpdated(function ($state, callable $get, callable $set) {
                            if ($state) {
                                $startDate = $get('start_date');
                                if ($startDate) {
                                    $set('start_date', Carbon::parse($startDate)->startOfDay());
                                    $set('end_date', Carbon::parse($startDate)->endOfDay());
                                }
                            }
                        }),
                ]),

            Forms\Components\Section::make('Associations')
                ->schema([
                    Forms\Components\Select::make('comandă_id')
                        ->relationship('comandă', 'name')
                        ->searchable()
                        ->preload()
                        ->reactive()
                        ->afterStateUpdated(function ($state, callable $set) {
                            if ($state) {
                                $comandă = Comandă::find($state);
                                $set('client_id', $comandă->client_id);
                            }
                        }),
                    
                    Forms\Components\Select::make('client_id')
                        ->relationship('client', 'company_name')
                        ->searchable()
                        ->preload(),
                ]),

            Forms\Components\Section::make('Additional Details')
                ->schema([
                    Forms\Components\TextInput::make('location')
                        ->maxLength(255),
                    
                    Forms\Components\TagsInput::make('participants')
                        ->placeholder('Add participant emails'),
                    
                    Forms\Components\Select::make('status')
                        ->options([
                            CalendarEvent::STATUS_SCHEDULED => 'Scheduled',
                            CalendarEvent::STATUS_CONFIRMED => 'Confirmed',
                            CalendarEvent::STATUS_IN_PROGRESS => 'In Progress',
                            CalendarEvent::STATUS_COMPLETED => 'Completed',
                            CalendarEvent::STATUS_CANCELLED => 'Cancelled',
                        ])
                        ->default(CalendarEvent::STATUS_SCHEDULED),
                ])
                ->visible(fn ($get) => $get('type') !== CalendarEvent::TYPE_DEADLINE),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->searchable()
                    ->sortable(),
                
                Tables\Columns\BadgeColumn::make('type')
                    ->colors([
                        'primary' => CalendarEvent::TYPE_MEETING,
                        'success' => CalendarEvent::TYPE_SERVICE_TOUR,
                        'warning' => CalendarEvent::TYPE_INSTALLATION,
                        'danger' => CalendarEvent::TYPE_DEADLINE,
                    ]),
                
                Tables\Columns\TextColumn::make('start_date')
                    ->dateTime('d.m.Y H:i')
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('end_date')
                    ->dateTime('d.m.Y H:i')
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('comandă.name')
                    ->searchable()
                    ->limit(30),
                
                Tables\Columns\TextColumn::make('client.company_name')
                    ->searchable()
                    ->limit(30),
                
                Tables\Columns\BadgeColumn::make('status')
                    ->colors([
                        'secondary' => CalendarEvent::STATUS_SCHEDULED,
                        'primary' => CalendarEvent::STATUS_CONFIRMED,
                        'warning' => CalendarEvent::STATUS_IN_PROGRESS,
                        'success' => CalendarEvent::STATUS_COMPLETED,
                        'danger' => CalendarEvent::STATUS_CANCELLED,
                    ]),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->options([
                        CalendarEvent::TYPE_MEETING => 'Meetings',
                        CalendarEvent::TYPE_SERVICE_TOUR => 'Service Tours',
                        CalendarEvent::TYPE_INSTALLATION => 'Installations',
                        CalendarEvent::TYPE_DEADLINE => 'Deadlines',
                    ]),
                
                Tables\Filters\Filter::make('upcoming')
                    ->query(fn (Builder $query): Builder => $query->upcoming())
                    ->label('Upcoming Events'),
                
                Tables\Filters\Filter::make('date_range')
                    ->form([
                        Forms\Components\DatePicker::make('from'),
                        Forms\Components\DatePicker::make('until'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('start_date', '>=', $date),
                            )
                            ->when(
                                $data['until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('start_date', '<=', $date),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->visible(fn (CalendarEvent $record) => 
                        $record->type !== CalendarEvent::TYPE_DEADLINE || 
                        !($record->metadata['auto_generated'] ?? false)
                    ),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function canDelete(Model $record): bool
    {
        // Prevent deletion of auto-generated deadline events
        return !($record->type === CalendarEvent::TYPE_DEADLINE && 
                ($record->metadata['auto_generated'] ?? false));
    }
}
```

This calendar integration provides comprehensive scheduling capabilities with automatic deadline management and seamless integration with the comandă workflow system.
