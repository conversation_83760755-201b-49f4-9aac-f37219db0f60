# Required Packages & Setup Specification

## Overview
This document outlines all required packages, their installation, and configuration for the Caramel CRM system, including calendar integration, Google Drive storage, document generation, and testing infrastructure.

## Package Requirements

### Core CRM Packages
```bash
# Calendar Integration
composer require guava/calendar

# Google Drive Integration (Laravel 12 compatible)
composer require masbug/flysystem-google-drive-ext:^2.3

# User Role Management
composer require spatie/laravel-permission

# Filament Permission Management Plugin
composer require bezhansalleh/filament-shield

# HTTP Client for external APIs (Gotenberg communication)
# Note: guzzlehttp/guzzle is already included in Laravel 12

# UUID Generation
composer require ramsey/uuid

# Romanian Localization
composer require laravel-lang/lang laravel-lang/attributes
```

### Development & Testing Packages
```bash
# Enhanced Testing
composer require --dev pestphp/pest-plugin-faker
composer require --dev pestphp/pest-plugin-livewire

# Code Quality
composer require --dev larastan/larastan
composer require --dev barryvdh/laravel-debugbar

# Database Testing
composer require --dev orchestra/testbench
```

### Frontend Dependencies
```bash
# Page.js CSS Framework for document styling
npm install @page-js/page-css

# Additional styling utilities
npm install tailwindcss-page-css
```

## Google Drive Integration Setup

### 1. Google Cloud Console Configuration

#### Create Google Cloud Project
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create new project: "Caramel CRM"
3. Enable Google Drive API
4. Enable Google Sheets API (for future reporting)

#### Create Service Account
```bash
# Steps in Google Cloud Console:
1. Navigate to "IAM & Admin" > "Service Accounts"
2. Click "Create Service Account"
3. Name: "caramel-crm-storage"
4. Description: "Service account for CRM file storage"
5. Grant roles:
   - "Storage Admin" (for file management)
   - "Drive File Admin" (for Drive operations)
6. Create and download JSON key file
```

#### OAuth2 Credentials (for user authentication)
```bash
# Steps in Google Cloud Console:
1. Navigate to "APIs & Credentials" > "Credentials"
2. Click "Create Credentials" > "OAuth 2.0 Client IDs"
3. Application type: "Web application"
4. Name: "Caramel CRM Web Client"
5. Authorized redirect URIs:
   - http://localhost:8000/auth/google/callback (development)
   - https://your-domain.com/auth/google/callback (production)
6. Download client configuration JSON
```

### 2. Laravel Configuration

#### Filesystem Configuration
```php
// config/filesystems.php - Add to 'disks' array
'google' => [
    'driver' => 'google',
    'clientId' => env('GOOGLE_DRIVE_CLIENT_ID'),
    'clientSecret' => env('GOOGLE_DRIVE_CLIENT_SECRET'),
    'refreshToken' => env('GOOGLE_DRIVE_REFRESH_TOKEN'),
    'folderId' => env('GOOGLE_DRIVE_FOLDER_ID'), // Optional: specific folder
    'teamDriveId' => env('GOOGLE_DRIVE_TEAM_DRIVE_ID'), // Optional: team drive
],
```

#### Services Configuration
```php
// config/services.php - Add Google Drive configuration
'google' => [
    'client_id' => env('GOOGLE_DRIVE_CLIENT_ID'),
    'client_secret' => env('GOOGLE_DRIVE_CLIENT_SECRET'),
    'redirect' => env('GOOGLE_DRIVE_REDIRECT_URI'),
],

'gotenberg' => [
    'url' => env('GOTENBERG_URL', 'http://localhost:3000'),
    'timeout' => env('GOTENBERG_TIMEOUT', 30),
],
```

### 3. Environment Variables
```env
# Google Drive Configuration
GOOGLE_DRIVE_CLIENT_ID=your_client_id_here
GOOGLE_DRIVE_CLIENT_SECRET=your_client_secret_here
GOOGLE_DRIVE_REFRESH_TOKEN=your_refresh_token_here
GOOGLE_DRIVE_FOLDER_ID=your_main_folder_id_here
GOOGLE_DRIVE_REDIRECT_URI=http://localhost:8000/auth/google/callback

# Gotenberg Configuration (external instance)
GOTENBERG_URL=http://your-gotenberg-instance:3000
GOTENBERG_TIMEOUT=30

# Calendar Configuration
CALENDAR_DEFAULT_VIEW=month
CALENDAR_TIMEZONE=Europe/Bucharest
CALENDAR_FIRST_DAY=1

# CRM Specific
CRM_TIMEZONE=Europe/Bucharest
CRM_CURRENCY=RON
CRM_DATE_FORMAT="d.m.Y"
CRM_TIME_FORMAT="H:i"
CRM_COMPANY_NAME="Your Company Name"
CRM_COMPANY_CUI="RO12345678"
CRM_COMPANY_REG_COM="J40/1234/2024"
```

## Installation Commands Summary

### Complete Installation Script
```bash
#!/bin/bash
# Caramel CRM Package Installation Script

echo "Installing core packages..."
composer require guava/calendar
composer require masbug/flysystem-google-drive-ext:^2.3
composer require spatie/laravel-permission
composer require bezhansalleh/filament-shield
composer require ramsey/uuid
composer require laravel-lang/lang laravel-lang/attributes

echo "Installing development packages..."
composer require --dev pestphp/pest-plugin-faker
composer require --dev pestphp/pest-plugin-livewire
composer require --dev larastan/larastan
composer require --dev barryvdh/laravel-debugbar

echo "Publishing configurations..."
php artisan vendor:publish --tag=calendar-config
php artisan vendor:publish --provider="Spatie\Permission\PermissionServiceProvider"
php artisan vendor:publish --tag=filament-shield-config
php artisan shield:install

echo "Running migrations..."
php artisan migrate

echo "Installing frontend dependencies..."
npm install @page-js/page-css

echo "Setup complete! Please configure your .env file with Google Drive credentials."
```

## Google Drive Folder Structure Setup

### Automated Folder Creation
```php
// Create this as an Artisan command: php artisan crm:setup-drive
class SetupGoogleDriveCommand extends Command
{
    protected $signature = 'crm:setup-drive';
    protected $description = 'Setup Google Drive folder structure for CRM';

    public function handle()
    {
        $disk = Storage::disk('google');
        
        // Create main CRM folder structure
        $folders = [
            'CRM_Files',
            'CRM_Files/Templates',
            'CRM_Files/Generated_Documents',
            'CRM_Files/Comenzi', // Individual comandă folders will be created dynamically
        ];
        
        foreach ($folders as $folder) {
            if (!$disk->exists($folder)) {
                $disk->makeDirectory($folder);
                $this->info("Created folder: {$folder}");
            }
        }
        
        $this->info('Google Drive folder structure setup complete!');
    }
}
```

## Package-Specific Configurations

### Guava Calendar Configuration
```php
// config/calendar.php (after publishing)
return [
    'default_view' => env('CALENDAR_DEFAULT_VIEW', 'month'),
    'timezone' => env('CALENDAR_TIMEZONE', 'Europe/Bucharest'),
    'first_day_of_week' => env('CALENDAR_FIRST_DAY', 1), // Monday
    'event_colors' => [
        'meeting' => '#3B82F6',      // Blue
        'service_tour' => '#10B981',  // Green
        'installation' => '#F59E0B',  // Orange
        'deadline' => '#EF4444',      // Red
    ],
];
```

### Spatie Permissions Configuration
```php
// config/permission.php (after publishing)
return [
    'models' => [
        'permission' => Spatie\Permission\Models\Permission::class,
        'role' => Spatie\Permission\Models\Role::class,
    ],
    
    'table_names' => [
        'roles' => 'roles',
        'permissions' => 'permissions',
        'model_has_permissions' => 'model_has_permissions',
        'model_has_roles' => 'model_has_roles',
        'role_has_permissions' => 'role_has_permissions',
    ],
    
    'cache' => [
        'expiration_time' => \DateInterval::createFromDateString('24 hours'),
        'key' => 'spatie.permission.cache',
        'store' => 'default',
    ],
];
```

## Verification Commands

### Test Google Drive Connection
```bash
# Create test command to verify Google Drive setup
php artisan make:command TestGoogleDriveConnection

# Test file upload
php artisan crm:test-drive
```

### Test Gotenberg Connection
```bash
# Create test command to verify Gotenberg connection
php artisan make:command TestGotenbergConnection

# Test PDF generation
php artisan crm:test-gotenberg
```

## Post-Installation Checklist

### Required Manual Steps
1. ✅ Install packages via Composer
2. ✅ Install NPM dependencies
3. ✅ Configure Google Cloud Console
4. ✅ Download and configure service account JSON
5. ✅ Set up OAuth2 credentials
6. ✅ Configure environment variables
7. ✅ Run migrations
8. ✅ Test Google Drive connection
9. ✅ Test Gotenberg connection
10. ✅ Create initial folder structure

### Verification Tests
```bash
# Run all verification tests
php artisan test --filter=Integration
php artisan crm:test-drive
php artisan crm:test-gotenberg
php artisan crm:test-calendar
```

This setup provides all necessary packages and configurations for the complete CRM system functionality.
