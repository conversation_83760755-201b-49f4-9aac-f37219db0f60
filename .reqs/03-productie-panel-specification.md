# Productie Panel Specification - Execution Team Interface

## Overview
The Productie Panel (`/productie`) is a simplified, task-focused interface designed specifically for execution team specialists. It emphasizes clarity, simplicity, and efficiency, removing distractions and focusing only on relevant work information.

## Design Philosophy

### Core Principles
- **Simplicity First**: Minimal interface with only essential information
- **Task-Focused**: Everything revolves around assigned tasks
- **No Pricing**: Complete removal of financial information
- **Visual Clarity**: Clear status indicators and progress visualization
- **Mobile-Friendly**: Optimized for tablet/mobile use in workshop environments

## Panel Structure

### Navigation Menu
```
Productie Panel (/productie)
├── 📋 My Tasks (Kanban Board)
├── 📅 My Schedule
├── 📁 Task Files
└── 👤 Profile
```

## Page 1: My Tasks - Kanban Board

### Kanban Board Layout
**Resource**: `TaskKanbanResource`
**Route**: `/productie/tasks`

#### Board Columns
```
┌─────────────┬─────────────┬─────────────┬─────────────┐
│   To Do     │ In Progress │   Review    │  Complete   │
│             │             │             │             │
│ ┌─────────┐ │ ┌─────────┐ │ ┌─────────┐ │ ┌─────────┐ │
│ │ Task A  │ │ │ Task B  │ │ │ Task C  │ │ │ Task D  │ │
│ │ C001    │ │ │ C002    │ │ │ C003    │ │ │ C004    │ │
│ │ Urgent  │ │ │ Normal  │ │ │ Review  │ │ │ Done    │ │
│ └─────────┘ │ └─────────┘ │ └─────────┘ │ └─────────┘ │
│             │             │             │             │
└─────────────┴─────────────┴─────────────┴─────────────┘
```

#### Task Card Design
```
┌─────────────────────────────────────┐
│ 🔧 Debitare CNC                     │ ← Activity Type Icon
│ ─────────────────────────────────── │
│ Comandă: C001 - Kitchen Renovation  │ ← Comandă Reference
│ Client: SC ABC SRL                  │ ← Client Name
│ ─────────────────────────────────── │
│ ⏰ Due: 15 Dec 2024                │ ← Deadline
│ 📎 3 files attached                │ ← File Count
│ ─────────────────────────────────── │
│ 🔴 URGENT                          │ ← Priority Indicator
└─────────────────────────────────────┘
```

#### Task Card Information
**Essential Data Only**:
- Activity name and type icon
- Comandă ID and name (no pricing)
- Client name
- Deadline with visual urgency indicators
- File attachment count
- Priority level (Normal/Urgent/Fast-track)
- Brief description/notes

**Visual Indicators**:
- 🔴 Red border: Urgent/overdue
- 🟡 Yellow border: Due soon (within 2 days)
- 🟢 Green border: On schedule
- ⚡ Lightning icon: Fast-track items

#### Drag & Drop Functionality
- **To Do → In Progress**: Automatically logs start time
- **In Progress → Review**: Marks as completed, awaits approval
- **Review → Complete**: Final approval (if PM approves)
- **Any → To Do**: Resets task status

#### Task Detail Modal
Clicking a task card opens detailed view:
```
┌─────────────────────────────────────────────────────────┐
│ 🔧 Debitare CNC - Task Details                         │
│ ─────────────────────────────────────────────────────── │
│ Comandă: C001 - Kitchen Renovation                     │
│ Client: SC ABC SRL                                     │
│ Stage: 5 - Executie                                    │
│ ─────────────────────────────────────────────────────── │
│ Description:                                            │
│ Cut kitchen cabinet panels according to specifications │
│ Material: 18mm MDF, White melamine finish             │
│ ─────────────────────────────────────────────────────── │
│ Files & Attachments:                                   │
│ 📄 cutting_plan.pdf                                   │
│ 📄 material_specs.pdf                                 │
│ 🖼️ reference_image.jpg                                │
│ ─────────────────────────────────────────────────────── │
│ Time Tracking:                                         │
│ Assigned: 10 Dec 2024, 09:00                         │
│ Started: 12 Dec 2024, 08:30                          │
│ Estimated: 4 hours                                    │
│ ─────────────────────────────────────────────────────── │
│ Notes & Comments:                                      │
│ [Text area for specialist notes]                      │
│ ─────────────────────────────────────────────────────── │
│ Actions:                                               │
│ [Start Task] [Mark Complete] [Add Files] [Add Note]   │
└─────────────────────────────────────────────────────────┘
```

## Page 2: My Schedule

### Schedule View
**Resource**: `SpecialistScheduleResource`
**Route**: `/productie/schedule`

#### Daily Schedule Layout
```
Today - Monday, 15 Dec 2024
┌─────────────────────────────────────────────────────────┐
│ 08:00 - 12:00  🔧 Debitare CNC (C001)                 │
│                Kitchen Renovation - SC ABC SRL         │
│ ─────────────────────────────────────────────────────── │
│ 13:00 - 15:00  🔨 Assembly (C003)                     │
│                Office Setup - XYZ Company              │
│ ─────────────────────────────────────────────────────── │
│ 15:30 - 17:00  📋 Quality Check (C002)                │
│                Bathroom Fix - John Doe                 │
└─────────────────────────────────────────────────────────┘
```

#### Weekly Overview
- Simple calendar grid showing assigned tasks
- Color-coded by activity type
- Click to see task details
- Mobile-optimized for quick reference

## Page 3: Task Files

### File Management
**Resource**: `TaskFileResource`
**Route**: `/productie/files`

#### File Organization
```
My Task Files
├── Current Tasks
│   ├── C001 - Kitchen Renovation
│   │   ├── cutting_plan.pdf
│   │   ├── material_specs.pdf
│   │   └── reference_image.jpg
│   └── C003 - Office Setup
│       ├── assembly_guide.pdf
│       └── hardware_list.xlsx
├── Completed Tasks (Last 30 days)
│   └── [Previous task files]
└── Upload New Files
    └── [Drag & drop area]
```

#### File Features
- **Download**: Direct download from Google Drive
- **Preview**: In-browser preview for images/PDFs
- **Upload**: Add work progress photos, completed work images
- **Organization**: Automatically organized by comandă
- **Search**: Quick file search across all tasks

## Specialist Profile Page

### Profile Information
```
┌─────────────────────────────────────────────────────────┐
│ 👤 Specialist Profile                                   │
│ ─────────────────────────────────────────────────────── │
│ Name: Ion Popescu                                       │
│ Role: CNC Operator                                      │
│ Department: Production                                  │
│ ─────────────────────────────────────────────────────── │
│ Current Workload:                                       │
│ Active Tasks: 3                                         │
│ Pending Review: 1                                       │
│ Completed This Week: 8                                  │
│ ─────────────────────────────────────────────────────── │
│ Specializations:                                        │
│ • CNC Operations                                        │
│ • Wood Processing                                       │
│ • Quality Control                                       │
│ ─────────────────────────────────────────────────────── │
│ Quick Actions:                                          │
│ [Change Password] [Notification Settings]              │
└─────────────────────────────────────────────────────────┘
```

## Mobile Optimization

### Responsive Design
- **Tablet Mode**: Full kanban board with touch-friendly cards
- **Phone Mode**: List view with swipe actions
- **Offline Support**: Basic task viewing when offline
- **Touch Gestures**: Swipe to change status, long-press for details

### Workshop Environment Considerations
- **High Contrast**: Easy reading in various lighting
- **Large Touch Targets**: Suitable for work gloves
- **Minimal Text Input**: Focus on status updates and file uploads
- **Quick Actions**: One-tap status changes

## Notification System

### Real-time Notifications
- **New Task Assigned**: Immediate notification
- **Deadline Approaching**: 24-hour and 2-hour warnings
- **Task Approved**: Confirmation when PM approves work
- **Priority Changes**: Immediate alert for urgent tasks

### Notification Channels
- **In-app**: Toast notifications and badge counts
- **Email**: Daily summary (optional)
- **Push**: Mobile push notifications (if PWA)

## Integration with Main System

### Data Synchronization
- **Real-time Updates**: Instant sync with main App panel
- **Status Propagation**: Task status updates reflect in comandă stages
- **File Sync**: Automatic Google Drive synchronization
- **Time Tracking**: Automatic logging of work hours

### Reporting Integration
- **Work Hours**: Automatic time tracking for payroll
- **Productivity Metrics**: Task completion rates and times
- **Quality Metrics**: Rework rates and approval times

## Security & Access Control

### Specialist Permissions
- **View Only**: Assigned tasks and related files
- **No Pricing**: Complete removal of financial data
- **Limited Comandă Info**: Only relevant operational details
- **File Access**: Only task-related files

### Data Protection
- **Session Management**: Automatic logout after inactivity
- **Secure File Access**: Temporary download links
- **Audit Trail**: Log all specialist actions
- **Role Enforcement**: Server-side permission validation

This specification creates a focused, efficient interface that allows specialists to concentrate on their work without distractions while maintaining full integration with the broader CRM system.
