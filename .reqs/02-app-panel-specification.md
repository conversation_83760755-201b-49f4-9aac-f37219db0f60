# App Panel Specification - Main Operations Interface

## Overview
The App Panel (`/app`) serves as the primary interface for all users, combining operational management with PM dashboard functionality. It provides comprehensive comandă management with role-based access control.

## Panel Structure

### Navigation Menu
```
App Panel (/app)
├── 📋 Comenzi (Main Table)
├── 📅 Calendar
├── 📄 Documents
│   ├── Proformas
│   ├── Invoices
│   ├── Contracts
│   ├── Guarantee Certificates
│   └── Proces Verbal
├── ⚙️ Activity Editor (Managers/PMs only)
└── 👤 Profile
```

## Page 1: Comenzi Table (Main Dashboard)

### Table Structure
**Resource**: `ComandaResource`
**Route**: `/app/comenzi`

#### Columns Configuration
```
┌─────────────────────────────────────────────────────────────────────────────────────┐
│ ID | Channel | Client | Comandă Name | Stage 1-7 Visual | Comments | Actions        │
├─────────────────────────────────────────────────────────────────────────────────────┤
│ C001│   📧   │ SC ABC │ Kitchen Reno │ ●●●○○○○        │    💬    │ 👁️ ✏️ 🗑️     │
│ C002│   📱   │ John D │ Bathroom Fix │ ●●○○○○○        │          │ 👁️ ✏️        │
│ C003│   🌐   │ XYZ Co │ Office Setup │ ●●●●●●●        │    💬    │ 👁️ ✏️ 🗑️     │
└─────────────────────────────────────────────────────────────────────────────────────┘
```

#### Column Details

**1. ID Column**
- Format: Auto-generated internal number (e.g., C001, C002)
- Sortable, searchable
- Links to comandă detail view

**2. Channel Icon Column**
- Visual icons representing arrival channels:
  - 📧 Email
  - 📱 WhatsApp/Social messaging  
  - 🌐 Website form
  - 📞 Phone
  - 🚶 Walk-in
- Tooltip shows channel name on hover
- Filterable by channel type

**3. Client Column**
- Client company name or individual name
- Searchable, sortable
- Links to client detail (if permissions allow)
- Truncated with tooltip for long names

**4. Comandă Name Column**
- Project/order name
- Searchable, sortable
- Truncated with tooltip for long names

**5. Stage Visual Columns (1-7)**
- Visual representation of completion status:
  - ● Completed stage (green)
  - ◐ In progress (yellow)
  - ○ Not started (gray)
- Each stage shows tooltip with stage name:
  1. Ofertare
  2. Contractare  
  3. Pregatire
  4. Aprovizionare
  5. Executie
  6. Livrare
  7. Facturare
- Clickable to show stage details

**6. Comments Indicator**
- 💬 Icon appears if comandă has comments/notes
- Shows count on hover
- Clickable to open comments panel

**7. Actions Column**
- 👁️ View: Always visible to all users
- ✏️ Edit: Visible based on role and ownership
- 🗑️ Delete: Visible only to owners/managers
- Actions adapt based on user permissions

#### Filtering & Search

**Global Search Bar**
- Searches across: ID, client name, comandă name, description
- Real-time search with debouncing

**Advanced Filters Panel**
```
Filters:
├── Stage Status
│   ├── ☐ Not Started
│   ├── ☐ In Progress  
│   └── ☐ Completed
├── Arrival Channel
│   ├── ☐ Email
│   ├── ☐ WhatsApp
│   ├── ☐ Website
│   ├── ☐ Phone
│   └── ☐ Walk-in
├── Priority
│   ├── ☐ Fast-track
│   └── ☐ High Priority
├── Date Range
│   ├── Arrival Date
│   └── Deadline
├── Owner (PM)
│   └── Dropdown of PMs
└── Client
    └── Searchable client list
```

**Sorting Options**
- ID (ascending/descending)
- Client name (A-Z/Z-A)
- Arrival date (newest/oldest)
- Deadline (urgent first/latest first)
- Stage progress (most/least complete)

#### Bulk Actions
Available to managers and owners:
- Bulk stage updates
- Bulk assignment to PMs
- Bulk priority changes
- Export selected to PDF/Excel

## Page 2: Calendar View

### Calendar Implementation
**Plugin**: Guava Calendar for Filament 4
**Resource**: `CalendarResource`
**Route**: `/app/calendar`

#### Event Types

**1. Meetings** 
- Duration: Hours (e.g., 2-4 PM)
- Color: Blue
- Shows: Client name, meeting type, location
- Editable by: PMs, Managers

**2. Service Tours**
- Duration: Days (00:00 first day to 23:59 last day)
- Color: Green
- Shows: Client name, service type, team assigned
- Editable by: PMs, Managers

**3. Installations**
- Duration: Hours (e.g., 9 AM - 5 PM)
- Color: Orange
- Shows: Client name, installation type, team
- Editable by: PMs, Managers

**4. Deadlines (Automatic)**
- Duration: All day event
- Color: Red
- Shows: Comandă name, deadline type
- Auto-generated from comandă deadlines
- Read-only (managed through comandă editing)

#### Calendar Features
- Month view (primary)
- Week/Day views available
- Event creation modal
- Drag & drop event editing
- Integration with comandă data
- Export to external calendars (iCal)

## Page 3: Document Generation Group

### Document Pages Structure
```
Documents Group:
├── Proformas
│   ├── Create New
│   ├── Templates Management
│   └── Generated Documents List
├── Invoices  
│   ├── Create New
│   ├── Templates Management
│   └── Generated Documents List
├── Contracts
│   ├── Create New
│   ├── Templates Management
│   └── Generated Documents List
├── Guarantee Certificates
│   ├── Create New
│   ├── Templates Management
│   └── Generated Documents List
└── Proces Verbal
    ├── Create New
    ├── Templates Management
    └── Generated Documents List
```

### Document Creation Flow
1. **Select Comandă**: Choose from dropdown or search
2. **Select Template**: Choose appropriate template
3. **Auto-populate Data**: Client info, comandă details, items
4. **Custom Fields**: Fill template-specific fields
5. **Preview**: Review generated document
6. **Generate**: Create PDF and database entry
7. **Store**: Save to Google Drive and provide download

### Integration with Comandă Edit View
Each comandă edit page includes:
- Quick document generation buttons
- Recently generated documents list
- Direct links to create new documents
- Document status indicators

## Role-Based Access Control

### Superadmin
- Full access to all features
- Can edit any comandă
- Can delete any comandă
- Sees all pricing information

### Managers  
- Full access except admin functions
- Can edit any comandă
- Can assign PMs to comenzi
- Sees all pricing information
- Can perform bulk operations

### Project Managers (PMs)
- Can edit owned comenzi
- Can view others' comenzi (no pricing)
- Can assign specialists to activities
- Can approve completed activities
- Can create documents for owned comenzi

### Specialists
- Can view assigned activities only
- Cannot see pricing information
- Cannot edit comandă details
- Can update activity status
- Limited document access

## Performance Considerations

### Table Optimization
- Pagination (25/50/100 per page)
- Lazy loading of related data
- Database indexing on frequently filtered columns
- Caching of stage completion calculations

### Real-time Updates
- WebSocket integration for live updates
- Notification system for status changes
- Auto-refresh for critical data

### Mobile Responsiveness
- Responsive table design
- Touch-friendly interface
- Simplified mobile view
- Offline capability for basic operations

## Page 4: Activity Editor (Managers/PMs Only)

### Activity Management Interface
**Resource**: `ActivityEditorResource`
**Route**: `/app/activity-editor`
**Access**: Managers and PMs only

#### Purpose
Centralized interface for creating, editing, and managing activity templates and assignments across all comandă stages.

#### Features

**Activity Template Management**
- Create reusable activity templates for each stage/substage combination
- Define default custom fields per activity type
- Set approval requirements and estimated durations
- Configure activity scope (comandă-level vs item-level)

**Bulk Activity Operations**
- Assign multiple activities to specialists
- Bulk status updates across multiple comenzi
- Template application to existing comenzi
- Progress monitoring across all active activities

**Activity Analytics**
- Completion rates by activity type
- Average duration vs estimated time
- Specialist workload distribution
- Bottleneck identification

#### Interface Layout
```
Activity Editor
├── Templates Tab
│   ├── Stage 1 - Ofertare
│   │   ├── Masuratori (Both scope)
│   │   ├── Cost Calculation (Item scope)
│   │   └── Technical Specs (Item scope)
│   ├── Stage 2 - Contractare
│   │   └── [Stage 2 activities...]
│   └── [Other stages...]
├── Active Activities Tab
│   ├── Filter by Stage/Status/Assignee
│   ├── Bulk Actions
│   └── Progress Overview
└── Analytics Tab
    ├── Performance Metrics
    ├── Workload Charts
    └── Completion Reports
```

This specification provides the foundation for implementing the main App panel interface with comprehensive comandă management capabilities and advanced activity management tools.
