# User Role Management System Specification

## Overview
The user role management system implements a hierarchical permission structure using <PERSON><PERSON>'s Laravel Permission package (if compatible with Laravel 12) or a custom implementation. The system supports four distinct user roles with specific access patterns and panel restrictions.

## Role Hierarchy & Permissions

### Role Structure
```
Superadmin (Developer)
├── Full system access
├── All panels accessible
├── User management
├── System configuration
└── Audit capabilities

Manager
├── Business operations access
├── All panels except Admin
├── Full pricing visibility
├── Comandă assignment rights
└── Team management

Project Manager (PM)
├── Owned comandă management
├── Limited pricing visibility
├── Specialist assignment
├── Activity approval rights
└── Document generation

Specialist
├── Task-focused access
├── Productie panel only
├── No pricing visibility
├── File upload capabilities
└── Status update rights
```

## User Model Extension

### Enhanced User Model
```php
User extends Authenticatable implements FilamentUser
{
    // Basic Authentication
    protected $fillable = [
        'name', 'email', 'password', 'role'
    ];
    
    // Role Management
    public function role(): string
    {
        return $this->role;
    }
    
    public function isRole(string $role): bool
    {
        return $this->role === $role;
    }
    
    public function hasAnyRole(array $roles): bool
    {
        return in_array($this->role, $roles);
    }
    
    // Panel Access Control
    public function canAccessPanel(Panel $panel): bool
    {
        return match($panel->getId()) {
            'admin' => $this->isRole('superadmin'),
            'app' => $this->hasAnyRole(['superadmin', 'manager', 'pm', 'specialist']),
            'definitions' => $this->hasAnyRole(['superadmin', 'manager', 'pm']),
            'productie' => $this->hasAnyRole(['superadmin', 'specialist']),
            default => false
        };
    }
    
    // Permission Checks
    public function canSeePricing(): bool
    {
        return $this->hasAnyRole(['superadmin', 'manager']);
    }
    
    public function canEditComanda(Comandă $comandă): bool
    {
        return match($this->role) {
            'superadmin', 'manager' => true,
            'pm' => $comandă->owner_id === $this->id,
            'specialist' => false,
            default => false
        };
    }
    
    public function canDeleteComanda(Comandă $comandă): bool
    {
        return match($this->role) {
            'superadmin' => true,
            'manager' => true,
            'pm' => $comandă->owner_id === $this->id,
            'specialist' => false,
            default => false
        };
    }
    
    public function canAssignWork(): bool
    {
        return $this->hasAnyRole(['superadmin', 'manager', 'pm']);
    }
    
    public function canApproveActivities(): bool
    {
        return $this->hasAnyRole(['superadmin', 'manager', 'pm']);
    }
    
    // Relationships
    public function ownedComenzi()
    {
        return $this->hasMany(Comandă::class, 'owner_id');
    }
    
    public function contributedComenzi()
    {
        return $this->belongsToMany(Comandă::class, 'comanda_contributors');
    }
    
    public function assignedActivities()
    {
        return $this->hasMany(ComandaActivity::class, 'assigned_to');
    }
}
```

## Role-Specific Access Patterns

### Superadmin Access
```php
Superadmin Permissions:
├── Panel Access: ALL
├── Comandă Operations:
│   ├── View: All comenzi
│   ├── Edit: All comenzi
│   ├── Delete: All comenzi
│   ├── Create: Yes
│   └── Assign: Yes
├── Pricing: Full visibility
├── User Management: Full control
├── System Settings: Full control
├── Document Generation: All types
└── Audit Logs: Full access
```

### Manager Access
```php
Manager Permissions:
├── Panel Access: App, Definitions, Productie (view only)
├── Comandă Operations:
│   ├── View: All comenzi
│   ├── Edit: All comenzi
│   ├── Delete: All comenzi
│   ├── Create: Yes
│   └── Assign: Yes (assign PMs)
├── Pricing: Full visibility
├── User Management: Limited (no superadmin changes)
├── Team Management: Assign specialists to activities
├── Document Generation: All types
└── Reporting: Full access
```

### Project Manager (PM) Access
```php
PM Permissions:
├── Panel Access: App, Definitions (read-only), Productie (view only)
├── Comandă Operations:
│   ├── View: All comenzi (no pricing for others)
│   ├── Edit: Owned comenzi only
│   ├── Delete: Owned comenzi only
│   ├── Create: Yes
│   └── Assign: Specialists to owned comandă activities
├── Pricing: Only for owned comenzi
├── Activity Management:
│   ├── Create activities in owned comenzi
│   ├── Assign specialists to activities
│   ├── Approve completed activities
│   └── Monitor progress
├── Document Generation: For owned comenzi
└── Reporting: Own portfolio only
```

### Specialist Access
```php
Specialist Permissions:
├── Panel Access: Productie only
├── Task Operations:
│   ├── View: Assigned activities only
│   ├── Edit: Update status, add notes
│   ├── Files: Upload work files
│   └── Time Tracking: Log work hours
├── Pricing: No access
├── Comandă Info: Limited (no financial data)
├── Communication: Comments on assigned tasks
└── Profile: Basic profile management
```

## Panel-Specific Access Control

### App Panel Access Matrix
```
Feature                 | Superadmin | Manager | PM      | Specialist
------------------------|------------|---------|---------|------------
Comenzi Table (All)     | ✓         | ✓       | ✓*      | ✗
Comenzi Table (Own)     | ✓         | ✓       | ✓       | ✗
Calendar (Full)         | ✓         | ✓       | ✓       | ✗
Document Generation     | ✓         | ✓       | ✓**     | ✗
Bulk Operations         | ✓         | ✓       | ✗       | ✗
Advanced Filters        | ✓         | ✓       | ✓       | ✗

* PM sees all comenzi but no pricing for others
** PM can generate documents only for owned comenzi
```

### Definitions Panel Access Matrix
```
Feature                 | Superadmin | Manager | PM      | Specialist
------------------------|------------|---------|---------|------------
Client Management       | ✓         | ✓       | Read    | ✗
Document Templates      | ✓         | ✓       | Read    | ✗
System Configuration    | ✓         | Limited | ✗       | ✗
Reference Data          | ✓         | ✓       | Read    | ✗
```

### Productie Panel Access Matrix
```
Feature                 | Superadmin | Manager | PM      | Specialist
------------------------|------------|---------|---------|------------
Kanban Board (All)      | ✓         | ✓       | ✓       | ✗
Kanban Board (Own)      | ✓         | ✓       | ✓       | ✓
Task Management         | ✓         | ✓       | ✓*      | ✓**
File Management         | ✓         | ✓       | ✓       | ✓***
Schedule View           | ✓         | ✓       | ✓       | ✓***

* PM can manage tasks for owned comenzi
** Specialist can update status for assigned tasks
*** Limited to assigned tasks only
```

## Implementation Strategy

### Filament Integration
```php
// In each Resource class
public static function canViewAny(): bool
{
    return auth()->user()->canAccessPanel(Filament::getCurrentPanel());
}

public static function canView(Model $record): bool
{
    return match(auth()->user()->role) {
        'superadmin', 'manager' => true,
        'pm' => $record->owner_id === auth()->id(),
        'specialist' => $record->assigned_to === auth()->id(),
        default => false
    };
}

public static function canEdit(Model $record): bool
{
    return auth()->user()->canEditComanda($record);
}

public static function canDelete(Model $record): bool
{
    return auth()->user()->canDeleteComanda($record);
}
```

### Field-Level Visibility
```php
// In Form/Table builders
Forms\Components\TextInput::make('total_value')
    ->visible(fn() => auth()->user()->canSeePricing())
    ->disabled(fn() => !auth()->user()->hasAnyRole(['superadmin', 'manager'])),

Tables\Columns\TextColumn::make('total_value')
    ->visible(fn() => auth()->user()->canSeePricing())
    ->formatStateUsing(fn($state) => number_format($state, 2) . ' RON'),
```

### Middleware Implementation
```php
class RoleMiddleware
{
    public function handle(Request $request, Closure $next, string $role): Response
    {
        if (!auth()->user()->hasAnyRole(explode('|', $role))) {
            abort(403, 'Unauthorized access');
        }
        
        return $next($request);
    }
}

// Route protection
Route::middleware(['auth', 'role:superadmin|manager'])->group(function () {
    // Manager+ only routes
});
```

## User Management Interface

### User Creation/Editing
- **Superadmin**: Can create/edit all user types
- **Manager**: Can create/edit PM and Specialist accounts
- **PM/Specialist**: Can only edit own profile

### Role Assignment Rules
- **Role Changes**: Only higher roles can change lower roles
- **Ownership Transfer**: When PM role changes, comandă ownership must be reassigned
- **Activity Reassignment**: When specialist role changes, active assignments must be handled

This role management system provides secure, hierarchical access control while maintaining operational flexibility and clear separation of responsibilities.
