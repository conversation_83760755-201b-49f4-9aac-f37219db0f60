# Admin Panel Specification - System Administration

## Overview
The Admin Panel (`/admin`) provides comprehensive system administration capabilities, including advanced user and permission management, system configuration, and monitoring tools. Access is restricted to superadmin users only.

## Panel Structure

### Navigation Menu
```
Admin Panel (/admin)
├── 👥 User Management
│   ├── Users
│   ├── Roles & Permissions
│   └── User Activity Logs
├── ⚙️ System Configuration
│   ├── General Settings
│   ├── CRM Configuration
│   ├── Integration Settings
│   └── Email Templates
├── 📊 System Monitoring
│   ├── Performance Metrics
│   ├── Database Status
│   ├── Error Logs
│   └── Audit Trail
├── 🔧 Maintenance Tools
│   ├── Database Cleanup
│   ├── Cache Management
│   ├── Backup Management
│   └── System Health Check
└── 👤 Profile
```

## User Permission Management System

### Recommended Filament Plugin
**Primary Choice**: `bezhansalleh/filament-shield`
- Laravel 12 compatible
- Built on Spatie Laravel Permission
- Automatic resource permission generation
- Role-based panel access control
- Custom permission management

**Installation**:
```bash
composer require bezhansalleh/filament-shield
php artisan vendor:publish --tag=filament-shield-config
php artisan shield:install
```

### Permission Structure

#### Resource-Level Permissions
```php
// Auto-generated permissions for each resource
'view_any_comanda'     // Can view comandă list
'view_comanda'         // Can view individual comandă
'create_comanda'       // Can create new comandă
'update_comanda'       // Can edit comandă
'delete_comanda'       // Can delete comandă
'delete_any_comanda'   // Can bulk delete comenzi

// Similar pattern for all resources:
// - Client, ComandaActivity, CalendarEvent, DocumentTemplate, etc.
```

#### Custom Permissions
```php
// CRM-specific permissions
'view_pricing'         // Can see financial information
'assign_activities'    // Can assign work to specialists
'approve_activities'   // Can approve completed work
'generate_documents'   // Can create documents
'manage_templates'     // Can edit document templates
'access_reports'       // Can view analytics/reports
'manage_system'        // Can access system settings
```

#### Panel Access Permissions
```php
// Panel-specific access
'access_app_panel'        // Can access App panel
'access_definitions_panel' // Can access Definitions panel
'access_productie_panel'   // Can access Productie panel
'access_admin_panel'       // Can access Admin panel (superadmin only)
```

### Role Configuration

#### Predefined Roles with Permissions
```php
// Superadmin Role
'superadmin' => [
    // All permissions (wildcard or explicit list)
    '*' // or comprehensive permission array
],

// Manager Role
'manager' => [
    'view_any_comanda', 'view_comanda', 'create_comanda', 'update_comanda', 'delete_comanda',
    'view_any_client', 'view_client', 'create_client', 'update_client',
    'view_pricing', 'assign_activities', 'approve_activities',
    'generate_documents', 'access_reports',
    'access_app_panel', 'access_definitions_panel', 'access_productie_panel'
],

// PM Role
'pm' => [
    'view_any_comanda', 'view_comanda', 'create_comanda', 'update_comanda',
    'view_any_client', 'view_client',
    'assign_activities', 'approve_activities', 'generate_documents',
    'access_app_panel', 'access_definitions_panel', 'access_productie_panel'
],

// Specialist Role
'specialist' => [
    'view_comanda', // Only assigned activities
    'update_comanda_activity', // Only assigned activities
    'access_productie_panel'
]
```

## User Management Interface

### Enhanced User Resource
```php
class UserResource extends Resource
{
    // Form with role and permission management
    public static function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\Section::make('User Information')
                ->schema([
                    Forms\Components\TextInput::make('name')->required(),
                    Forms\Components\TextInput::make('email')->email()->required(),
                    Forms\Components\DateTimePicker::make('email_verified_at'),
                    Forms\Components\TextInput::make('password')
                        ->password()
                        ->dehydrateStateUsing(fn ($state) => Hash::make($state))
                        ->dehydrated(fn ($state) => filled($state))
                        ->required(fn (string $context): bool => $context === 'create'),
                ]),

            Forms\Components\Section::make('Role & Permissions')
                ->schema([
                    Forms\Components\Select::make('role')
                        ->options([
                            'superadmin' => 'Super Administrator',
                            'manager' => 'Manager',
                            'pm' => 'Project Manager',
                            'specialist' => 'Specialist',
                        ])
                        ->required()
                        ->reactive(),
                    
                    Forms\Components\CheckboxList::make('permissions')
                        ->relationship('permissions', 'name')
                        ->columns(3)
                        ->visible(fn ($get) => $get('role') !== 'superadmin'),
                        
                    Forms\Components\Toggle::make('is_active')
                        ->default(true),
                ]),

            Forms\Components\Section::make('Additional Information')
                ->schema([
                    Forms\Components\Textarea::make('notes'),
                ]),
        ]);
    }
}
```

### Permission Management Features

#### Role Templates
- Quick role assignment with predefined permission sets
- Custom role creation with granular permissions
- Role inheritance and permission overrides
- Bulk role updates for multiple users

#### Permission Auditing
- Track permission changes with timestamps
- User activity logging for security
- Permission usage analytics
- Access attempt monitoring

## System Configuration

### CRM Settings Management
```php
// System settings stored in database or config
'crm_settings' => [
    'company_info' => [
        'name' => 'Company Name',
        'cui' => '**********',
        'reg_com' => 'J40/1234/2024',
        'address' => 'Company Address',
        'phone' => '+40 XXX XXX XXX',
        'email' => '<EMAIL>',
    ],
    'workflow_settings' => [
        'auto_assign_activities' => true,
        'require_approval_by_default' => false,
        'deadline_notification_days' => 3,
    ],
    'document_settings' => [
        'default_currency' => 'RON',
        'vat_rate' => 19,
        'invoice_numbering_format' => 'INV-{YYYY}-{####}',
    ],
];
```

### Integration Configuration
- Google Drive API settings
- Gotenberg service configuration
- Email service settings (SMTP, etc.)
- Calendar integration settings
- Notification preferences

## System Monitoring

### Performance Metrics Dashboard
- Database query performance
- Response time monitoring
- Memory usage tracking
- Active user sessions
- System resource utilization

### Error Tracking
- Application error logs
- Failed job monitoring
- Database connection issues
- External service failures
- User-reported issues

### Audit Trail
- User login/logout tracking
- Data modification logs
- Permission changes
- System configuration changes
- Document generation history

## Maintenance Tools

### Database Management
- Database optimization tools
- Index analysis and recommendations
- Data cleanup utilities
- Backup and restore functionality
- Migration status monitoring

### Cache Management
- Cache clearing tools
- Cache performance metrics
- Redis/database cache monitoring
- Session management
- Queue monitoring

### System Health Checks
- Automated system diagnostics
- Service connectivity tests
- File system health checks
- Security vulnerability scans
- Performance benchmarking

## Security Features

### Access Control
- IP-based access restrictions
- Session timeout configuration
- Failed login attempt monitoring
- Two-factor authentication support
- API rate limiting

### Data Protection
- Sensitive data encryption
- GDPR compliance tools
- Data retention policies
- Secure file handling
- Audit log protection

This admin panel specification provides comprehensive system administration capabilities while maintaining security and ease of use for superadmin users.
