# Nginx Configuration for Laravel Reverb WebSockets

## The Problem

Your current nginx config has:
```nginx
location /reverb/app {
    ...
}
```

This is too specific. When the browser tries to connect to:
```
wss://crm.concept-42.com/reverb/app/cky5hbmyipkciufhiyg0?protocol=7&client=js&version=7.6.0
```

Nginx matches `/reverb/app` but then passes the FULL path (including `/reverb/app/...`) to Reverb.

Reverb expects paths like `/app/{key}`, NOT `/reverb/app/{key}`.

## The Solution

Change your nginx location block to:

```nginx
# Laravel Reverb WebSocket proxy (WSS)
location /reverb/ {
    proxy_http_version 1.1;
    resolver 127.0.0.11;
    proxy_set_header Host $http_host;
    proxy_set_header Scheme $scheme;
    proxy_set_header SERVER_PORT $server_port;
    proxy_set_header REMOTE_ADDR $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "Upgrade";
    
    # Strip /reverb prefix before passing to Reverb server
    rewrite ^/reverb/(.*) /$1 break;
    proxy_pass http://**********:8081;
}
```

## What Changed

1. **`location /reverb/`** - Catches ALL paths starting with `/reverb/`
2. **`rewrite ^/reverb/(.*) /$1 break;`** - Strips the `/reverb` prefix
   - Browser requests: `/reverb/app/cky5hbmyipkciufhiyg0`
   - Reverb receives: `/app/cky5hbmyipkciufhiyg0` ✓

## How to Apply

1. Edit your nginx config file (likely `/etc/nginx/sites-available/crm.concept-42.com`)
2. Replace the `location /reverb/app` block with the new `location /reverb/` block above
3. Test the configuration:
   ```bash
   nginx -t
   ```
4. If test passes, reload nginx:
   ```bash
   systemctl reload nginx
   # OR if in Docker:
   docker exec <nginx-container> nginx -s reload
   ```

## Verification

After applying:

1. Visit https://crm.concept-42.com/app
2. Open browser console (F12)
3. You should see Echo connect successfully
4. Visit https://crm.concept-42.com/test/notification/quick
5. You should see a toast notification appear immediately!

## Alternative: No Rewrite (if above doesn't work)

If the rewrite doesn't work, try this alternative:

```nginx
location /reverb/ {
    proxy_http_version 1.1;
    resolver 127.0.0.11;
    proxy_set_header Host $http_host;
    proxy_set_header Scheme $scheme;
    proxy_set_header SERVER_PORT $server_port;
    proxy_set_header REMOTE_ADDR $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "Upgrade";
    
    # Pass to Reverb with trailing slash
    proxy_pass http://**********:8081/;
}
```

The trailing slash in `proxy_pass` tells nginx to strip the location prefix.

## Debugging

If it still doesn't work, check:

1. **Reverb is running:**
   ```bash
   supervisorctl status reverb
   ```

2. **Reverb logs:**
   ```bash
   supervisorctl tail -f reverb
   ```

3. **Nginx error logs:**
   ```bash
   tail -f /var/log/nginx/error.log
   ```

4. **Test direct connection to Reverb (from server):**
   ```bash
   curl -i http://**********:8081/app/cky5hbmyipkciufhiyg0
   ```
   Should return a 426 Upgrade Required response.

