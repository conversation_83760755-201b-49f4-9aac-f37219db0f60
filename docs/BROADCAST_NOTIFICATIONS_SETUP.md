# Broadcast Notifications Setup - Summary

## Current Status

✅ **Working:**
- <PERSON>vel Reverb is running
- Database notifications are working (appear in bell icon)
- Filament panels are configured with `->databaseNotifications()`
- Echo configuration exists in `config/filament.php`
- User model has `receivesBroadcastNotificationsOn()` method
- Broadcasting channel authorization is set up in `routes/channels.php`
- NotificationService sends both database AND broadcast notifications

❌ **Not Working:**
- Toast notifications don't appear in real-time
- WebSocket connection fails with error:
  ```
  WebSocket connection to 'wss://crm.concept-42.com/reverb/app/...' failed
  ```

## Root Cause

**Nginx configuration issue.** The current nginx location block is:

```nginx
location /reverb/app {
    ...
    proxy_pass http://**********:8081;
}
```

This causes the problem:
- <PERSON><PERSON><PERSON> requests: `wss://crm.concept-42.com/reverb/app/cky5hbmyipkciufhiyg0`
- Nginx passes to Reverb: `/reverb/app/cky5hbmyipkciufhiyg0`
- Reverb expects: `/app/cky5hbmyipkciufhiyg0` ❌

## The Fix

### Step 1: Update Nginx Configuration

Edit your nginx config and change the Reverb location block to:

```nginx
# Laravel Reverb WebSocket proxy (WSS)
location /reverb/ {
    proxy_http_version 1.1;
    resolver 127.0.0.11;
    proxy_set_header Host $http_host;
    proxy_set_header Scheme $scheme;
    proxy_set_header SERVER_PORT $server_port;
    proxy_set_header REMOTE_ADDR $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "Upgrade";
    
    # Strip /reverb prefix before passing to Reverb server
    rewrite ^/reverb/(.*) /$1 break;
    proxy_pass http://**********:8081;
}
```

**OR** use the simpler trailing-slash method:

```nginx
location /reverb/ {
    proxy_http_version 1.1;
    resolver 127.0.0.11;
    proxy_set_header Host $http_host;
    proxy_set_header Scheme $scheme;
    proxy_set_header SERVER_PORT $server_port;
    proxy_set_header REMOTE_ADDR $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "Upgrade";
    
    # Trailing slash strips the location prefix
    proxy_pass http://**********:8081/;
}
```

### Step 2: Test and Reload Nginx

```bash
# Test configuration
nginx -t

# If test passes, reload
systemctl reload nginx
# OR for Docker:
docker exec <nginx-container> nginx -s reload
```

### Step 3: Clear Laravel Caches

```bash
php artisan config:clear
php artisan route:clear
```

### Step 4: Test the Connection

1. Visit https://crm.concept-42.com/app
2. Open browser console (F12)
3. Look for Echo connection messages - should see "Connected" instead of errors
4. Visit https://crm.concept-42.com/test/notification/quick
5. You should see a toast notification appear immediately! 🎉

## What We Changed in the Code

### 1. Added `receivesBroadcastNotificationsOn()` to User Model

**File:** `app/Models/User.php`

```php
/**
 * The channels the user receives notification broadcasts on.
 * This is required for Filament broadcast notifications to work.
 */
public function receivesBroadcastNotificationsOn(): string
{
    return 'App.Models.User.' . $this->id;
}
```

This tells Laravel which channel to broadcast notifications to for each user.

### 2. Updated NotificationService to Broadcast

**File:** `app/Services/NotificationService.php`

Changed from:
```php
// Old: Only database
$notification->sendToDatabase($recipient, isEventDispatched: true);
```

To:
```php
// New: Both database AND broadcast
$notification->sendToDatabase($recipient, isEventDispatched: true);
$notification->broadcast($recipient);  // ← Added this!
```

This ensures notifications are sent BOTH to the database (for persistence) AND broadcast (for real-time toasts).

### 3. Fixed Filament Echo Configuration

**File:** `config/filament.php`

Ensured the `wsPath` is correctly formatted:
```php
'wsPath' => env('VITE_REVERB_PATH') ? '/' . env('VITE_REVERB_PATH') : '',
```

This adds a leading slash to the path if it's set.

## Environment Variables

**File:** `.env`

```env
# Broadcasting
BROADCAST_CONNECTION=reverb

# Reverb Server (internal)
REVERB_APP_ID=355121
REVERB_APP_KEY=cky5hbmyipkciufhiyg0
REVERB_APP_SECRET=dkkows0bcoucxzo61zkj
REVERB_HOST=**********
REVERB_PORT=8081
REVERB_SCHEME=http

# Frontend (browser connections via nginx)
VITE_REVERB_APP_KEY="${REVERB_APP_KEY}"
VITE_REVERB_HOST="crm.concept-42.com"
VITE_REVERB_PORT=443
VITE_REVERB_SCHEME=https
VITE_REVERB_PATH="reverb"
```

## How It Works

1. **User action triggers notification** (e.g., comanda assigned)
2. **NotificationService.send()** is called
3. **Notification is saved to database** → appears in bell icon
4. **Notification is broadcast via Reverb** → triggers toast
5. **Browser receives broadcast** via WebSocket
6. **Filament displays toast** automatically

## Test Routes

We created several test routes to verify the setup:

- `/test/notification/quick` - Quick test (redirects to /app)
- `/test/notification/both` - Test both database + broadcast
- `/test/notification/broadcast-only` - Test broadcast only
- `/test/notification/database-only` - Test database only
- `/test/notification/types` - Test all notification types
- `/test/notification/persistent` - Test persistent notification

## Troubleshooting

### Still seeing WebSocket errors?

1. **Check Reverb is running:**
   ```bash
   supervisorctl status reverb
   ```

2. **Check Reverb logs:**
   ```bash
   supervisorctl tail -f reverb
   ```

3. **Test direct connection to Reverb:**
   ```bash
   curl -i http://**********:8081/app/cky5hbmyipkciufhiyg0
   ```
   Should return `426 Upgrade Required`

4. **Check nginx error logs:**
   ```bash
   tail -f /var/log/nginx/error.log
   ```

### Notifications appear only on reload?

This means database notifications work but broadcast doesn't. The nginx fix above should resolve this.

### Echo is undefined?

This only happens on non-Filament pages (like the test-echo-status page). On Filament panel pages (/app, /admin, etc.), Echo is automatically loaded.

## Next Steps

After fixing nginx:

1. Test with the quick test route
2. If it works, test with actual business logic (e.g., assign a comanda to a PM)
3. Monitor for any issues
4. Consider adding more notification types as needed

## Documentation

- Laravel Broadcasting: https://laravel.com/docs/12.x/broadcasting
- Laravel Reverb: https://laravel.com/docs/12.x/reverb
- Filament Notifications: https://filamentphp.com/docs/4.x/notifications/broadcast-notifications

