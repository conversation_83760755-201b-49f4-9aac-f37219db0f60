# ✅ Broadcast Notifications - Final Working Setup

## Status: WORKING! 🎉

Toast notifications now appear immediately when sent, and also persist in the database notifications bell icon.

## What Was Fixed

### 1. Nginx Configuration
Changed from:
```nginx
location /reverb/app {
    proxy_pass http://**********:8081;
}
```

To:
```nginx
location /reverb/ {
    proxy_http_version 1.1;
    resolver 127.0.0.11;
    proxy_set_header Host $http_host;
    proxy_set_header Scheme $scheme;
    proxy_set_header SERVER_PORT $server_port;
    proxy_set_header REMOTE_ADDR $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "Upgrade";
    
    proxy_pass http://**********:8081/;  # Trailing slash strips /reverb prefix
}
```

### 2. User Model
Added the required method for Laravel broadcast notifications:

**File:** `app/Models/User.php`
```php
/**
 * The channels the user receives notification broadcasts on.
 * This is required for Filament broadcast notifications to work.
 */
public function receivesBroadcastNotificationsOn(): string
{
    return 'App.Models.User.' . $this->id;
}
```

### 3. NotificationService
Updated to send BOTH database and broadcast notifications:

**File:** `app/Services/NotificationService.php`
```php
public static function send(
    User $recipient,
    string $title,
    string $body,
    string $icon = 'heroicon-o-bell',
    string $iconColor = 'info',
    array $actions = [],
    bool $persistent = false
): void {
    $notification = Notification::make()
        ->title($title)
        ->body($body)
        ->icon($icon)
        ->iconColor($iconColor)
        ->actions($actions);

    if ($persistent) {
        $notification->persistent();
    }

    // Send to database for persistence (shows in bell icon)
    $notification->sendToDatabase($recipient, isEventDispatched: true);
    
    // ALSO broadcast for real-time toast notification
    $notification->broadcast($recipient);
}
```

## How to Use

### Basic Notification
```php
use App\Services\NotificationService;

NotificationService::send(
    recipient: $user,
    title: 'Hello!',
    body: 'This is a test notification',
    icon: 'heroicon-o-bell',
    iconColor: 'success'
);
```

### Persistent Notification (won't auto-dismiss)
```php
NotificationService::send(
    recipient: $user,
    title: 'Important Alert',
    body: 'Please review this immediately',
    icon: 'heroicon-o-exclamation-triangle',
    iconColor: 'warning',
    persistent: true
);
```

### Notification with Actions
```php
use Filament\Actions\Action;

NotificationService::send(
    recipient: $pm,
    title: 'Comandă nouă asignată',
    body: "Ți-a fost asignată comanda #{$comanda->id}",
    icon: 'heroicon-o-clipboard-document-check',
    iconColor: 'success',
    actions: [
        Action::make('view')
            ->button()
            ->url(route('filament.app.resources.comandas.view', $comanda))
            ->markAsRead(),
        Action::make('dismiss')
            ->button()
            ->color('gray')
            ->close(),
    ],
    persistent: true
);
```

## Pre-built Notification Methods

The NotificationService includes several pre-built methods for common scenarios:

### Comanda Notifications
```php
// When a comanda is assigned to a PM
NotificationService::comandaAssignedToPM($comanda, $pm);

// When comanda status changes
NotificationService::comandaStatusChanged($comanda, $user, $oldStatus, $newStatus);
```

### Item/Activity Notifications
```php
// When an item is assigned to a specialist
NotificationService::itemAssignedToSpecialist($item, $specialist);

// When an activity is approved
NotificationService::activityApproved($activity, $specialist);

// When an activity is rejected
NotificationService::activityRejected($activity, $specialist, $reason);
```

### Deadline Notifications
```php
// When a deadline is approaching
NotificationService::deadlineApproaching($comanda, $user, $hoursRemaining);

// When a deadline is overdue
NotificationService::deadlineOverdue($comanda, $user);
```

### Bulk Notifications
```php
// Send to multiple users
NotificationService::sendToMultiple(
    recipients: [$user1, $user2, $user3],
    title: 'Team Update',
    body: 'Important announcement'
);

// Notify all PMs about a new comanda
NotificationService::notifyPMsAboutNewComanda($comanda);

// Notify all managers and superadmins
NotificationService::notifyManagers(
    title: 'Critical Issue',
    body: 'Requires immediate attention',
    icon: 'heroicon-o-exclamation-circle',
    iconColor: 'danger'
);
```

## Test Routes

Test routes are available at:
- `/test/notification/quick` - Quick test (redirects to /app)
- `/test/notification/both` - Test both database + broadcast
- `/test/notification/broadcast-only` - Test broadcast only
- `/test/notification/database-only` - Test database only
- `/test/notification/types` - Test all notification types
- `/test/notification/persistent` - Test persistent notification

## Configuration Files

### Environment Variables (.env)
```env
BROADCAST_CONNECTION=reverb

# Reverb Server (internal)
REVERB_APP_ID=355121
REVERB_APP_KEY=cky5hbmyipkciufhiyg0
REVERB_APP_SECRET=dkkows0bcoucxzo61zkj
REVERB_HOST=**********
REVERB_PORT=8081
REVERB_SCHEME=http

# Frontend (browser connections via nginx)
VITE_REVERB_APP_KEY="${REVERB_APP_KEY}"
VITE_REVERB_HOST="crm.concept-42.com"
VITE_REVERB_PORT=443
VITE_REVERB_SCHEME=https
VITE_REVERB_PATH="reverb"
```

### Filament Configuration (config/filament.php)
```php
'broadcasting' => [
    'echo' => [
        'broadcaster' => 'reverb',
        'key' => env('VITE_REVERB_APP_KEY'),
        'wsHost' => env('VITE_REVERB_HOST'),
        'wsPort' => env('VITE_REVERB_PORT', 80),
        'wssPort' => env('VITE_REVERB_PORT', 443),
        'forceTLS' => (env('VITE_REVERB_SCHEME', 'https') === 'https'),
        'enabledTransports' => ['ws', 'wss'],
        'authEndpoint' => '/broadcasting/auth',
        'disableStats' => true,
        'wsPath' => env('VITE_REVERB_PATH') ? '/' . env('VITE_REVERB_PATH') : '',
    ],
],
```

### Panel Configuration
All panels have database notifications enabled:

```php
public function panel(Panel $panel): Panel
{
    return $panel
        // ...
        ->databaseNotifications()
        ->databaseNotificationsPolling('30s');
}
```

## How It Works

1. **Notification is sent** via `NotificationService::send()`
2. **Saved to database** via `->sendToDatabase($recipient, isEventDispatched: true)`
   - Appears in bell icon
   - Persists across sessions
   - Can be marked as read/unread
3. **Broadcast via Reverb** via `->broadcast($recipient)`
   - Sent over WebSocket to user's private channel
   - Filament receives the broadcast
   - Toast notification appears immediately
4. **User sees both:**
   - Immediate toast notification (auto-dismisses after 5 seconds unless persistent)
   - Notification in bell icon (persists until deleted)

## Troubleshooting

### No toast appearing?
1. Check browser console for WebSocket errors
2. Verify Reverb is running: `supervisorctl status reverb`
3. Check nginx configuration is correct
4. Clear caches: `php artisan config:clear && php artisan route:clear`

### Notifications only in bell icon?
- Broadcasting is not working
- Check WebSocket connection in browser console
- Verify nginx is proxying `/reverb/` correctly

### Notifications only as toast?
- Database storage is not working
- Check `sendToDatabase()` is being called
- Verify notifications table exists

## Next Steps

Now that notifications are working, you can:

1. **Integrate into business logic** - Add notifications to observers, actions, jobs
2. **Customize notification types** - Create more specific notification methods
3. **Add notification preferences** - Let users choose which notifications they want
4. **Monitor usage** - Track which notifications are most useful
5. **Consider email/SMS** - For critical notifications when users are offline

## Documentation References

- Full setup guide: `docs/NOTIFICATIONS.md`
- NotificationService API: `docs/NOTIFICATIONS.md`
- Nginx configuration: `docs/NGINX_REVERB_CONFIG.md`
- Laravel Broadcasting: https://laravel.com/docs/12.x/broadcasting
- Filament Notifications: https://filamentphp.com/docs/4.x/notifications/broadcast-notifications

