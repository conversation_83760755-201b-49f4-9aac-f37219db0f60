# 🔔 Notification System Documentation

## Overview

The CRM uses Filament's native notification system with **database storage** and **live broadcast** via Laravel Reverb (WebSockets).

### Features
- ✅ **Live notifications** - Delivered instantly via WebSockets
- ✅ **Persistent inbox** - Stored in database, survives page navigation
- ✅ **Toast notifications** - Attention-grabbing popups (top-right)
- ✅ **Cumulative stacking** - Multiple notifications stack vertically
- ✅ **Read/Unread tracking** - Mark notifications as read/unread
- ✅ **Action buttons** - Add interactive buttons to notifications
- ✅ **Persistent mode** - Notifications that don't auto-dismiss

---

## Architecture

```
Event Occurs
    ↓
NotificationService::send()
    ↓
    ├─→ Database Storage (notifications table)
    │   └─→ User Inbox Modal (bell icon)
    │       ├─→ Mark as Read/Unread
    │       └─→ Delete
    │
    └─→ Broadcast via Reverb (WebSocket)
        └─→ Live Toast Notification (top-right)
            └─→ Auto-dismiss or Persistent
```

---

## Quick Start

### 1. Send a Simple Notification

```php
use App\Services\NotificationService;

NotificationService::send(
    recipient: $user,
    title: 'Hello!',
    body: 'This is a test notification',
    icon: 'heroicon-o-bell',
    iconColor: 'info'
);
```

### 2. Send a Persistent Notification with Actions

```php
NotificationService::send(
    recipient: $user,
    title: 'Important Alert',
    body: 'Please review this immediately',
    icon: 'heroicon-o-exclamation-triangle',
    iconColor: 'warning',
    actions: [
        Action::make('view')
            ->button()
            ->url(route('some.route'))
            ->markAsRead(),
        Action::make('dismiss')
            ->button()
            ->color('gray')
            ->close(),
    ],
    persistent: true  // Won't auto-dismiss
);
```

---

## NotificationService Methods

### Core Method

#### `send()`
Send a notification to a single user.

```php
NotificationService::send(
    recipient: User $user,
    title: string,
    body: string,
    icon: string = 'heroicon-o-bell',
    iconColor: string = 'info',  // info, success, warning, danger
    actions: array = [],
    persistent: bool = false
): void
```

### Business Logic Methods

#### `comandaAssignedToPM()`
Notify a PM when a comanda is assigned to them.

```php
NotificationService::comandaAssignedToPM($comanda, $pm);
```

#### `itemAssignedToSpecialist()`
Notify a specialist when a comanda item is assigned.

```php
NotificationService::itemAssignedToSpecialist($item, $specialist);
```

#### `activityApproved()`
Notify a specialist when their activity is approved.

```php
NotificationService::activityApproved($activity, $specialist);
```

#### `activityRejected()`
Notify a specialist when their activity is rejected.

```php
NotificationService::activityRejected($activity, $specialist, $reason);
```

#### `deadlineApproaching()`
Notify about an approaching deadline.

```php
NotificationService::deadlineApproaching($comanda, $user, $hoursRemaining);
```

#### `deadlineOverdue()`
Notify about an overdue deadline.

```php
NotificationService::deadlineOverdue($comanda, $user);
```

#### `comandaStatusChanged()`
Notify when a comanda status changes.

```php
NotificationService::comandaStatusChanged($comanda, $user, $oldStatus, $newStatus);
```

### Bulk Methods

#### `sendToMultiple()`
Send the same notification to multiple users.

```php
NotificationService::sendToMultiple(
    recipients: [$user1, $user2, $user3],
    title: 'Team Update',
    body: 'Important announcement for the team',
    icon: 'heroicon-o-megaphone',
    iconColor: 'info'
);
```

#### `notifyPMsAboutNewComanda()`
Notify all active PMs about a new comanda.

```php
NotificationService::notifyPMsAboutNewComanda($comanda);
```

#### `notifyManagers()`
Notify all managers and superadmins.

```php
NotificationService::notifyManagers(
    title: 'Critical Issue',
    body: 'Requires immediate attention',
    icon: 'heroicon-o-exclamation-circle',
    iconColor: 'danger'
);
```

---

## Icon Colors

Available colors:
- `info` - Blue
- `success` - Green
- `warning` - Yellow/Orange
- `danger` - Red
- `gray` - Gray

---

## Common Icons

```php
// General
'heroicon-o-bell'                    // Bell
'heroicon-o-megaphone'               // Announcement
'heroicon-o-information-circle'      // Info

// Success/Approval
'heroicon-o-check-circle'            // Check mark
'heroicon-o-clipboard-document-check' // Clipboard with check

// Warning/Alert
'heroicon-o-exclamation-triangle'    // Warning triangle
'heroicon-o-clock'                   // Clock (deadlines)

// Error/Rejection
'heroicon-o-x-circle'                // X mark
'heroicon-o-exclamation-circle'      // Exclamation

// Work/Tasks
'heroicon-o-wrench-screwdriver'      // Tools
'heroicon-o-document-plus'           // New document
'heroicon-o-arrow-path'              // Status change
```

[Full icon list](https://heroicons.com/)

---

## Adding Actions to Notifications

Actions are interactive buttons that appear in notifications.

### Basic Action

```php
use Filament\Actions\Action;

Action::make('view')
    ->button()
    ->url(route('filament.app.resources.comandas.view', $comanda))
```

### Action that Marks as Read

```php
Action::make('acknowledge')
    ->button()
    ->markAsRead()
    ->close()
```

### Action with Custom Logic

```php
Action::make('approve')
    ->button()
    ->color('success')
    ->action(function () {
        // Your custom logic here
    })
    ->markAsRead()
```

### Multiple Actions

```php
actions: [
    Action::make('view')
        ->button()
        ->url(route('some.route'))
        ->markAsRead(),
    Action::make('snooze')
        ->button()
        ->color('gray')
        ->action(fn () => /* snooze logic */),
    Action::make('dismiss')
        ->button()
        ->color('gray')
        ->close(),
]
```

---

## Testing

### Test Routes

Visit these routes while logged in:

1. **Basic test**: `/test-notifications`
   - Sends 2 test notifications (normal + persistent)

2. **Type test**: `/test-notification-types`
   - Sends 4 notifications (success, warning, danger, info)

### Manual Testing

```php
// In tinker or a controller
use App\Services\NotificationService;

$user = auth()->user();

NotificationService::send(
    recipient: $user,
    title: 'Test',
    body: 'Testing notifications',
    icon: 'heroicon-o-bell',
    iconColor: 'success'
);
```

---

## Integration Examples

### In a Resource Action

```php
use App\Services\NotificationService;

Action::make('assign')
    ->action(function (Comanda $record, array $data) {
        $pm = User::find($data['pm_id']);
        $record->update(['owner_id' => $pm->id]);
        
        // Send notification
        NotificationService::comandaAssignedToPM($record, $pm);
    })
```

### In an Observer

```php
namespace App\Observers;

use App\Models\ComandaItem;
use App\Services\NotificationService;

class ComandaItemObserver
{
    public function updated(ComandaItem $item): void
    {
        if ($item->isDirty('assigned_to') && $item->assigned_to) {
            $specialist = $item->assignedUser;
            NotificationService::itemAssignedToSpecialist($item, $specialist);
        }
    }
}
```

### In a Job/Command

```php
namespace App\Jobs;

use App\Models\Comanda;
use App\Services\NotificationService;

class CheckDeadlines
{
    public function handle(): void
    {
        $comandas = Comanda::whereNotNull('deadline')
            ->where('deadline', '>', now())
            ->where('deadline', '<', now()->addHours(24))
            ->get();

        foreach ($comandas as $comanda) {
            $hoursRemaining = now()->diffInHours($comanda->deadline);
            
            NotificationService::deadlineApproaching(
                $comanda,
                $comanda->owner,
                $hoursRemaining
            );
        }
    }
}
```

---

## User Interface

### Notification Bell Icon
- Located in the topbar (top-right)
- Shows unread count badge
- Click to open notifications modal

### Notifications Modal
- Shows all notifications (newest first)
- Unread notifications are highlighted
- "Mark all as read" button at top
- Individual delete buttons
- Action buttons (if defined)

### Toast Notifications
- Appear in top-right corner
- Stack vertically
- Auto-dismiss after 5 seconds (unless persistent)
- Can be manually closed

---

## Configuration

### Panel Configuration

All panels are configured in `app/Providers/Filament/*PanelProvider.php`:

```php
->databaseNotifications()           // Enable database notifications
->databaseNotificationsPolling('30s') // Poll every 30 seconds as fallback
```

### Polling Interval

Change polling frequency:
```php
->databaseNotificationsPolling('60s')  // Every minute
->databaseNotificationsPolling('10s')  // Every 10 seconds
->databaseNotificationsPolling(null)   // Disable polling (websockets only)
```

---

## Troubleshooting

### Notifications not appearing live?
1. Check Reverb is running: `supervisorctl status reverb`
2. Check browser console for WebSocket errors
3. Verify `BROADCAST_CONNECTION=reverb` in `.env`

### Notifications not saving to database?
1. Check migration ran: `php artisan migrate:status`
2. Verify User model has `Notifiable` trait
3. Check database connection

### Actions not working?
1. Ensure routes exist and are accessible
2. Check user has permission to access the route
3. Verify action syntax is correct

---

## Best Practices

1. **Use the NotificationService** - Don't create notifications manually
2. **Be specific** - Clear titles and descriptive bodies
3. **Choose appropriate icons** - Match the notification type
4. **Add actions when relevant** - Make notifications actionable
5. **Use persistent mode sparingly** - Only for critical alerts
6. **Test thoroughly** - Use test routes before deploying

---

## Future Enhancements

Potential additions:
- [ ] Browser push notifications (for offline users)
- [ ] Email notifications (for critical alerts)
- [ ] SMS notifications (via Twilio)
- [ ] Notification preferences per user
- [ ] Notification templates
- [ ] Scheduled notifications

