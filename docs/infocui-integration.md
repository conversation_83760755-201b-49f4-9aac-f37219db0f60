# Infocui Integration

This document describes the integration with the Infocui.ro API for automatic company data fetching in Romania.

## Overview

The `InfocuiService` fetches company information from the Infocui.ro API using a company's CUI (Cod Unic de Înregistrare - Romanian Tax ID).

## Configuration

Add to your `.env` file:
```env
INFOCUI_API_KEY=your_api_key_here
INFOCUI_BASE_URL=https://infocui.ro/system/api
```

The service is automatically configured in `config/services.php`.

## Usage

### Basic Usage - Fetch Company Data

```php
use App\Services\InfocuiService;

$service = new InfocuiService();
$companyData = $service->getCompanyInfo('********');

if ($companyData) {
    // Returns array with mapped Client model fields:
    // - company_name
    // - cui
    // - reg_com
    // - address
    // - email
    // - phone
    // - contact_person
    // - contact_email
    // - contact_phone
    // - bank_name
    // - bank_account
    // - notes
    // - is_active
}
```

### Populate Existing Client Model

```php
use App\Services\InfocuiService;
use App\Models\Client;

$client = Client::find(1);
$service = new InfocuiService();

// Only fills empty fields
$success = $service->populateClientModel($client, '********');
```

### Create New Client from CUI

```php
use App\Services\InfocuiService;

$service = new InfocuiService();
$client = $service->createClientFromCui('********');
```

### Check if Company Exists

```php
use App\Services\InfocuiService;

$service = new InfocuiService();
$exists = $service->companyExists('********');
```

## Field Mapping

The service maps Infocui.ro API fields to our Client model:

| Client Field | API Field(s) (with fallbacks) |
|-------------|-------------------------------|
| company_name | denumire |
| cui | cui |
| reg_com | nr_reg_com, nr_reg_comert |
| address | adresa, localitate, judet, cod_postal (concatenated) |
| email | email, adresa_email |
| phone | telefon, telefon_fix, telefon_mobil |
| contact_person | administrator_unic, administrator, reprezentant_legal, director |
| contact_email | email_contact |
| contact_phone | telefon_contact |
| bank_name | banca, nume_banca, denumire_banca |
| bank_account | iban, cont_bancar |
| notes | forma_juridica, cod_caen, data_inceput_activitate, stare_firma |

**Note**: Based on the actual API documentation from infocui.ro, the following fields are mapped correctly:
- `company_name` → `nume`
- `cui` → `cod_fiscal`
- `reg_com` → `cod_inmatriculare`
- `address` → `adresa` (or built from components)
- `phone` → `tel`
- `notes` → Additional metadata (stare_firma, TVA registration, fax, EUID, etc.)

Fields not available in the API and must be set manually:
- `email`
- `contact_person`
- `contact_email`
- `contact_phone`
- `bank_name`
- `bank_account`

## Error Handling

The service handles errors gracefully:
- Invalid CUI format returns `null`
- API errors are logged and return `null`
- Network timeouts (30s) are handled
- Empty responses are handled

All errors are logged to Laravel's log file with context.

## Testing

Unit tests are available in `tests/Unit/Services/InfocuiServiceTest.php`.

Run tests with:
```bash
php artisan test --filter InfocuiServiceTest
```

