# 🚀 Notification System - Implementation Guide

## ✅ What's Been Set Up

### 1. Database Table
- ✅ Notifications migration created
- ✅ Ready to run: `php artisan migrate`

### 2. Panel Configuration
All 4 panels configured with database notifications:
- ✅ `AdminPanelProvider`
- ✅ `AppPanelProvider`
- ✅ `DefinitionsPanelProvider`
- ✅ `ProductiePanelProvider`

Each panel has:
- Database notifications enabled
- 30-second polling as fallback
- WebSocket support via Reverb

### 3. NotificationService
Created `app/Services/NotificationService.php` with methods for:
- ✅ Generic notifications
- ✅ Comanda assignments
- ✅ Activity approvals/rejections
- ✅ Deadline alerts
- ✅ Status changes
- ✅ Bulk notifications

### 4. Test Routes
Created test routes for verification:
- `/test-notifications` - Basic test
- `/test-notification-types` - All notification types

### 5. Documentation
- ✅ `docs/NOTIFICATIONS.md` - Complete usage guide

---

## 🎯 Next Steps

### Step 1: Run the Migration

```bash
php artisan migrate
```

This creates the `notifications` table.

### Step 2: Test the System

1. **Log into the app** at `/app`
2. **Visit test route**: `/test-notifications`
3. **Check for**:
   - Toast notification appears (top-right)
   - Bell icon shows unread count
   - Click bell → notifications appear in modal

### Step 3: Verify WebSocket Connection

1. Open browser console (F12)
2. Look for WebSocket connection messages
3. Should see: `Echo connected to reverb`

If not connected:
```bash
# Check Reverb is running
supervisorctl status reverb

# Restart if needed
supervisorctl restart reverb
```

### Step 4: Integrate into Business Logic

Now you can add notifications to your existing code!

#### Example 1: When Assigning a Comanda

In your Comanda resource action:

```php
use App\Services\NotificationService;

Action::make('assign')
    ->form([
        Select::make('owner_id')
            ->label('Assign to PM')
            ->options(User::where('role', 'pm')->pluck('name', 'id'))
            ->required(),
    ])
    ->action(function (Comanda $record, array $data) {
        $pm = User::find($data['owner_id']);
        
        $record->update(['owner_id' => $pm->id]);
        
        // 🔔 Send notification
        NotificationService::comandaAssignedToPM($record, $pm);
        
        Notification::make()
            ->title('Comanda assigned successfully')
            ->success()
            ->send();
    })
```

#### Example 2: When Assigning a Comanda Item

In your ComandaItem resource or observer:

```php
use App\Services\NotificationService;

// In an observer
public function updated(ComandaItem $item): void
{
    if ($item->isDirty('assigned_to') && $item->assigned_to) {
        $specialist = User::find($item->assigned_to);
        
        // 🔔 Send notification
        NotificationService::itemAssignedToSpecialist($item, $specialist);
    }
}
```

#### Example 3: Deadline Monitoring

Create a scheduled command:

```bash
php artisan make:command CheckDeadlines
```

```php
namespace App\Console\Commands;

use App\Models\Comanda;
use App\Services\NotificationService;
use Illuminate\Console\Command;

class CheckDeadlines extends Command
{
    protected $signature = 'deadlines:check';
    protected $description = 'Check for approaching deadlines';

    public function handle()
    {
        // Find comandas with deadlines in next 24 hours
        $comandas = Comanda::whereNotNull('deadline')
            ->where('deadline', '>', now())
            ->where('deadline', '<', now()->addHours(24))
            ->whereDoesntHave('notifications', function ($query) {
                $query->where('created_at', '>', now()->subHours(24))
                      ->where('data->title', 'like', '%Deadline apropiat%');
            })
            ->get();

        foreach ($comandas as $comanda) {
            $hoursRemaining = now()->diffInHours($comanda->deadline);
            
            // 🔔 Notify owner
            NotificationService::deadlineApproaching(
                $comanda,
                $comanda->owner,
                $hoursRemaining
            );
        }

        $this->info("Checked {$comandas->count()} comandas");
    }
}
```

Add to `app/Console/Kernel.php`:

```php
protected function schedule(Schedule $schedule)
{
    $schedule->command('deadlines:check')->hourly();
}
```

---

## 🎨 Customization Examples

### Custom Notification

```php
use Filament\Notifications\Notification;
use Filament\Notifications\Actions\Action;

Notification::make()
    ->title('Custom Event')
    ->body('Something important happened')
    ->icon('heroicon-o-sparkles')
    ->iconColor('success')
    ->actions([
        Action::make('view')
            ->button()
            ->url(route('some.route'))
            ->markAsRead(),
    ])
    ->persistent()
    ->sendToDatabase($user, isEventDispatched: true)
    ->broadcast($user);
```

### Notification to Multiple Users

```php
$specialists = User::where('role', 'specialist')->get();

NotificationService::sendToMultiple(
    recipients: $specialists->all(),
    title: 'Team Meeting',
    body: 'Team meeting tomorrow at 10 AM',
    icon: 'heroicon-o-calendar',
    iconColor: 'info'
);
```

### Conditional Notifications

```php
// Only notify if user has preference enabled
if ($user->preferences['notify_on_assignment'] ?? true) {
    NotificationService::itemAssignedToSpecialist($item, $user);
}
```

---

## 🔍 Monitoring & Debugging

### Check Notification Count

```php
// In tinker
$user = User::first();
$user->notifications()->count();
$user->unreadNotifications()->count();
```

### View Recent Notifications

```php
$user->notifications()->latest()->take(5)->get();
```

### Clear All Notifications

```php
$user->notifications()->delete();
```

### Mark All as Read

```php
$user->unreadNotifications->markAsRead();
```

---

## 📊 Database Queries

### Find Users with Unread Notifications

```sql
SELECT users.*, COUNT(notifications.id) as unread_count
FROM users
LEFT JOIN notifications ON notifications.notifiable_id = users.id
WHERE notifications.read_at IS NULL
GROUP BY users.id
HAVING unread_count > 0;
```

### Most Active Notification Recipients

```sql
SELECT users.name, COUNT(notifications.id) as notification_count
FROM users
LEFT JOIN notifications ON notifications.notifiable_id = users.id
GROUP BY users.id, users.name
ORDER BY notification_count DESC
LIMIT 10;
```

---

## 🚨 Common Issues & Solutions

### Issue: Notifications not appearing live

**Solution:**
1. Check Reverb is running: `supervisorctl status reverb`
2. Check browser console for WebSocket errors
3. Verify `.env` has `BROADCAST_CONNECTION=reverb`
4. Clear config cache: `php artisan config:clear`

### Issue: Notifications saved but not showing in UI

**Solution:**
1. Hard refresh browser (Ctrl+Shift+R)
2. Check user has `Notifiable` trait
3. Verify panel has `->databaseNotifications()` enabled

### Issue: Actions not working

**Solution:**
1. Check route exists: `php artisan route:list | grep <route-name>`
2. Verify user has permission to access route
3. Check action syntax matches documentation

### Issue: Too many notifications

**Solution:**
1. Add throttling to prevent spam
2. Batch similar notifications
3. Add user preferences for notification types

---

## 📈 Performance Considerations

### Optimize Queries

```php
// Bad - N+1 query
foreach ($users as $user) {
    NotificationService::send($user, ...);
}

// Good - Batch notifications
NotificationService::sendToMultiple($users->all(), ...);
```

### Queue Notifications

For bulk operations, queue notifications:

```php
dispatch(function () use ($users, $title, $body) {
    NotificationService::sendToMultiple($users, $title, $body);
})->afterResponse();
```

### Cleanup Old Notifications

Add to scheduler:

```php
// Delete read notifications older than 30 days
$schedule->call(function () {
    DB::table('notifications')
        ->whereNotNull('read_at')
        ->where('created_at', '<', now()->subDays(30))
        ->delete();
})->daily();
```

---

## 🎓 Learning Resources

- [Filament Notifications Docs](https://filamentphp.com/docs/4.x/notifications)
- [Laravel Broadcasting Docs](https://laravel.com/docs/11.x/broadcasting)
- [Laravel Reverb Docs](https://laravel.com/docs/11.x/reverb)
- [Heroicons](https://heroicons.com/)

---

## ✨ Quick Reference

### Send Notification
```php
NotificationService::send($user, 'Title', 'Body');
```

### With Actions
```php
NotificationService::send($user, 'Title', 'Body', actions: [
    Action::make('view')->button()->url(...)->markAsRead()
]);
```

### Persistent
```php
NotificationService::send($user, 'Title', 'Body', persistent: true);
```

### Multiple Users
```php
NotificationService::sendToMultiple([$user1, $user2], 'Title', 'Body');
```

---

## 🎉 You're Ready!

The notification system is fully set up and ready to use. Start by:

1. ✅ Running the migration
2. ✅ Testing with the test routes
3. ✅ Adding notifications to your business logic
4. ✅ Monitoring and iterating

Happy notifying! 🔔

