# 🚀 Quick Fix for Broadcast Notifications

## The Problem
Notifications appear in the bell icon but NOT as toast notifications.

## The Solution (2 steps)

### 1️⃣ Fix Nginx Configuration

Edit your nginx config file and change:

**FROM:**
```nginx
location /reverb/app {
    proxy_pass http://**********:8081;
}
```

**TO:**
```nginx
location /reverb/ {
    proxy_http_version 1.1;
    resolver 127.0.0.11;
    proxy_set_header Host $http_host;
    proxy_set_header Scheme $scheme;
    proxy_set_header SERVER_PORT $server_port;
    proxy_set_header REMOTE_ADDR $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "Upgrade";
    
    # Trailing slash strips the /reverb prefix
    proxy_pass http://**********:8081/;
}
```

### 2️⃣ Reload Nginx

```bash
nginx -t && systemctl reload nginx
```

OR if in Docker:
```bash
docker exec <nginx-container> nginx -s reload
```

## Test It

Visit: https://crm.concept-42.com/test/notification/quick

You should see a toast notification appear immediately! 🎉

---

## What Changed in the Code (Already Done)

✅ Added `receivesBroadcastNotificationsOn()` to User model  
✅ Updated NotificationService to call `->broadcast($recipient)`  
✅ Fixed Filament Echo configuration  
✅ Created test routes  

**You only need to fix nginx!**

---

## Why This Works

- **Before:** Nginx sent `/reverb/app/...` to Reverb (wrong path)
- **After:** Nginx strips `/reverb` and sends `/app/...` to Reverb (correct path)

The trailing slash in `proxy_pass http://**********:8081/;` tells nginx to strip the location prefix.

---

See `docs/BROADCAST_NOTIFICATIONS_SETUP.md` for full details.

