<?php

namespace Tests\Unit\Services;

use App\Services\InfocuiService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class InfocuiServiceTest extends TestCase
{
    /**
     * Test that the service correctly validates CUI format
     */
    public function test_validates_cui_format(): void
    {
        $service = new InfocuiService();

        // Valid CUI formats
        $this->assertNotNull($service->getCompanyInfo('12345678'));
        $this->assertNotNull($service->getCompanyInfo('1234567890123'));

        // Invalid formats should return null
        $this->assertNull($service->getCompanyInfo(''));
        $this->assertNull($service->getCompanyInfo('abc'));
    }

    /**
     * Test successful API response mapping
     */
    public function test_maps_api_response_to_client_data(): void
    {
        Http::fake([
            'infocui.ro/*' => Http::response([
                'denumire' => 'Test Company SRL',
                'cui' => '12345678',
                'nr_reg_com' => 'J40/1234/2020',
                'adresa' => 'Strada Test 123',
                'localitate' => 'București',
                'judet' => 'București',
                'cod_postal' => '010101',
                'email' => '<EMAIL>',
                'telefon' => '0212345678',
                'administrator_unic' => 'John Doe',
                'forma_juridica' => 'SRL',
                'cod_caen' => '1234',
            ], 200),
        ]);

        $service = new InfocuiService();
        $result = $service->getCompanyInfo('12345678');

        $this->assertNotNull($result);
        $this->assertEquals('Test Company SRL', $result['company_name']);
        $this->assertEquals('12345678', $result['cui']);
        $this->assertEquals('J40/1234/2020', $result['reg_com']);
        $this->assertStringContainsString('Strada Test 123', $result['address']);
        $this->assertStringContainsString('București', $result['address']);
        $this->assertEquals('<EMAIL>', $result['email']);
        $this->assertEquals('0212345678', $result['phone']);
        $this->assertEquals('John Doe', $result['contact_person']);
    }

    /**
     * Test handling of API errors
     */
    public function test_handles_api_errors_gracefully(): void
    {
        Http::fake([
            'infocui.ro/*' => Http::response([], 404),
        ]);

        $service = new InfocuiService();
        $result = $service->getCompanyInfo('12345678');

        $this->assertNull($result);
    }

    /**
     * Test company exists check
     */
    public function test_company_exists_check(): void
    {
        Http::fake([
            'infocui.ro/*' => Http::response([
                'denumire' => 'Test Company SRL',
                'cui' => '12345678',
            ], 200),
        ]);

        $service = new InfocuiService();
        $this->assertTrue($service->companyExists('12345678'));
    }
}

