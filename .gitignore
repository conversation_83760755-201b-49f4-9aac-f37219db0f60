# Laravel
/.phpunit.cache
/node_modules
/public/build
/public/hot
/public/storage
/storage/*.key
/storage/pail
/vendor
.env
.env.backup
.env.production
.phpactor.json
.phpunit.result.cache
Homestead.json
Homestead.yaml
npm-debug.log
yarn-error.log
/auth.json
/.fleet
/.idea
/.nova
/.vscode
/.zed
/.vagrant

# <PERSON><PERSON> legacy paths
bootstrap/compiled.php
app/storage/
public_html/storage
public_html/hot

# Docker & Development
docker-compose.yml
docker-compose.override.yml
.docker/
supervisord.log
supervisord.pid

# Temporary files
public/phpinfo.php
*.tmp
*.log

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
